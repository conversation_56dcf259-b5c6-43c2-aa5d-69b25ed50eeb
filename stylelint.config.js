module.exports = {
  defaultSeverity: 'error',
  customSyntax: 'postcss-scss',
  extends: ['@tencent/stylelint-config-tencent', 'stylelint-config-idiomatic-order'],
  plugins: ['stylelint-scss', 'stylelint-order'],
  rules: {
    'declaration-block-semicolon-space-before': 'never', // 分号前不允许空格
    'block-opening-brace-newline-after': 'always', // 大括号后面需要换行且没有空格
    'declaration-block-semicolon-newline-after': 'always', // 分号后面需要换行
    'block-closing-brace-newline-before': 'always', // 结尾大括号之前需要换行，分号后无空格
    'declaration-no-important': [true, { severity: 'warning' }],
    'selector-max-id': 1,
  },
};
