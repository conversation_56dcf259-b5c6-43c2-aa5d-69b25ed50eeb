import { message } from '@tencent/tea-component';

// 文字复制到剪切板
// 文字复制到剪切板
export const copyTextToClipboard = (text: string) => {
  // 使用 Clipboard API
  navigator.clipboard
    .writeText(text)
    .then(() => {
      message.success({ content: '复制成功' });
    })
    .catch(() => {
      message.error({ content: '复制失败' });
    });
};

export const getPopupContainer = (): HTMLElement => document.querySelector(
  '#micro-frontend-root div[data-qiankun="isa-cloud-arch"]',
) || document.body;

// 去重，同一类型的搜索项只保留最后一个
export const deduplicateSearchItems = (items: any[]) => {
  const uniqueValue = [];
  const seenKeys = new Set();

  // 从后往前遍历，确保保留最新的项
  for (let i = items.length - 1; i >= 0; i--) {
    const item = items[i];
    const key = item?.attr?.key;

    if (!seenKeys.has(key)) {
      seenKeys.add(key);
      uniqueValue.unshift(item);
    }
  }

  return uniqueValue;
};

// 处理搜索框值变化，如果没有选中筛选条件，默认为InsId过滤条件
export const processSearchBoxValues = (values: any[]) => {
  // 检查是否有无效的搜索项（没有选择搜索类型）
  const invalidItems = values?.filter((x: any) => !x?.attr?.key);

  if (invalidItems?.length > 0) {
    // 处理无效项，将其默认设置为实例ID搜索
    const processedValue = values?.map((item: any) => {
      if (!item?.attr?.key) {
        return {
          ...item,
          attr: {
            key: 'InsId',
            name: '实例ID',
          },
        };
      }
      return item;
    });

    // 去重处理
    return deduplicateSearchItems(processedValue);
  }

  return values;
};
