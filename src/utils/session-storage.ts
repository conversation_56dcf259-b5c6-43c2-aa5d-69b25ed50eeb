/* eslint-disable no-console */
// 是否为浏览器环境
const IS_NODE = typeof window === 'undefined';

const CacheModal: {
  [key: string]: any | null;
} = {};

// 全局cache对象
const CacheController = {
  setItem: (key: string, value: any) => {
    CacheModal[key] = value;
  },
  getItem: (key: string) => CacheModal[key],
  removeItem: (key: string) => {
    CacheModal[key] = null;
    delete CacheModal[key];
  },
  clear: () => {
    Object.keys(CacheModal).forEach((key) => delete CacheModal[key]);
  },
};

const StorageReposity = IS_NODE ? CacheController : window.sessionStorage;

/**
 * 带有expires的本地缓存工具方法
 * 缓存api默认为：nodejs—— CacheController，browser—— sessionStorage对象
 * 缓存有效期默认：12小时
 * @param key
 * @returns
 */
export const getLocal = (key: string): any => {
  let result;
  try {
    const info = StorageReposity.getItem(key) ?? '';
    const obj = info ? JSON.parse(info) : undefined;
    if (obj === undefined) {
      return obj;
    }
    const current = Date.now();
    if (obj.expires < 0 || current - obj.startTime < obj.expires) {
      result = obj.data;
    }
  } catch (e) {
    console.error(e);
  }
  return result;
};

export const clearLocal = (cb?: any): void => {
  try {
    StorageReposity.clear();
    cb?.({ res: true });
  } catch (e) {
    cb?.({ res: false, error: e });
  }
};

export function removeLocal(type: string): void {
  StorageReposity.removeItem(type);
}

/**
 *
 * @param key
 * @param value
 * @param expires 有效期，设置为负数时则在session有效期内永久有效
 */
export const saveLocal = (
  key: string,
  value: string,
  expires: number = 8 * 60 * 60 * 1000,
): void => {
  try {
    const val = JSON.stringify({
      data: value,
      expires,
      startTime: Date.now(),
    });
    StorageReposity.setItem(key, val);
  } catch (e) {
    console.error('error', e);
  }
};

/**
 * 获取存储在storage里的Map对象的key-value
 * @param key
 * @param objKey
 * @returns
 */
export const getMapStorage = (key: string, objKey?: string): any => {
  if (!key) {
    console.error('getMapStorage 参数错误，无法取值');
    return undefined;
  }
  let result;
  try {
    const mapStr = StorageReposity.getItem(key) ?? '';
    const map = mapStr ? JSON.parse(mapStr) : undefined;
    if (map === undefined || !objKey) {
      return map;
    }
    const current = Date.now();
    const objValue = map[objKey];
    if (
      objValue
      && (objValue.expires < 0 || current - objValue.startTime < objValue.expires)
    ) {
      result = objValue.data;
    }
  } catch (e) {
    console.error(e);
  }
  return result;
};

/**
 * 存储value到Map对象中, 其数据结构为key-value对，其中每一对key-value数据结构为：
 * {
 *  key: string
 *  value: {
 *   [objKey]: {
 *      data: any,
 *      expires: number,
 *      startTime: number
 *    }
 *  }
 * }
 * @param key
 * @param objKey
 * @param value
 * @param expires
 */
export const saveMapStorage = (
  key: string,
  objKey: string,
  value: string,
  expires: number = 8 * 60 * 60 * 1000,
): void => {
  try {
    const map = getMapStorage(key) ?? {};
    map[objKey] = {
      data: value,
      expires,
      startTime: Date.now(),
    };
    const mapStr = JSON.stringify(map);
    StorageReposity.setItem(key, mapStr);
  } catch (e) {
    console.error('error', e);
  }
};

export default {
  clearLocal,
  removeLocal,
  saveLocal,
  getLocal,
};
