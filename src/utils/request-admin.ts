import axiosFactory, { AxiosRequestConfig, Method } from 'axios';
import { cloneDeep } from 'lodash';
import { notification, message } from '@tencent/tea-component';
import { getMapStorage, saveMapStorage } from './session-storage';
import {
  CachedType,
  CacheOption,
  IRequestOptions,
  RequestMethodEnum,
} from './request.type';
import { copyTextToClipboard } from './common-funcs';

const REQUEST_STORAGE_KEY = 'CLOUD_ARCH_API_STORAGE';

const rejectError = (msg: string) => {
  message.error({ content: msg });
  return Promise.reject(msg);
};

// 使用独立的axios实例，防止实例污染
export const axios = axiosFactory.create();

axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

const responseInterceptors: Parameters<typeof axios.interceptors.response.use> = [
  (response) => {
    if (response.data?.Response?.Error) {
      const { Code: title, Message: description } = response?.data?.Response?.Error ?? {};
      const { data } = response?.config ?? {};
      try {
        const parseData = JSON.parse(data);
        // 资源绑定接口错误特殊处理
        if (title !== 'ResourceNotFound') {
          notification.error({
            title: parseData?.Action,
            extra: title,
            description,
            footer: '复制requestId',
            onFooterClick: () => {
              if (response?.data?.Response?.RequestId) {
                copyTextToClipboard(response?.data?.Response?.RequestId);
              }
            },
            duration: 5000,
          });
        }
      } catch (e) {
        console.error(e.message);
      }
    }
    return response;
  },
  // eslint-disable-next-line complexity
  (error) => {
    const showError = error?.config?.headers?.showError ?? true;
    if (!showError) {
      return false;
    }
    if (error?.response) {
      switch (error?.response?.status) {
        case 401: {
          return rejectError('未登录或登录态已过期，点击“确定”马上刷新页面重新登录，点击“取消”稍后手动刷新');
        }
        case 403: {
          const err = '暂无权限';
          return rejectError(err);
        }
        default: {
          return rejectError('请求失败');
        }
      }
    } else if (error?.request || error?.message) {
      return rejectError(
        `request = ${error?.request}, message = ${error?.message}`,
      );
    }
    return rejectError(error?.config);
  },
];

axios.interceptors.response.use(...responseInterceptors);

/**
 * 设置请求拦截器
 * @param interceptors
 * @returns
 */
export function setRequestInterceptors(
  interceptors: Parameters<typeof axios.interceptors.request.use>,
): number {
  return axios.interceptors.request.use(...interceptors);
}

/**
 * 设置响应拦截器
 * @param interceptors
 * @returns
 */
export function setResponseInterceptors(
  interceptors: Parameters<typeof axios.interceptors.response.use>,
): number {
  return axios.interceptors.response.use(...interceptors);
}

/**
 * 移除请求拦截器
 * @param interceptorKey
 */
export function ejectRequestInterceptors(interceptorKey: number): void {
  axios.interceptors.request.eject(interceptorKey);
}

/**
 * 移除响应拦截器
 * @param interceptorKey
 */
export function ejectResponseInterceptors(interceptorKey: number): void {
  axios.interceptors.response.eject(interceptorKey);
}

export function get(
  url: string,
  cacheOption: CacheOption = {
    enable: false,
  },
): Promise<any> {
  return requestWithCache(url, { method: RequestMethodEnum.get }, cacheOption)
    .then((res) => Promise.resolve(res))
    .catch((e) => {
      // eslint-disable-next-line
      console.error(e);
      return Promise.reject(e);
    });
}

export function deleteFun(
  url: string,
  data?: any,
  cacheOption: CacheOption = {
    enable: false,
  },
): Promise<any> {
  return requestWithCache(
    url,
    { method: RequestMethodEnum.delete, data },
    cacheOption,
  )
    .then((res) => Promise.resolve(res))
    .catch((e) => {
      // eslint-disable-next-line
      console.error(e);
      return Promise.reject(e);
    });
}

export function blobGet(
  url: string,
  cacheOption: CacheOption = {
    enable: false,
  },
): Promise<any> {
  return requestWithCache(
    url,
    { method: RequestMethodEnum.blobGet },
    cacheOption,
  )
    .then((res) => Promise.resolve(res))
    .catch((e) => {
      // eslint-disable-next-line
      console.error(e);
      return Promise.reject(e);
    });
}

export function post(
  url: string,
  data?: any,
  cacheOption: CacheOption = {
    enable: false,
  },
  headers: object = {},
): Promise<any> {
  return requestWithCache(
    url,
    { method: RequestMethodEnum.post, data },
    cacheOption,
    headers,
  )
    .then((res) => Promise.resolve(res))
    .catch((e) => {
      // eslint-disable-next-line
      console.error(e);
      return Promise.reject(e);
    });
}

export function put(
  url: string,
  data: any,
  cacheOption: CacheOption = {
    enable: false,
  },
): Promise<any> {
  return requestWithCache(
    url,
    { method: RequestMethodEnum.put, data },
    cacheOption,
  )
    .then((res) => Promise.resolve(res))
    .catch((e) => {
      // eslint-disable-next-line
      console.error(e);
      return Promise.reject(e);
    });
}

export function postForm(
  url: string,
  data: any,
  cacheOption: CacheOption = {
    enable: false,
  },
): Promise<any> {
  return requestWithCache(
    url,
    { method: RequestMethodEnum.postForm, data },
    cacheOption,
  )
    .then((res) => Promise.resolve(res))
    .catch((e) => {
      // eslint-disable-next-line
      console.error(e);
      return Promise.reject(e);
    });
}

export function blobPost(
  url: string,
  data: any,
  cacheOption: CacheOption = {
    enable: false,
  },
): Promise<any> {
  return requestWithCache(
    url,
    { method: RequestMethodEnum.blobPost, data },
    cacheOption,
  )
    .then((res) => Promise.resolve(res))
    .catch((e) => {
      // eslint-disable-next-line
      console.error(e);
      return Promise.reject(e);
    });
}

const cachedRequests: CachedType = {};

function cleanCache(hash: string) {
  return (res?: Response) => {
    delete cachedRequests[hash];
    return res;
  };
}

const getAxiosConfig = (options: IRequestOptions, url: string) => {
  const config: AxiosRequestConfig = {
    method: options.method
      .replace(/blob|[f|F]orm/g, '')
      .toLowerCase() as Method,
    url,
  };
  const { headers } = options;
  if (
    [RequestMethodEnum.get, RequestMethodEnum.blobGet].includes(options.method)
  ) {
    config.headers = {
      ...headers,
      'X-Requested-With': 'XMLHttpRequest',
    };
  }
  if ([RequestMethodEnum.delete].includes(options.method)) {
    config.headers = {
      ...headers,
      'X-Requested-With': 'XMLHttpRequest',
    };
  }
  // @hack-跟后端约定delete方法从 body 中取值
  if (
    [
      RequestMethodEnum.post,
      RequestMethodEnum.blobPost,
      RequestMethodEnum.put,
      RequestMethodEnum.delete,
    ].includes(options.method)
  ) {
    config.headers = {
      ...headers,
      'Content-Type': 'application/json; charset=utf-8',
      'X-Requested-With': 'XMLHttpRequest',
    };
    if (options.data) {
      config.data = cloneDeep(options.data);
    }
  }
  if (
    [RequestMethodEnum.blobGet, RequestMethodEnum.blobPost].includes(
      options.method,
    )
  ) {
    config.responseType = 'blob';
  }
  if (options.method === RequestMethodEnum.postForm) {
    config.headers = {
      ...headers,
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    config.data = options?.data;
  }
  return config;
};

/**
 * 带有请求缓存队列的request方法
 * 机制：当请求未返回而新的同样的请求又发送过来时，会根据请求类型进行cache
 * 根据RESTful规范制定缓存策略
 * 对于patch，put，delete请求：直接返回pending状态的Promise对象
 * 对于get，post请求：返回sessionStorage或CacheController中的缓存数据
 * @param url
 * @param options
 * @param silent
 * @param cacheOption - 缓存配置
 *  enable：boolean 是否启用缓存
 *  expires：number 缓存有效期
 * @returns
 */
function requestWithCache(
  url: string,
  options: IRequestOptions,
  cacheOption: CacheOption = {
    enable: false,
    expires: 12 * 60 * 60 * 1000,
  },
  headers: object = {},
): Promise<any> {
  const hash = `${options.method} ${url} ${JSON.stringify(options.data ?? {})}`;
  if (cacheOption?.enable) {
    const cachedRequest = cachedRequests[hash];
    if (
      cachedRequest instanceof Promise
      && [
        RequestMethodEnum.get,
        RequestMethodEnum.post,
        RequestMethodEnum.postForm,
      ].includes(options.method)
    ) {
      return cachedRequest
        .then((res) => {
          if (
            res?.data?.Code === 0
            && cacheOption?.expires
            && cacheOption?.expires > 0
          ) {
            saveMapStorage(
              REQUEST_STORAGE_KEY,
              hash,
              res.data,
              cacheOption.expires,
            );
          }
          return res.data;
        })
        .catch(() => {});
    }
    const cache = getMapStorage(REQUEST_STORAGE_KEY, hash);
    // get/post cache逻辑
    if (cache) {
      return Promise.resolve(cache);
    }
  }
  const config = getAxiosConfig(options, url);
  config.headers = { ...config.headers, ...headers };
  cachedRequests[hash] = axios(config);
  return cachedRequests[hash]
    .then((res: any) => {
      // 缓存结果
      if (
        cacheOption.enable
        && [
          RequestMethodEnum.get,
          RequestMethodEnum.post,
          RequestMethodEnum.postForm,
        ].includes(options.method)
        && res
        && cacheOption?.expires
        && cacheOption?.expires > 0
      ) {
        saveMapStorage(
          REQUEST_STORAGE_KEY,
          hash,
          res.data,
          cacheOption?.expires,
        );
      }
      return res?.data;
    })
    .finally(() => cleanCache(hash)());
}
