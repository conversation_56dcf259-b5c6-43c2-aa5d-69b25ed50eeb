/* eslint-disable max-len */
/* eslint-disable prefer-regex-literals */
// eslint-disable-next-line import/prefer-default-export

import Aegis from 'aegis-web-sdk';
import { lng, t } from '@tea/app/i18n';
import { app } from '@tea/app';
import { pluginAPI } from '@src/utils/caching';
import moment from 'moment';
import { EnvEnum } from '@src/constant';
import { ReportFields } from './index.type';

export const aegis = new Aegis({
  id: 'dWxjwtv1RPLpr97n3Q', // 项目上报id
  reportApiSpeed: false, // 接口测速
  reportAssetSpeed: true, // 静态资源测速
  spa: true, // spa 页面需要开启，页面切换的时候上报pv
  beforeRequest: () => { },
  modifyRequest(options) {
    // TODO: 发起请求前调用，做上报参数处理
    return options;
  },
  afterRequest() {
    // TODO: 埋点请求完，做失败处理逻辑
  },
});

export const sleep = (time: number) => new Promise((resolve) => {
  setTimeout(resolve, time);
});

export const getAppid = () => {
  // 获取url参数中的appid
  const urlParams = new URLSearchParams(window.location.search);
  const appid = urlParams.get('appid');
  return Number(appid);
};

export function getLanguageParam() {
  return lng === 'zh' ? 'zh-CN' : 'en-US';
}

export const clickReport = async (name) => {
  if (pluginAPI.env === 'ISA') {
    aegis.reportEvent({
      name: 'click',
      ext1: `isa;${name};0;0;${pluginAPI.username}`,
      ext2: '0',
      ext3: '0',
    });
  } else {
    const { loginUin, ownerUin } = app.user;
    const userCurrent = await app.user.current();
    aegis.reportEvent({
      name: 'click',
      ext1: `tsa;${name};${ownerUin};${loginUin};${userCurrent.displayName}`,
      ext2: `${ownerUin}`,
      ext3: `${loginUin}`,
    });
  }
};

export function toUTCtime(timestamp = Date.now(), format = 'yyyy-MM-dd HH:mm:ss') {
  if (isNaN(timestamp)) {
    return '';
  }

  if (format.length < 4 || 'yyyy-MM-dd HH:mm:ss'.indexOf(format) !== 0) {
    return '';
  }

  // let end = Math.round(new Date() / 1000)
  const date = new Date(Number(timestamp * 1000));

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  return format
    .replace('yyyy', year.toString())
    .replace('MM', month > 9 ? month.toString() : `0${month}`)
    .replace('dd', day > 9 ? day.toString() : `0${day}`)
    .replace('HH', hour > 9 ? hour.toString() : `0${hour}`)
    .replace('mm', minute > 9 ? minute.toString() : `0${minute}`)
    .replace('ss', second > 9 ? second.toString() : `0${second}`);
}

// <div
// className="info-desc"
// dangerouslySetInnerHTML={{ __html: simpleMarkdownToHTML(strategy.Repair) }}
// ></div>
// 把建议中的markdown形式的超链接转换为html里的target为_blank的a标签
export const simpleMarkdownToHTML = (input: string): string => {
  /* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
  const reg = new RegExp(
    '(\\[[\u4e00-\u9fa5_a-zA-Z0-9,、/-\\s]+\\])(\\((\\s?https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]\\))',
    'g',
  );
  /* eslint-enable @tencent/tea-i18n/no-bare-zh-in-js */
  let result = input?.replace(reg, (match, text, link) => `<a href="${link.slice(1, -1)}" target="_blank" style="vertical-align: baseline;">${text.slice(1, -1)}</a>`);

  // 无序列表markdown语法转换
  if (result?.includes('- ')) {
    result = result
      .split('- ')
      .filter((r) => r.length)
      .map((r, index) => (!index ? `<div>• ${r}</div>` : `<div style="margin-top: 8px;">• ${r}</div>`))
      .join('');
  }
  return result;
};

export const getCurrTimeToStr = () => moment(Date.now()).format('YY-MM-DD HH:mm:ss');

/**
 * 埋点上报
 * @param platform 平台
 * @param fields 上报参数
 * @description
 */
export const reportEvent = async (platform: EnvEnum, fields: ReportFields, event = 'scanPluginYeHeData') => {
  const {
    subUin, subUinName, ...otherFields
  } = fields;
  if (!event || !platform) {
    return;
  }
  try {
    if (platform === EnvEnum.CONSOLE) {
      const user = await app.user.current();
      const { appId, loginUin, displayName } = user;
      const data = {
        actionTime: getCurrTimeToStr(),
        subUin: loginUin?.toString(),
        subUinName: displayName,
        appId: appId?.toString(),
        ...otherFields,
      };
      // console.log('埋点上报:', data);
      app.insight.stat({
        ns: 'cloudInspectionSdk',
        event,
        stringFields: data,
        integerFields: {
          time: Date.now(),
        },
      });
    } else if (platform === EnvEnum.ISA) {
      // // 上报到isa;
      // const data = {
      //   actionTime: getCurrTimeToStr(),
      //   appId: getAppid().toString(),
      //   ...fields,
      // };
      // console.log('isa:', data);
    }
    // 上报完成后删除from参数，防止复制链接出去上报数据不准
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.delete('from');
    window.history.replaceState(null, '', currentUrl.toString());
  } catch (e) {
    console.error(t('参数不合法：'), e);
  }
};

/**
 * 获取url上指定参数
 * @param paramName
 * @returns
 */
export const getUrlParam = (paramName: string) => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(paramName);
};

/**
 * 删除url上指定参数
 * @param paramName
 * @returns
 */
export const deleteUrlParam = (paramName: string) => {
  const currentUrl = new URL(location.href);
  currentUrl.searchParams.delete(paramName);
  window.history.replaceState(null, '', currentUrl.toString());
};
/**
 * 分割字符串中第一个连字符('-')，返回分割后的两部分
 * @param str 待分割的字符串，默认为空字符串
 * @returns 包含两个元素的元组，第一个是连字符前的部分，第二个是连字符后的部分（若无连字符则返回空字符串）
 */
export function splitFirstHyphen(str = ''): [string, string] {
  const index = str?.indexOf?.('-');
  if (index === -1) return [str, ''];
  return [str.slice(0, index), str.slice(index + 1)];
}

// 导出通用函数
export {
  copyTextToClipboard, getPopupContainer, deduplicateSearchItems, processSearchBoxValues,
} from './common-funcs';
