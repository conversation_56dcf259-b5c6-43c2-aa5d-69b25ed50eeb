import { AxiosRequestConfig } from 'axios';

export interface CacheOption {
  enable: boolean;
  expires?: number;
}

export interface CachedType {
  [key: string]: Promise<any>;
}

interface ErrorType {
  RequestId: string;
  Error?: {
    Code: number;
    Message: string;
  };
}
export interface ResponseResult<T> {
  Response: T & ErrorType;
}

export enum RequestMethodEnum {
  get = 'get',
  post = 'post',
  postForm = 'postForm',
  blobGet = 'blobGet',
  blobPost = 'blobPost',
  put = 'put',
  delete = 'delete'
}

export interface IRequestOptions extends AxiosRequestConfig {
  method: RequestMethodEnum;
  data?: any;
}
