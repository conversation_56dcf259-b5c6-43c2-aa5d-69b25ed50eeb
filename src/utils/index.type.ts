/**
 * 事件名称类型
 */
export type EventName =
  | ''
  | 'clickNodeEvent'
  | 'clickGenerateNodeReport'
  | 'clickNodeScan'
  | 'clickHandleProgressScan'
  | 'clickRiskProgressInstanceTab'
  | 'clickAtRiskTab'
  | 'clickClaimedTab'
  | 'clickIgnoredTab'
  | 'clickNoRiskTab'
  | 'riskClickInstanceIgnore'
  | 'riskClickInstanceClaimed'
  | 'riskClickBatchInstanceClaimed'
  | 'riskClickBatchInstanceIgnore'
  | 'claimedClickInstanceIgnore'
  | 'claimedClickBatchInstanceIgnore'
  | 'ignoredClickUnignore'
  | 'riskClickExpandInstanceRisk'
  | 'riskClickIgnoreRisk'
  | 'riskClickAICustomerService'
  | 'claimedClickExpandInstanceRisk'
  | 'claimedClickIgnoreRisk'
  | 'claimedClickAICustomerService'
  | 'clickFindRisk'
  | 'clickNodeReportRisk'
  | 'clickSubmitRiskSearch'
  | 'searchExpandRiskItem'
  | 'searchClickInstanceIgnore'
  | 'searchClickUnignore'
  | 'searchClickAICustomerService'
  | 'topBarClickHighRisk'
  | 'topBarClickMediumRisk'
  | 'topBarClickAllRisk'
  | 'clickRiskSearchCreateScan'
  | 'topBarClickOnlyHighRisk'
  | 'clickRiskAiAnalyzeFromEmail';

/**
 * 上报参数类型
 */
export interface ReportFields {
  subUin: string; // 子账号
  subUinName: string; // 子账号名称
  eventType: EventName; // 事件类型
  archId?: string; // ArchID
  nodeId?: string; // 节点 ID
  nodeName?: string; // 节点名称
  instanceId?: string; // 实例 ID
  strategyId?: string; // 风险 ID
  searchCondition?: string; // 搜索条件
  // remarks?: string; // 备注
  // appId: string; // APPID  -  reportEvent中统一获取
  // actionTime?: string; // 动作时间-时间戳  -  reportEvent中统一获取
  [x: string]: string;
}
