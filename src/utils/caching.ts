/* eslint-disable @typescript-eslint/naming-convention */
// eslint-disable-next-line no-unused-vars, import/no-mutable-exports
export let pluginAPI: any = {};
// eslint-disable-next-line import/no-mutable-exports
export let inspectionTaskIsStop = false; // 巡检任务是否是主动停止
// eslint-disable-next-line import/no-mutable-exports
export let initTaskIsStop = false; // 是否手动停止初始化偏移检测任务
// eslint-disable-next-line import/no-mutable-exports
export let initScanReportTime = null;
// eslint-disable-next-line import/no-mutable-exports
export let chatBiCallBacks = null;

// eslint-disable-next-line import/prefer-default-export
export const setPluginAPI = (value) => {
  pluginAPI = value;
};

export const setInitScanReportTime = (value) => {
  initScanReportTime = value;
};

export const setInspectionTaskIsStop = (value) => {
  inspectionTaskIsStop = value;
};

export const setInitTaskIsStop = (value) => {
  initTaskIsStop = value;
};

export const setChatBiCallBacks = (value) => {
  chatBiCallBacks = value;
};
