import { app } from '@tea/app';
import { set } from 'lodash';
import { RequestV3Options } from '@tea/app/bridge/capi';
import { API_SERVICE, API_SERVICE_VERSION, COMMON_NO_AUTH_CODES } from '@src/constant/api';
import { t } from '@tea/app/i18n';

// 默认设为广州
const defaultRegionId = 1;

interface ServiceParamsProps {
  apiService?: string; // 默认为 advisor，调用其他产品的接口时需要传入产品名称
  regionId?: number;
  apiServiceVersion?: string;
  data?: { [propsName: string]: unknown };
}
export interface ResultCommonType<T> {
  code: number;
  cgwerrorCode: number;
  errObj: unknown;
  mccode: number;
  reqId: string;
  resId: string;
  msg: string;
  data: {
    Response: T & IErrorInfo;
  };
}

export interface IErrorInfo {
  Error: {
    Code: string;
    Message: string;
  }
}

export interface ResultType<T> {
  code: number;
  cgwerrorCode: number;
  errObj: unknown;
  mccode: number;
  reqId: string;
  resId: string;
  msg: string;
  data: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Response: T;
  };
}
// 唤起拦截对话框
export async function authrity(err) {
  const cam = await app.sdk.use('cam-sdk');
  const { data } = err;

  // 通过 cam 调用 API
  cam.showBreakModal({
    message: data?.message,
  });
  return err;
}

/**
 * 生成service请求函数，对于advisor项目的通用请求，已配置默认的API_SERVICE和版本参数
 * @param cmd 请求参数名称
 * @param apiService 默认值: advisor
 * @param apiServiceVersion 默认值: 2020-07-21
 * @param options 请求配置
 * @param authTip cam鉴权不通过是否自动提弹窗提示，默认是
 * @returns
 */
export const createServiceFunc = (
  cmd: string,
  apiService?: string,
  apiServiceVersion?: string,
  options?: RequestV3Options,
  authTip = true,
) => {
  const isShowTipErr = options?.tipErr === undefined ? true : options?.tipErr;
  // options = { ...options, tipErr: false, tipLoading: false };
  set(options, 'tipErr', false);
  set(options, 'tipLoading', false);
  return (async (params: ServiceParamsProps = {}) => {
    const { regionId, data } = params;
    const recentRegionIdStr = String(defaultRegionId);
    const isValidRecentRegionIdStr = /^\d+$/.test(recentRegionIdStr);
    return app.capi.requestV3({
      cmd,
      serviceType: apiService || API_SERVICE,
      regionId: regionId
      ?? (isValidRecentRegionIdStr ? parseInt(recentRegionIdStr, 10) : defaultRegionId),
      data: {
        Version: apiServiceVersion || API_SERVICE_VERSION,
        ...(data || {}),
      },
    }, options).catch((err) => {
      if (COMMON_NO_AUTH_CODES.includes(err?.code)) {
        if (authTip) return authrity(err);
        return err;
      } if (isShowTipErr) {
        if (apiService === 'tke' && cmd === 'ForwardPlatformRequestV3') {
          app.tips.error(t('获取集群信息失败，请联系集群管理员查看集群。报错信息{{attr0}}', { attr0: err?.msg }));
        } else {
          app.tips.error(err?.msg);
        }
        return err;
      }
      return err;
    });
  });
};
