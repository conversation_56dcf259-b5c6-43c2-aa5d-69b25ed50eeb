import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';

export interface HandleProgressDrawerState {
  handleProgressDrawerVisible?: boolean,
}

const initialState: HandleProgressDrawerState = {
  handleProgressDrawerVisible: true,
};

export const handleProgressDrawerState = createSlice({
  name: 'handleProgressDrawerState',
  initialState,
  reducers: {
    changeHandleProgressDrawerState: (state, action: PayloadAction<HandleProgressDrawerState>) => {
      Object.assign(state, action.payload);
    },
    resetHandleProgressDrawerState: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const {
  changeHandleProgressDrawerState,
  resetHandleProgressDrawerState,
} = handleProgressDrawerState.actions;

export const useHandleProgressDrawerStateSelector:
  () => HandleProgressDrawerState = () => useSelector((state: any) => state.handleProgressDrawerState);

export default handleProgressDrawerState.reducer;
