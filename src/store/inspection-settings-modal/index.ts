import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';

export interface InspectionsSettingsModalState {
  products?: string[];
  inspectionSettingsModalVisible?: boolean,
}

const initialState: InspectionsSettingsModalState = {
  products: [],
  inspectionSettingsModalVisible: false,
};

export const inspectionsSettingsModalState = createSlice({
  name: 'inspectionsSettingsModalState',
  initialState,
  reducers: {
    changeInspectionsSettingsModalState: (state, action: PayloadAction<InspectionsSettingsModalState>) => {
      Object.assign(state, action.payload);
    },
    resetInspectionsSettingsModalState: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const {
  changeInspectionsSettingsModalState,
  resetInspectionsSettingsModalState,
} = inspectionsSettingsModalState.actions;

export const useInspectionsSettingsModalStateSelector:
() => InspectionsSettingsModalState = () => useSelector((state: any) => state.inspectionsSettingsModalState);

export default inspectionsSettingsModalState.reducer;
