import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';

export interface NodeRiskDrawerState {
  riskTableFilter?: any;
  claimedTabelFilter?: any;
  ignoredTableFilter?: any;
  noRiskTableFilter?: any;
  riskTablePage?: number;
  claimedTabelPage?: number;
  noRiskTablePage?: number;
  ignoredTablePage?: number;
  riskNodeCount?: any;
}

const initialState: NodeRiskDrawerState = {
  riskTableFilter: [],
  claimedTabelFilter: [],
  ignoredTableFilter: [],
  noRiskTableFilter: [],
  riskTablePage: 1,
  claimedTabelPage: 1,
  noRiskTablePage: 1,
  ignoredTablePage: 1,
  riskNodeCount: {},
};

export const nodeRiskDrawerState = createSlice({
  name: 'nodeRiskDrawerState',
  initialState,
  reducers: {
    changeNodeRiskDrawerState: (state, action: PayloadAction<NodeRiskDrawerState>) => {
      Object.assign(state, action.payload);
    },
    resetNodeRiskDrawerState: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const { changeNodeRiskDrawerState, resetNodeRiskDrawerState } = nodeRiskDrawerState.actions;

export const useNodeRiskDrawerStateSelector:
() => NodeRiskDrawerState = () => useSelector((state: any) => state.nodeRiskDrawerState);

export default nodeRiskDrawerState.reducer;
