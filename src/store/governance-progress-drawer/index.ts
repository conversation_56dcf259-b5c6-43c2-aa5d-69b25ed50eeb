import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';
import { TabEnum } from '@src/components/inspection-drawer';
import { RiskProgressEnum } from '@src/constant/index';

export interface GovernanceProgressDrawerState {
  riskTableFilter?: any;
  riskTablePage?: number;
  visible?: boolean;
  progressTab?: TabEnum;
  activeTabId?: RiskProgressEnum;
  customTopicSearchValue?: any[];
  customTopicTabHash?: string;
  secondaryDrawer?: any;
  safeCenterFilter?: any;
  safeCenterPage?: number;
  safeCenterPositionUuids?: string[];
}

const initialState: GovernanceProgressDrawerState = {
  riskTableFilter: [],
  riskTablePage: 1,
  visible: false,
  progressTab: TabEnum.left,
  activeTabId: RiskProgressEnum.instance,
  customTopicSearchValue: [],
  secondaryDrawer: {
    visible: false,
    defaultId: undefined,
    defaultTopicTitle: undefined,
  },
  safeCenterFilter: [],
  safeCenterPage: 1,
  safeCenterPositionUuids: [],
};

export const governanceProgressDrawerState = createSlice({
  name: 'governanceProgressDrawerState',
  initialState,
  reducers: {
    changeGovernanceProgressDrawerState: (state, action: PayloadAction<GovernanceProgressDrawerState>) => {
      Object.assign(state, action.payload);
    },
    resetGovernanceProgressDrawerState: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const {
  changeGovernanceProgressDrawerState,
  resetGovernanceProgressDrawerState,
} = governanceProgressDrawerState.actions;

export const useGovernanceProgressDrawerStateSelector:
() => GovernanceProgressDrawerState = () => useSelector((state: any) => state.governanceProgressDrawerState);

export default governanceProgressDrawerState.reducer;
