import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';

export interface ReportState {
  archId?: string;
  visible?: boolean;
  resultId?: string;
  downloadInfo?: Record<string, any>;
  scanFail?: boolean;
  generating?: boolean;
  scanReportBubbleVisible?: boolean;
  isFinished?: boolean;
  scanReportTime?: string | null | number;
  finishTime?: string;
  todayInspected?: number;
  progressBubbleVisible?: boolean;
  progressNum?: number | null;
  isInspecting?: boolean;
  taskTimer?: any;
  isLoading?: boolean;
  reportBtnConfig?: any;
  initScanReportTime?: string;
  // 节点报告相关信息
  nodeReportState?: {
    visible?: boolean;
  }
}

const initialState: ReportState = {
  archId: '',
  visible: false,
  resultId: '',
  downloadInfo: {},
  scanFail: false,
  generating: false,
  scanReportBubbleVisible: false,
  isFinished: false,
  scanReportTime: null,
  finishTime: '',
  todayInspected: 0,
  progressBubbleVisible: false,
  progressNum: null,
  isInspecting: false,
  taskTimer: null,
  isLoading: false,
  reportBtnConfig: {},
  initScanReportTime: null,
  // 节点报告相关信息
  nodeReportState: {
    visible: false,
  },
};

export const reportState = createSlice({
  name: 'reportState',
  initialState,
  reducers: {
    changeReportData: (state, action: PayloadAction<ReportState>) => {
      Object.assign(state, action.payload);
    },
    resetReportData: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const { changeReportData, resetReportData } = reportState.actions;

export const useReportSelector: () => ReportState = () => useSelector((state: any) => state.reportState);

export default reportState.reducer;
