import { configureStore } from '@reduxjs/toolkit';
import governanceProgressDrawerReducer from './governance-progress-drawer';
import globalStateReducer from './global';
import reportStateReducer from './report';
import riskPanelStateReducer from './risk-search-panel';
import nodeRiskDrawerStateReducer from './node-risk-drawer';
import inspectionsSettingsModalStateReducer from './inspection-settings-modal';

export const store = configureStore({
  reducer: {
    globalState: globalStateReducer,
    reportState: reportStateReducer,
    riskPanelState: riskPanelStateReducer,
    nodeRiskDrawerState: nodeRiskDrawerStateReducer,
    inspectionsSettingsModalState: inspectionsSettingsModalStateReducer,
    governanceProgressDrawerState: governanceProgressDrawerReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;
