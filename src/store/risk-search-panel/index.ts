import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';

export interface RiskPanelState {
  activeCollapseId?: string;
  visible?: boolean;
  strategyTaskItems?: {StrategyId: number, TaskId: string}[] | null;
  refreshInstanceListHash?: string; // 刷新实例列表的hash值 控制实例列表数据的刷新
  positionUuids?: string[]; // 定位的节点uuid
  searchBoxValue?: null | any[];
  ignoreSelectedKeys?: string[];
  riskSelectedKeys?: string[];
}

const initialState: RiskPanelState = {
  activeCollapseId: '',
  visible: false,
  strategyTaskItems: null,
  refreshInstanceListHash: '',
  positionUuids: [],
  searchBoxValue: [],
  ignoreSelectedKeys: [],
  riskSelectedKeys: [],
};

export const riskPanelState = createSlice({
  name: 'riskPanelState',
  initialState,
  reducers: {
    changeRiskPanelData: (state, action: PayloadAction<RiskPanelState>) => {
      Object.assign(state, action.payload);
    },
    resetRiskPanelData: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const { changeRiskPanelData, resetRiskPanelData } = riskPanelState.actions;

export const useRiskPanelSelector: () => RiskPanelState = () => useSelector((state: any) => state.riskPanelState);

export default riskPanelState.reducer;
