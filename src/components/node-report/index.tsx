/* eslint-disable react/no-array-index-key */
/* eslint-disable max-len */
import React, { useState, useMemo, useEffect } from 'react';
import {
  Modal,
  Card,
  Table,
  Button,
} from '@tencent/tea-component';
import { Env } from '@src/constant';
import { store } from '@src/store';
import { useGlobalSelector } from '@src/store/global/index';
import { t } from '@tea/app/i18n';
import { changeReportData, useReportSelector } from '@src/store/report/index';
import { StatusTip } from '@tencent/tea-component/lib/tips';
import Report01 from '@src/assets/svg-component/report01.svg';
import Report02 from '@src/assets/svg-component/report02.svg';
import Report03 from '@src/assets/svg-component/report03.svg';
import Report04 from '@src/assets/svg-component/report04.svg';
import { describeArchScanNodeReportResult, exportArchScanNodeReportResult } from '@src/service/export-service/final-request';
// import s from './index.module.scss';

import './index.less';
// import { clickReport } from '@src/utils';

const { dispatch } = store;

export interface Props {
  archId?: string;
  visible?: boolean;
  resultId?: string;
  downloadInfo?: any;
  onClose?: () => void;
  createArchScanReportFile?: () => Promise<any>;
  pluginAPI: AppPluginAPI.PluginAPI;
}

export interface ScanReportContentProps {
  bubbleVisible?: boolean;
  changeVisible?: any;
}

// eslint-disable-next-line no-empty-pattern
const Report = React.forwardRef(({
  // createArchScanReportFile,
  // pluginAPI,
// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
}: Props, ref: any) => {
  const { pluginPropsData, selectNodeInfo, archScanRiskInfo } = useGlobalSelector();
  const { nodeReportState } = useReportSelector();
  const {
    visible,
  } = nodeReportState;
  const { archInfo } = pluginPropsData;
  const { archId } = archInfo;
  const { scrollable } = Table.addons;
  const [records, setRecords] = useState([]);
  const [overviewInfo, setOverviewInfo] = useState<any>({});
  const [downloading, setDownloading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');

  const columns = [
    {
      key: 'InstanceId',
      header: t('实例ID'),
      render: (item) => <div>{item.InstanceId}</div>,
    },
    {
      key: 'StrategyName',
      header: t('风险项名称'),
    },
    {
      key: 'RiskLevelName',
      header: t('等级'),
      render: (item) => <div>{item.RiskLevelName}</div>,
    },
    {
      key: 'RiskTypeName',
      header: t('类别'),
      width: 70,
    },
    {
      key: 'AlertConditions',
      header: t('告警条件'),
      render: (item) => (
        <div title={item?.AlertConditions?.join('\n')} className="repair-line-wrap">
          {item?.AlertConditions?.map((item, index) => (
            <div key={`condition-${index}`}>{item}</div>
          ))}
        </div>
      ),
    },
  ];

  useMemo(() => {
    if (visible) {
      const { NodeRiskItems: nodeRiskItems } = archScanRiskInfo;
      const item = nodeRiskItems.find((item) => item.NodeUuid === selectNodeInfo.key);
      if (item) {
        describeArchScanNodeReportResult({
          uin: pluginPropsData.uin,
          env: pluginPropsData.env as Env,
          data: {
            MapId: archId,
            NodeUuid: selectNodeInfo.key,
            TaskId: item.LastSuccessTaskId,
          },
        }).then((res) => {
          if (res) {
            setOverviewInfo(res);
            setRecords(res.RiskInstanceList);
          }
        });
      }
    } else {
      setOverviewInfo({});
      setRecords([]);
    }
  }, [visible]);

  const getSvgStr = (str) => {
    if (!str) {
      return;
    }
    let newStr = str;
    if (newStr.indexOf('\'') === 0) {
      newStr = newStr.slice(1);
    }
    if (newStr.lastIndexOf('\'') === newStr.length - 1) {
      newStr = newStr.slice(0, newStr.length - 1);
    }
    // eslint-disable-next-line consistent-return
    return newStr;
  };
  const downloadFile = () => {
    setDownloading(true);
    const { NodeRiskItems: nodeRiskItems } = archScanRiskInfo;
    const item = nodeRiskItems.find((item) => item.NodeUuid === selectNodeInfo.key);
    if (item) {
      exportArchScanNodeReportResult({
        uin: pluginPropsData.uin,
        env: pluginPropsData.env as Env,
        data: {
          MapId: archId,
          NodeUuid: selectNodeInfo.key,
          TaskId: item.LastSuccessTaskId,
        },
      }).then((res) => {
        setDownloading(false);
        if (res?.FileUrl) {
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          document.body.appendChild(iframe);
          iframe.src = res.FileUrl;
          setTimeout(() => {
            iframe.remove();
          }, 1000);
        }
      });
    }
  };
  // const getShowText = (str) => (str === undefined ? '-' : str);

  const captureCenteredSvgElement = (targetElement, width, height) => {
    // 1. 获取目标元素的位置信息
    const bbox = targetElement?.getBBox?.();

    // 2. 计算居中坐标
    const centerX = bbox.x + bbox.width / 2;
    const centerY = bbox.y + bbox.height / 2;

    // 3. 创建临时SVG副本
    const tempSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    tempSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
    tempSvg.innerHTML = `
      <style>
        svg {
          overflow: auto !important;
        }
      </style>
      ${getSvgStr(document.getElementById('sigma-container').children[0].innerHTML)}
    `;
    tempSvg.style.overflow = 'auto';
    // 4. 设置裁剪视窗
    tempSvg.setAttribute(
      'viewBox',
      `${centerX - width / 2} ${centerY - height / 2} ${width} ${height}`,
    );

    // 5. 生成图片数据
    const svgData = new XMLSerializer().serializeToString(tempSvg);
    const base64Svg = svgToBase64(svgData); // 使用修复后的编码函数
    // eslint-disable-next-line consistent-return
    return `data:image/svg+xml;base64,${base64Svg}`;
  };

  // 将 SVG 转换为 Base64 编码
  const svgToBase64 = (svg) => {
    // 使用 encodeURIComponent 对 SVG 字符串进行编码
    const encodedSvg = encodeURIComponent(svg)
      .replace(/%([0-9A-F]{2})/g, (match, p1) => String.fromCharCode((`0x${p1}`) as unknown as number));
      // 使用 btoa 对编码后的字符串进行 Base64 编码
    return btoa(encodedSvg);
  };

  useEffect(() => {
    if (selectNodeInfo?.key) {
      const targetNode = document.getElementById(selectNodeInfo.key);
      if (targetNode) {
        const screenshotData = captureCenteredSvgElement(targetNode, 580, 440);
        setImageUrl(screenshotData);
      }
    }
  }, [selectNodeInfo]);
  return (
    <Modal
      visible={visible}
      className={`inspect-report-modal-real inspect-report-modal-real-node ${
        pluginPropsData.env === 'ISA' ? 'inspect-report-modal-real-isa' : ''
      }`}
      caption={
        <div className="head-btn-wrap" />
      }
      onClose={() => {
        dispatch(changeReportData({ nodeReportState: { ...nodeReportState, visible: false } }));
      }}
    >
      <Modal.Body>
        <div className="con-wrap">
          <div className="t-area" style={{ zIndex: 0 }}>
            <div className="t-area-text">
              <div className="report-t">
                <Report04 />
                <div>
                  {/* {t('云顾问-云巡检架构评估报告-{{time}}', {
                    time: overviewInfo.ReportDate || '-',
                  })} */}
                  {overviewInfo.ReportName || '-'}
                </div>
              </div>
              <div className="report-t-en">
                Tencent Cloud Smart Advisor Architecture Evaluation Report
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ flex: 1 }}>
                  {overviewInfo.CustomerName ? <div className="line" style={{ marginTop: 22 }}>
                    <div className="line-t">{t('名称：')}</div>
                    <div className="line-desc">
                      {overviewInfo.CustomerName || '-'}
                    </div>
                  </div> : null}
                  <div className="line">
                    <div className="line-t">{t('架构图名称：')}</div>
                    <div className="line-desc">{overviewInfo.MapName || '-'}</div>
                  </div>
                  <div className="line">
                    <div className="line-t">{t('架构图ID：')}</div>
                    <div className="line-desc">{overviewInfo.MapId || '-'}</div>
                  </div>
                  <div className="line">
                    <div className="line-t">APPID：</div>
                    <div className="line-desc">{overviewInfo.AppId || '-'}</div>
                  </div>
                  <div className="line">
                    <div className="line-t">{t('评估时间：')}</div>
                    <div className="line-desc">
                      {overviewInfo.ScanAt || '-'}
                    </div>
                  </div>
                </div>
                <div style={{
                  width: 290, height: 220, display: 'flex', alignItems: 'center', justifyContent: 'center', padding: 10,
                }}
                >
                  { imageUrl && <img src={imageUrl} style={{ width: '100%' }} alt="架构图" />}
                </div>
              </div>

            </div>
            <div className="t-area-bg">
              <Report01 />
              <div>
                <Report02 />
              </div>
            </div>
          </div>
          <div style={{ marginTop: 20 }} />
          <Card>
            <Card.Body>
              <div className="card-t">
                <>架构图云巡检明细清单</>
              </div>
              <Table
                className="inspect-see-report-tab"
                bordered
                records={records || []}
                columns={columns || []}
                addons={[
                  scrollable({
                    maxHeight: 500,
                  }),
                ]}
                topTip={records?.length === 0 && <StatusTip status="empty" />}
              />
            </Card.Body>
          </Card>
          <div className="download-wrap">
            <div>{t('更多巡检明细，请点击 ')}</div>
            <Button
              // loading={downloading}
              disabled={downloading}
              onClick={() => {
                // todo：下载节点报告埋点
                // clickReport(
                //   `download-excel;${new Date().getTime()};${resultId}`,
                // );
                downloadFile();
              }}
            >
              <Report03 />
              {t('下载详细数据')}
            </Button>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
});
Report.displayName = 'Report';
export default Report;
