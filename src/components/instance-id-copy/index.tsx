/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import { Icon, Copy } from '@tencent/tea-component';
import s from './index.module.scss';

interface IInstanceIdCopyProps {
  instanceId: string;
  url: string;
}

export default function InstanceIdCopy(props: IInstanceIdCopyProps): React.ReactElement {
  const {
    instanceId,
    url,
  } = props;
  return (
    <div
      className={s['instance-id-copy-container']}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <a
        className={s['instance-a']}
        type="link"
        onClick={() => {
          window.open(url);
        }}
        title={instanceId}
      >
        {instanceId}
      </a>
      <Copy
        text={instanceId}
      >
        <Icon className={s['instance-copy-icon']} type="copy" />
      </Copy>
    </div>
  );
}
