import React from 'react';
// import { app } from '@tencent/tea-app';
import { useGlobalSelector } from '@src/store/global/index';
import kefu from '@src/assets/svg/kefu.svg';
import { EnvEnum } from '@src/constant';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';

interface IProps {
  onClick?: (helpSdk?: any) => void;
  buttonText?: string;
  strategyText: string;
}

/**
 * 客服组件
 * @param {string} buttonText - 按钮文案属性
 * @param {Function} onClick - 点击事件-预留其他操作如埋点等
 * @returns React.ReactElement
 */
export default function CustomerService(props: IProps): React.ReactElement {
  const { pluginPropsData } = useGlobalSelector();
  const { env } = pluginPropsData;
  const { onClick = () => {}, buttonText = '智能客服解读', strategyText = '' } = props;
  const onQuestion = () => {
    // app.sdk.use('online-service-sdk').then((helpSdk) => {
    //   onClick(helpSdk);
    //   helpSdk.open(t('{{msg}}对客户业务有什么影响？', {
    //     msg: `${strategyText}`,
    //   }));
    // });
    onClick();
    try {
      const sdk = (window as any).seajs.require('sdk');
      sdk.use('online-service-sdk').then((res) => {
        res.open(t('{{msg}}对客户业务有什么影响？', {
          msg: `${strategyText}`,
        }));
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  if (env === EnvEnum.ISA) {
    return null;
  }
  return (
    <div className={s.wrapper} onClick={onQuestion}>
      <img style={{ width: 14, height: 14 }} src={kefu} alt="客服" />
      <span className={buttonText ? s.text : ''}>{buttonText}</span>
    </div>
  );
}
