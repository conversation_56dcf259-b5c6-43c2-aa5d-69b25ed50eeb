.inspection-drawer-title {
  display: flex;
  width: 0;
  height: 100%;
  flex: 1;

  .left-tab-title {
    display: flex;
    height: 100%;
    align-items: center;
    padding-right: 20px;
    padding-left: 20px;
    background-color: #fff;
    color: #000;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    
    .left-tab-title-inner {
      overflow: hidden;
      align-items: center;
      text-align: left;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .right-tab-title {
    display: flex;
    align-items: center;
    padding-left: 30px;
    cursor: pointer;

    .right-tab-icon {
      :global {
        rect {
          fill: transparent;
        }
      }
    }
  }
}

.left-tab-content,
.right-tab-content {
  width: 100%;
  height: 100%;
}


.inspection-drawer {
  background-image: url(https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/7edba92f-d260-4095-b658-2fecf07ae167.png);
  background-position: 0px 0px;
  background-repeat: no-repeat;
  background-size: 100% 300px;

  :global {
    .sdk-cloud-inspection-drawer__header {
      padding: 0;

      .sdk-cloud-inspection-justify-grid {
        height: 100%;

        .sdk-cloud-inspection-justify-grid__col--left {
          display: none;
        }

        .sdk-cloud-inspection-justify-grid__col--right {
          display: flex;
          width: 100%;
          height: 100%;
        }

        .sdk-cloud-inspection-btn {
          width: 24px;
          height: 24px;
          box-sizing: border-box;
          margin-right: 20px;
        }

        .sdk-cloud-inspection-btn:hover {
          background-color: rgba(0, 0, 0, .05);
        }
      }
    }

    .sdk-cloud-inspection-drawer__body {
      .sdk-cloud-inspection-drawer__body-inner {
        height: 100%;
      }
    }
  }
}

.inspection-drawer-left {
  :global {
    .sdk-cloud-inspection-drawer__body {
      background-color: #fff;
    }
  }
}

.inspection-drawer-right {
  :global {
    .sdk-cloud-inspection-drawer__header {
      border-bottom: 1px solid transparent;
    }
  }
}

.inspection-drawer-fixed-name {
  &:after {
    display: none;
  }

  :global .chat-ui .chat-ui-ft {
    padding: 0 0 20px 0;
  }

  :global .sdk-cloud-inspection-drawer__body {
    padding-bottom: 0;
  }

  :global .sdk-cloud-inspection-tabs__tabpanel {
    padding-bottom: 0;
  }

  :global .sdk-cloud-inspection-tabs__tabpanel .sdk-cloud-inspection-pagination {
    padding-bottom: 0;
  }
}