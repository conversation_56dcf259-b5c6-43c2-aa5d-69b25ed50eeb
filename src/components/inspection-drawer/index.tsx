import React from 'react';
import KefuSvg from '@src/assets/svg-component/kefu.svg';
import { Button } from '@tencent/tea-component';
import { useAppContext } from '@src/context/AppContext';
import classNames from 'classnames';
import { useDispatch } from 'react-redux';
import { changeGlobalData } from '@src/store/global/index';
import DraggableDrawer from '../draggable-drawer';
import s from './index.module.scss';

export enum TabEnum {
  left = 'left',
  right = 'right'
}

interface IInspectionDrawerProps {
  pluginAPI: AppPluginAPI.PluginAPI;
  drawerVisible: boolean;
  title: string;
  children?: React.ReactElement;
  nowTab: TabEnum;
  footer?: React.ReactNode;
  otherProps?: any;
  minSize?: number;
  maxSize?: number;
  onClose: () => void;
  onTabChange?: (tab: TabEnum) => void;
}

/**
 * 巡检插件专用抽屉
 * @returns
 */
export default function InspectionDrawer(props: IInspectionDrawerProps): React.ReactElement {
  const {
    drawerVisible,
    title = '治理进展',
    children,
    nowTab,
    footer,
    otherProps,
    minSize = 640,
    maxSize = 800,
    onClose,
    onTabChange,
  } = props;
  const dispatch = useDispatch();

  const { chatBiCallBacks } = useAppContext();

  const onWidthChange = (width: number) => {
    dispatch(changeGlobalData({
      inspectionDrawerWidth: width < minSize ? minSize : width,
    }));
  };

  return (
    <DraggableDrawer
      onWidthChange={onWidthChange}
      className={classNames(
        [s['inspection-drawer']],
        { [s['inspection-drawer-left']]: nowTab === TabEnum.left },
        { [s['inspection-drawer-right']]: nowTab === TabEnum.right },
        s['inspection-drawer-fixed-name'],
      )}
      drawerVisible={drawerVisible}
      extra={{
        extra: (
          <div className={s['inspection-drawer-title']}>
            <div
              title={title}
              style={{
                width: nowTab === TabEnum.left ? 'calc(100% - 160px)' : 160,
                clipPath:
                nowTab === TabEnum.right ? 'polygon(0 0, 100% 0, calc(100% - 24px) 100%, 0 100%)'
                  : 'polygon(0 0, calc(100% - 24px) 0, 100% 100%, 0 100%)',
              }}
              className={s['left-tab-title']}
              onClick={() => {
                onTabChange?.(TabEnum.left);
              }}
            >
              <div
                style={{ width: nowTab === TabEnum.right ? 115 : '100%' }}
                className={s['left-tab-title-inner']}
              >
                {title}
              </div>
            </div>
            <div
              style={{
                width: nowTab === TabEnum.left ? 160 : 'calc(100% - 160px)',
              }}
              className={s['right-tab-title']}
              onClick={() => {
                onTabChange?.(TabEnum.right);
              }}
            >
              <KefuSvg
                // @ts-ignore
                className={s['right-tab-icon']}
              />
              <Button type="link" style={{ marginLeft: 5, fontSize: 14 }}>风险分析</Button>
            </div>
          </div>
        ),
        ...otherProps ?? {},
      }}
      minSize={minSize}
      maxSize={maxSize}
      footer={footer}
      closeHandler={() => {
        onClose();
      }}
    >
      <div
        style={{
          display: nowTab === TabEnum.left ? 'block' : 'none',
        }}
        className={s['left-tab-content']}
      >
        {children}
      </div>
      <div
        style={{
          display: nowTab === TabEnum.right ? 'block' : 'none',
        }}
        className={s['right-tab-content']}
      >
        {chatBiCallBacks?.component?.biEmbeddedContent}
      </div>
    </DraggableDrawer>
  );
}
