/* eslint-disable @typescript-eslint/member-ordering */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/prop-types */
import React from 'react';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';

interface IErrorBoundaryProps {
  children: React.ReactNode;
}
export default class ErrorBoundary extends React.Component<IErrorBoundaryProps> {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  render() {
    // @ts-ignore
    if (this.state.hasError) {
      return <span className={s['error-tip']}>{t('组件渲染异常')}</span>;
    }

    return this.props.children;
  }
}
