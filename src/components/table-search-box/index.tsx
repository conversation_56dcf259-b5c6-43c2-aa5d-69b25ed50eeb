import React, {
  useEffect, useState, useMemo,
} from 'react';
import { TagSearchBox } from '@tencent/tea-component';
import { getTagKeysHandle } from '@src/service/common-service/final-request';
import { useGlobalSelector } from '@src/store/global/index';
import { processSearchBoxValues } from '@src/utils';
import TagPicker from '../tag-picker';
import s from './index.module.scss';

interface ITableSearchBoxProps {
  value: any;
  hasLevelFilter?: boolean;
  onChange: (value: any) => void;
}
/**
 * 关键字检索组件
 * @returns
 */
export default function TableSearchBox(props: ITableSearchBoxProps): React.ReactElement {
  const {
    value,
    hasLevelFilter = true,
    onChange,
  } = props;
  const { pluginPropsData } = useGlobalSelector();
  const [tagKeys, setTagKeys] = useState<string[]>([]);

  const attributesMemo = useMemo(() => {
    const attributes: any = [
      {
        type: 'input',
        key: 'InsId',
        name: '实例ID',
      },
      {
        type: 'input',
        key: 'Name',
        name: '实例名称',
      },
      {
        type: ['multiple', { all: true, searchable: true }],
        key: 'TagName',
        name: '标签键',
        values: () => tagKeys.map((item) => ({ key: item, name: item })),
      },
      {
        type: 'render',
        key: 'TagList',
        name: '标签',
        render: ({ onSelect }) => (
          <div id="tag-render-overlay" className={s['tag-render-overlay']}>
            <TagPicker
              setPreviewParams={(value) => {
                const tagData = [];
                value.forEach((valueItem) => {
                  valueItem.TagSlice?.forEach((tagItem) => {
                    tagData.push({
                      key: tagItem.Key,
                      name: tagItem.Value?.join(';'),
                    });
                  });
                });
                onSelect(tagData);
              }}
            />
          </div>
        ),
      },
    ];

    if (hasLevelFilter) {
      attributes.splice(2, 0, {
        type: 'multiple',
        key: 'Level',
        name: '风险等级',
        values: () => [
          { key: 2, name: '中风险' },
          { key: 3, name: '高风险' },
        ],
      });
    }
    return attributes;
  }, [tagKeys, hasLevelFilter]);

  useEffect(() => {
    getTagKeysHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          Language: 'zh-CN',
          MaxResults: 1000,
          PaginationToken: '',
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setTagKeys(rs.TagKeys);
      }
    });
  }, []);

  return (
    <TagSearchBox
      attributes={attributesMemo}
      minWidth={400}
      value={value}
      onChange={(values) => {
        const processedValues = processSearchBoxValues(values);
        const valuesFilter = processedValues?.filter((item: any) => !!item?.attr);
        onChange(valuesFilter || []);
      }}
    />
  );
}
