.arch-svg-component {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  
  &:hover {
    .jump-button-bg {
      display: flex;
    }

    .arch-svg:first-child {
      transform: scale(1.05);
      transition: all .35s;
    }
  }
  
  .arch-svg {
    overflow: hidden;
    width: 100%;
    height: 100%;

    :global {
      svg {
        overflow: hidden;
        width: 100%;
        height: 100%;

        &:not(:root) {
          overflow: visible;
        }
      }
    }
  }

  .arch-svg > svg {
    transition: all .35s;

    &:hover {
      transform: scale(1.05);
    }
  }

  .jump-button-bg {
    position: absolute;
    top: 0;
    left: 0;
    display: none;
    overflow: hidden;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, .26);
    cursor: pointer;
  }
}
