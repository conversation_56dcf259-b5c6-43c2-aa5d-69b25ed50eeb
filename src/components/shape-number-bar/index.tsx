import React from 'react';
import GreenIconSvg from '@src/assets/svg-component/green-icon.svg';
import NodeInspectionSvg from '@src/assets/svg-component/node-inspection.svg';
import { RiskFilterTypeEnum } from '@src/constant';
import classNames from 'classnames';
import s from './index.module.scss';

interface IShapeNumberBarProps {
  hasTask: boolean;
  highRiskCount: number;
  mediumRiskCount: number;
  riskFilterType: RiskFilterTypeEnum;
  nodeTaskStatus: {NodeUuid: string; IsFinish: boolean}; // 节点当前的巡检状态
  archTaskStatus: boolean; // 架构图当前的巡检状态
}
/**
 * 图元角标组件
 * @returns
 */
export default function ShapeNumberBar(props: IShapeNumberBarProps): React.ReactElement {
  const {
    highRiskCount,
    mediumRiskCount,
    riskFilterType,
    nodeTaskStatus,
    archTaskStatus,
    hasTask,
  } = props;

  if (archTaskStatus && !nodeTaskStatus?.IsFinish) {
    // 当架构图完成巡检节点没有完成巡检时节点显示loading状态
    return (
      <NodeInspectionSvg
        // @ts-ignore
        className="node-inspection-ing"
      />
    );
  }

  if (!hasTask) {
    return <></>;
  }

  if (highRiskCount === 0 && mediumRiskCount === 0 && riskFilterType === RiskFilterTypeEnum.ALL_RISK) {
    return <GreenIconSvg />;
  }

  return (
    <div className={classNames(
      s['shape-number-bar'],
      {
        [s['shape-number-bar-nobg']]: !(highRiskCount > 0 && mediumRiskCount > 0)
        || riskFilterType === RiskFilterTypeEnum.HIGH_RISK_ONLY
        || riskFilterType === RiskFilterTypeEnum.MID_RISK_ONLY,
      },
    )}
    >
      {
        !!highRiskCount
        && (riskFilterType === RiskFilterTypeEnum.HIGH_RISK_ONLY || riskFilterType === RiskFilterTypeEnum.ALL_RISK)
        && (
          <div
            style={{
              display: 'flex',
              minWidth: '28px',
              height: '28px',
              boxSizing: 'border-box',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '5px 9px',
              borderRadius: '14px',
              marginRight: '4px',
              background: '#e54545',
              color: '#fff',
              fontWeight: '600',
            }}
          >
            {highRiskCount > 99 ? '99+' : highRiskCount}
          </div>
        )
      }
      {
        !!mediumRiskCount
        && (riskFilterType === RiskFilterTypeEnum.MID_RISK_ONLY || riskFilterType === RiskFilterTypeEnum.ALL_RISK)
        && (
          <div
            style={{
              display: 'flex',
              minWidth: '28px',
              height: '28px',
              boxSizing: 'border-box',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '5px 9px',
              borderRadius: '14px',
              background: '#ff7200',
              color: '#fff',
              fontWeight: '600',
            }}
          >
            {mediumRiskCount > 99 ? '99+' : mediumRiskCount}
          </div>
        )
      }
    </div>
  );
}
