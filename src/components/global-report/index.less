.inspection-wrap {
  background-color: #fff;
  font-family: Roboto,San Francisco,Helvetica Neue,Helvetica,Arial,PingFangSC-Light,Hiragina Sans GB,WenQuanYi Micro Hei,microsoft yahei ui,microsoft yahei,sans-serif;

  .loading-wrap {
    display: flex;
    height: 250px;
    justify-content: center;
  }

  .overview-t {
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
  }

  .risk-detail-t {
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
  }

  .desc {
    //padding: 5px 0;
    //margin: 10px 0;
    margin-bottom: 10px;
    color: #888;
    line-height: 17px;

    .num {
      color: #000;
    }
  }

  .time {
    margin-bottom: 40px;
    color: #7c878e;
    line-height: 17px;
  }

  .sdk-cloud-inspection-tabs__tabitem,
  .tea-tabs__tabitem {
    width: 25%!important;
    height: auto!important;
    margin-right: 0!important;
  }

  .common-tab-wrap .tea-tabs__tabitem {
    width: 33.33333%!important;
  }

  .sdk-cloud-inspection-tabs__tab.is-active,
  .tea-tabs__tab.is-active {
    font-weight: normal;
  }

  .sdk-cloud-inspection-tabs__tabbar:before,
  .tea-tabs__tabbar:before {
    display: none;
  }

  .tab-item-wrap {
    width: 100%;
    padding: 16px 14px 3px 12px!important;
    background-color: #f1f2f5;
    cursor: pointer;
    text-align: center;

    .title {
      color: rgba(0, 0, 0, .40);
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
    }

    .count {
      padding: 5px 0;
      font-size: 20px;
      line-height: 28px;
    }

    &.is-active {
      background-color: rgba(0, 112, 255, .10);
    }

    &:after {
      display: none;
    }

    &.high {
      .count {
        color: #fe0303;
      }
    }

    &.middle {
      .count {
        color: #ff7200;
      }
    }

    &.healthy {
      .count {
        color: #0abf5b;
      }
    }
  }

  .btn-wrap {
    padding-top: 5px;
    margin-bottom: 20px;
    //.sel-all-btn {
    //  margin: 0 10px 0 -15px;
    //}
    input {
      top: 1px;
    }

    .sdk-cloud-inspection-form-check,
    .tea-form-check {
      padding-left: 22px;
    }

    .ignore-btn {
      margin-right: 20px;
    }
  }

  .sdk-cloud-inspection-form-check-group,
  .tea-form-check-group {
    width: 100%;
  }

  .inspection-list {
    .no-data-wrap {
      display: flex;
      justify-content: center;
      margin-top: 30px;
    }

    .inspection-check-item {
      display: flex;

      .sdk-cloud-inspection-form-check,
      .tea-form-check {
        padding-left: 0;
        margin-top: 1px;
        margin-right: 23px;

        &.had-risk-label {
          margin-top: 3px;
        }
      }
    }

    .inspection-item {
      //cursor: pointer;
      flex: 1;
      padding-bottom: 10px;
      margin-bottom: 30px;

      .item-t {
        margin-bottom: 10px;
        font-size: 12px;
        font-weight: 600;
        line-height: 17px;

        &.item-t-no-svg {
          .icon-wrap-mid,
          .icon-wrap-high {
            top: 0;

            .icon-img-wrap {
              display: none;
            }
          }
        }

        .icon-wrap {
          position: relative;
          top: 2px;
          display: inline-flex;
          align-items: center;
          margin-left: 10px;
          font-size: 14px;

          &:nth-child(1) {
            margin-left: 20px;
          }

          &.icon-wrap-high {
            color: #e54545;

            .icon-img-wrap {
              max-width: 14px;
              overflow-x: hidden;
            }

            svg {
              margin-left: -14px;
              filter: drop-shadow(14px 0px 0px #e54545);
            }
          }

          &.icon-wrap-mid {
            color: #ff7200;

            .icon-img-wrap {
              max-width: 14px;
              overflow-x: hidden;
            }

            svg {
              margin-left: -14px;
              filter: drop-shadow(14px 0px 0px #ff7200);
            }
          }

          .icon-img-wrap {
            width: 1em;
            margin-right: 4px;
            margin-bottom: -3.5px;
          }

          svg {
            display: inline-block;
            width: 1em;
            height: 1em;
            flex: none;
          }

          span {
            font-weight: normal;
          }
        }

        button {
          display: inline-flex;
          margin-left: 10px;
          text-decoration: none;
          vertical-align: bottom;
        }

        .handling-text {
          margin-left: 10px;
        }
      }

      .tag-wrap {
        padding-right: 47px;
        margin-bottom: 5px;

        &:nth-last-child(2) {
          margin-bottom: 10px;
        }

        .tea-tag {
          margin: 0;
        }
      }

      .item-desc {
        display: flex;
        box-sizing: border-box;
        align-items: center;
        padding: 6px 0;
        color: #444;
        font-size: 12px;
        line-height: 17px;

        &.item-desc-pd {
          width: calc(100% + 23px);
          padding-left: 23px;
          margin-left: -23px;
        }

        &:hover {
          background-color: #f3f4f7;
        }

        .desc-con {
          flex-grow: 1;
          flex-shrink: 1;
          padding-right: 35px;
        }

        svg {
          width: 14px;
          flex-grow: 0;
          flex-shrink: 0;
          margin-right: 5px;
          //padding: 2px;
          //background-color: #CFD6DE;
        }
      }
    }
  }

  .tea-icon {
    position: relative;
    top: 0;
    background-size: auto;
  }
}

.inspection-drawer {
  z-index: 1001!important;

  .ignore-set-th {
    display: flex;
    align-items: center;

    svg {
      margin-left: 3px;
    }
  }

  .inspect-btn-wrap {
    display: flex;

    button {
      margin-right: 10px;

      &:nth-last-child(1) {
        display: flex;
        align-items: center;

        svg {
          margin-right: 5px;
        }

        path {
          fill: #fff;
        }
      }
    }
  }

  .intlc-assessment-tabs-tag__block {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }

  &.size-l {
    width: 1000px!important;
  }

  .detail-t-wrap {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;

    .back-icon {
      position: relative;
      top: -1px;
      margin-right: 24px;
      cursor: pointer;
    }

    .node-t {
      //color: #0076FF;
    }
  }

  .info-wrap {
    display: flex;
    margin-bottom: 5px;

    .info-text-wrap {
      flex-grow: 1;
      flex-shrink: 1;
    }

    .handle-desc-wrap {
      display: flex;
      margin-bottom: 3px;

      .t {
        margin-right: 10px;
      }

      span {
        &:nth-child(2) {
          margin-right: 15px;
        }
      }
    }
  }

  .inpection-desc {
    font-size: 12px;
    line-height: 24px;
  }

  .info-list {
    font-size: 12px;

    .info-item {
      line-height: 24px;
    }
  }

  .info-t {
    padding: 0;
    margin-top: 30px;
    margin-bottom: 10px;
    color: #000;
    font-size: 14px;
    font-weight: 700;

    &.info-t-fir {
      margin-top: 0;
      line-height: 24px;
    }

    &.info-t-condition {
      display: flex;
      align-items: center;

      .change-rule {
        margin-left: 20px;
        font-size: 12px;
      }
    }
  }

  .condition-wrap {
    display: flex;
    font-size: 12px;

    span {
      width: 8px;
      height: 8px;
      flex: none;
      border-radius: 50%;
      margin: 8px 8px 0 0;
    }
  }

  .sdk-cloud-inspection-icon,
  .tea-icon {
    vertical-align: initial!important;
  }

  .ignore-inspection-icon {
    vertical-align: middle!important;
  }

  .tea-icon {
    position: relative;
    top: 0;
    background-size: auto;
  }

  .tea-btn--link {
    text-decoration: none;
  }

  .resource-desc-wrap {
    display: flex;
    line-height: 24px;

    .resource-desc-item {
      flex-grow: 1;
      flex-shrink: 1;
    }

    .resource-position {
      min-width: 400px;
    }
  }

  .pie-wrap {
    position: relative;
    width: 410px;
    flex: none;
    margin: -40px 0 -200px 40px;

    .pie-none {
      display: none;
    }

    .pie-loading-wrap {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 120px;
    }

    .percent-wrap {
      position: absolute;
      top: 150px;
      left: 115px;
      text-align: center;
      transform: translate(-50%, -50%);
      white-space: nowrap;

      .text {
        color: rgba(0, 0, 0, .4)!important;
      }

      .num {
        color: #0abf5b;
        font-size: 16px;
      }
    }

    .pie-legend-wrap {
      position: absolute;
      top: 40px;
      left: 0;
      line-height: 22px;
    }

    .legend-item-wrap {
      position: absolute;
      top: 62px;
      left: 0;
      line-height: 22px;

      .legend-item {
        display: flex;
        color: #000;
        cursor: pointer;
        //&:hover {
        //  opacity: .4;
        //}
        &.active {
          opacity: .4;
        }

        .legent-icon {
          width: 8px;
          height: 8px;
          flex: none;
          border-radius: 50%;
          margin-top: 7px;
          margin-right: 8px;
        }

        .legend-text {
          max-width: 100px;

          span {
            margin: 0 2px;
          }
        }
      }
    }

    .total {
      color: #000;

      .count {
        margin: 0 2px;
      }
    }
  }
}

.inspection-resource-tab {
  margin-top: 20px;

  .sdk-cloud-inspection-tabs__scroll-area,
  .tea-tabs__scroll-area {
    margin-bottom: -2px;
  }
}

.inspection-handle-list {
  width: 110px;

  @keyframes Routes {
    0% {
      transform: rotate(0);
    }

    100% {
      transform: rotate(1turn);
    }
  }

  & > li {
    & {
      display: flex!important;
      align-items: center;
      color: #c1c6c8!important;
    }

    &:hover {
      background-color: #006eff!important;
      color: #fff;
    }
  }

  svg {
    margin-right: 5px;

    path {
      fill: #c1c6c8;
    }
  }

  .loading-icon {
    animation: Routes 1s linear infinite;
  }

  .inspecting-icon {
    animation: Routes 1s linear infinite;
  }
}

@keyframes inspect-routes {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(1turn);
  }
}

.inspecting-icon {
  animation: inspect-routes 1s linear infinite;
}

.inspect-sdk-sub-icon-wrap {
  display: flex;
  align-items: center;
  margin-left: -50%;
  float: left;
  font-size: 16px;

  &.inspect-sdk-sub-icon-wrap-bg {
    padding: 2px 4px;
    border-radius: 40px;
    background-color: rgba(244, 223, 225, .80);
  }

  svg {
    flex: none;
  }

  .num {
    min-width: 28px;
    height: 28px;
    box-sizing: border-box;
    padding: 0px 3px;
    border-radius: 14px;
    margin-right: 3px;
    color: #fff;
    font-size: 16px;
    line-height: 28px;
    text-align: center;
  }

  .high-num {
    background-color: #e54545;
  }

  .mid-num {
    margin-right: 0;
    background-color: #ff7200;
  }

  img {
    width: 28px;
    height: 28px;
  }
}

.inspect-sdk-footer-btn-wrap {
  text-align: center;

  button:nth-child(1) {
    margin-right: 10px;
  }

  button:nth-child(2) {
    display: inline-flex!important;
    align-items: center;

    @keyframes Routes {
      0% {
        transform: rotate(0);
      }

      100% {
        transform: rotate(1turn);
      }
    }

    svg {
      margin-right: 5px;
      fill: #fff;
    }

    .inspecting-icon {
      animation: Routes 1s linear infinite;
    }
  }

  .is-loading .tea-icon-loading {
    background-size: auto;
  }

  .tea-btn--link {
    text-decoration: none;
  }
}

.inspect-sdk-condition-modal {
  .tea-icon-loading {
    background-size: auto;
  }

  .is-loading .tea-icon-loading {
    background-size: auto;
  }

  .tea-icon-close {
    background-size: auto;
  }

  .tea-icon-arrowdown {
    background-size: auto;
  }

  .tea-inputnum__minus:before,
  .tea-inputnum__plus:before {
    display: none;
  }
}

.inspect-sdk-overlay-root {
  background-color: red;

  .tea-backdrop {
    opacity: inherit;
  }

  .tea-overlay {
    z-index: 1001;
  }
}

.inspect-detail-tab {
  .table-loading-wrap {
    display: flex;
    justify-content: center;

    .tea-action-state {
      display: inline-flex!important;
    }
  }

  .inspect-tag-wrap {
    .tea-tag {
      display: block;
      padding: 0 2px;

      span {
        max-width: 300px;
      }
    }
  }
}

.settingsModal {
  .tea-icon {
    background-size: auto;
  }

  .tea-dialog__inner {
    width: 70%;
    height: 70%;

    .tea-dialog__body {
      height: calc(100% - 46px);

      .tea-layout__content,
      .tea-layout__content-body-inner {
        overflow: hidden;
      }

      .tea-layout__content-body-inner {
        max-width: none;
      }

      section,
      .tea-layout__content,
      .tea-layout__content-body,
      .tea-layout__content-body-inner {
        height: 100%;

        .tea-card {
          height: calc(100% - 40px);

          .tea-table {
            height: calc(100% - 56px);

            .tea-dropdown-filter:hover .tea-icon-filter {
              background-size: auto;
            }
          }
        }
      }
    }
  }

  .settings-table-rule-button {
    &:hover {
      text-decoration: underline;
    }

    &:focus {
      text-decoration: none;
    }
  }
}

.inspect-report-modal-real {
  .sdk-cloud-inspection-dialog__headertitle {
    position: relative;
    top: -8px;
    margin-bottom: 0 !important;
  }

  .head-btn-wrap {
    display: flex;

    .download-btn {
      cursor: pointer;
    }
    //button {
    //  font-weight: bold;
    //}
  }

  .tea-icon-close {
    background-size: auto;
  }

  .tea-link-external:after {
    background-size: auto;
  }

  .tea-dialog__header {
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    width: 100%;
    padding-top: 12px;
    background-color: #fff;
    box-shadow: 0px 2px 8px -1px rgba(0, 0, 0, .05), 0px 2px 4px -2px rgba(0, 0, 0, .05);
    text-align: right;

    .tea-dialog__headertitle {
      padding-right: 60px;

      button {
        display: flex;
        align-items: center;

        svg {
          margin-right: 5px;
        }
      }
    }

    .tea-btn--icon {
      top: 10px;
      right: 10px;
    }
  }

  .tea-dialog__inner {
    position: relative;
    width: 800px;
    padding-right: 0;
    padding-bottom: 0;
    padding-left: 0;
    overflow-x: hidden;
    //background-color: #1B3EC3;
    overflow-y: auto;
  }

  .tea-dialog__body {
    //padding: 0 25px;
    max-height: 75vh;
    margin-top: 67px;
    overflow-x: hidden;
    overflow-y: auto;

    .con-wrap {
      padding: 0 25px;
      background-color: #1b3ec3;
    }

    &::-webkit-scrollbar {
      display: block;
      width: 10px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 9px;
      background: #dbdbdb;

      &:hover {
        background: #c2c2c2;
        background-clip: content-box;
      }
    }

    &::-webkit-scrollbar-track {
      //background-color: transparent;
    }
  }

  .t-area {
    position: relative;
    padding-top: 30px;

    .t-area-text {
      position: relative;
      z-index: 2;
      color: #fff;

      .report-t {
        display: flex;
        align-items: center;
        margin-bottom: 14px;
        font-size: 24px;

        svg {
          margin-right: 16px;
        }
      }

      .report-t-en {
        margin-bottom: 20px;
        font-size: 16px;
      }

      .line {
        display: flex;
        margin-bottom: 16px;
        font-size: 16px;

        &:nth-last-child(1) {
          margin-bottom: 27px;
        }

        .line-t {
          margin-right: 10px;
        }
      }
    }

    .t-area-bg {
      position: absolute;
      top: 30px;
      right: 0;
      width: 476px;
      height: 224px;
      background-image: url(https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/a7171bde-9cb7-4eae-810c-ad9c76bc8e17.png);
      background-position: 0px -28px;
      background-repeat: no-repeat;
      background-size: 100% auto;
      text-align: right;

      .svg-t {
        margin-top: 25px;
        margin-right: 79px;
      }

      .sub-svg-t {
        margin-top: 13px;
        margin-right: 148px;
      }
    }
  }

  .card-t {
    margin-bottom: 12px;
    color: #1b3ec3;
    font-size: 16px;
    font-weight: bold;
  }

  .pic-wrap {
    height: 382px;
    margin-top: 24px;
    margin-bottom: 34px;

    svg {
      overflow: auto;
      width: 100%;
      height: 382px;
    }
  }

  .overview-t-desc {
    margin-bottom: 8px;
    font-size: 16px;

    .percent-num {
      color: #1b3ec3;
    }
  }

  .overview-t-desc-sub {
    margin-bottom: 24px;
    font-size: 13px;
  }

  .risk-items-wrap {
    display: flex;
    gap: 16px;

    .risk-items {
      width: 25%;
      padding: 24px 0 24px 24px;

      .items-t {
        margin-bottom: 8px;
        font-size: 16px;
        line-height: 16px;
      }

      .item-num {
        margin-bottom: 8px;
        font-size: 32px;
        line-height: 32px;
      }

      .item-desc {
        color: rgba(0, 0, 0, .60);
        font-size: 12px;
        line-height: 20px;

        svg {
          margin: -2px 5px 0;
          vertical-align: middle;
        }
      }

      &:nth-child(1) {
        background-color: #f0f4ff;

        .items-t {
          margin-bottom: 8px;
          font-size: 16px;
        }

        .item-num {
          margin-bottom: 8px;
          color: #1b3ec3;
          font-size: 32px;
        }

        .item-desc {
          svg {
            path {
              fill: #1b3ec3;
            }
          }

          .num {
            color: #1b3ec3;
          }
        }
      }

      &:nth-child(2) {
        background-color: #fdf1f1;

        .items-t {

        }

        .item-num {
          color: #f64041;
        }

        .item-desc {
          svg {
            path {
              fill: #f64041;
            }
          }

          .num {
            color: #f64041;
          }
        }
      }

      &:nth-child(3) {
        background-color: #fff4ec;

        .items-t {

        }

        .item-num {
          color: #ff7800;
        }

        .item-desc {
          svg {
            path {
              fill: #ff7800;
            }
          }

          .num {
            color: #ff7800;
          }
        }
      }

      &:nth-child(4) {
        background-color: rgba(224, 247, 235, .60);

        .items-t {

        }

        .item-num {
          color: #0cbf5b;
        }

        .item-desc {
          svg {
            path {
              fill: #0cbf5b;
            }
          }

          .num {
            color: #0cbf5b;
          }
        }
      }
    }
  }

  .chart-wrap {
    display: flex;
    margin-top: 16px;
    gap: 16px;

    &.chart-wrap-br {
      border: 1px solid #e6e9ef;
    }

    .chart-outer {
      height: 300px;
    }

    .chart-list {
      display: flex;
      flex-wrap: wrap;
      padding: 18px 24px 0;

      .desc-wrap {
        height: 170px!important;
        box-sizing: border-box;
        padding: 12px 20px;
        background-image: url(https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/264056be-fdcc-4c3b-b224-153634d2de5e.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;

        .desc-t-wrap {
          width: 208px;
          padding: 4px 8px;
          margin-left: -8px;
          background: #f8f9ff;
          //box-shadow: 0px 10px 20px 0px rgba(75, 91, 118, 0.08), 0px 4px 6px 0px rgba(75, 91, 118, 0.08);
          filter: drop-shadow(5px 5px 5px rgba(75, 91, 118, .08));
        }

        .desc-t {
          color: #2d5eea;
          font-size: 12px;
          font-style: normal;
          font-weight: 500;
          line-height: 18px;
        }

        .desc-line {
          margin-top: 7px;
          color: rgba(0, 0, 0, .90);
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 18px;
        }
      }

      .line-pic-item {
        width: 50%;
        height: 250px;
        margin-bottom: -40px;
      }
    }

    .title {
      padding-left: 24px;
      font-size: 16px;
      font-style: normal;
      font-weight: bold;
    }

    .tea-link-external {
      padding-left: 24px;
      margin-top: 8px;
    }

    .chart-item {
      width: 50%;
      padding-top: 24px;

      .chart-outer {
        height: 300px;

        .pic-wrap {
          padding-left: 24px;
          margin-top: 24px;

          svg {
            overflow: auto;
            width: 100%;
            height: 220px;
          }
        }

        .line-pic-item {
          height: 250px;
          margin-bottom: -40px;
        }
      }

      &.chart-item-br {
        border: 1px solid #e6e9ef;

        &:nth-child(2) {
          .chart-outer {
            height: 350px;
          }
        }
      }
    }
  }

  .download-wrap {
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    margin-top: 20px;
    color: #fff;

    button {
      display: flex;
      align-items: center;
      margin-left: 0px !important;
      color: #000;

      svg {
        margin-right: 5px;
      }
    }
  }

  .tab-desc {
    margin-top: 10px;
    color: #3d485d;
    font-size: 12px;
  }

  .inspect-see-report-tab {
    .sdk-cloud-inspection-table__header {
      margin-right: 0 !important;
    }

    .tea-table__body {
      &::-webkit-scrollbar {
        display: block;
        width: 10px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 9px;
        background: #dbdbdb;

        &:hover {
          background: #c2c2c2;
          background-clip: content-box;
        }
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
    }

    th {
      background-color: #1b3ec3;

      > div {
        color: #fff;
      }
    }

    .tea-table__box tr:nth-child(even) {
      background-color: #ebeef2;
    }

    .repair-line-wrap {
      display: box;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      text-overflow: ellipsis;
    }
  }

  .tea-alert {
    position: absolute;
    z-index: 3;
    top: 44px;
    left: 0;
    width: 100%;

    .tea-icon {
      background-size: auto;
    }

    .name {
      font-weight: bold;
    }
  }
}

.inspect-report-modal-real-isa .tea-dialog__body {
  margin-top: 22px;
}

.report-hover-text-wrap {
  font-size: 12px;

  .type {
    font-weight: 800;
    //margin: 0 3px;
  }
}

.inspect-handle-modal {
  .tea-dialog__header {
    i {
      background-size: auto!important;
    }
  }

  .handle-modal-sure-btn {
    i {
      background-size: auto!important;
    }
  }
}

.progress-overlay {
  .app-cloud-arch-bubble {
    max-width: 600px;
  }
}

.report-scan-wrap {
  display: flex;
  align-items: center;
  margin-left: 15px;
  font-size: 12px;
  white-space: nowrap;

  span {
    margin: 0 3px;
  }

  .tea-btn {
    margin-right: 3px;
  }
}

.handle-progress-icon {
  width: 18px;
  height: 18px;
  margin-left: -1px;

  path {
    fill: #6a7b92;
  }
}