.risk-number-panel-container {
  display: flex;
  width: auto;
  height: 100%;
  box-sizing: border-box;
  align-items: center;
  padding: 0 6px;
  
  .risk-number-label {
    margin-right: 5px;
    color: rgba(0, 0, 0, .9);
  }

  .high-risk-number,
  .mid-risk-number {
    display: flex;
    height: 100%;
    align-items: center;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;

    .high-risk-number-title,
    .mid-risk-number-title {
      margin: 0 5px;
    }
  }

  .high-risk-number {
    height: 23px;
    padding-right: 6px;
    color: #e54545;

    .high-risk-number-count {
      display: flex;
      width: auto;
      min-width: 16px;
      height: 16px;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      aspect-ratio: 1;  // 保持宽高比1:1，确保圆形
      background-color: #e54545;
      color: #fff;
      font-size: 10px;

      // 当内容较长时，保持圆形但增加最小尺寸
      &.long-content {
        min-width: 22px;
        border-radius: 8px;
      }
    }
  }

  .mid-risk-number {
    height: 23px;
    padding-right: 6px;
    color: #ff7200;

    .mid-risk-number-count {
      display: flex;
      width: auto;
      min-width: 16px;
      height: 16px;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      aspect-ratio: 1;  // 保持宽高比1:1，确保圆形
      background-color: #ff7200;
      color: #fff;
      font-size: 10px;

      // 当内容较长时，保持圆形但增加最小尺寸
      &.long-content {
        min-width: 22px;
        border-radius: 8px;
      }
    }
  }

  .mid-risk-number-selected {
    border-radius: 4px;
    background-color: #e7eaef;
  }

  .high-risk-number-selected {
    border-radius: 4px;
    background-color: #e7eaef;
  }

  .risk-number-panel-split {
    width: 1px;
    height: 16px;
    background: #cfd5de;
  }

  .risk-number-panel-change {
    display: flex;
    height: 100%;
    box-sizing: border-box;
    align-items: center;
  }

  .chat-bi-trriger {
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center;
  }
}