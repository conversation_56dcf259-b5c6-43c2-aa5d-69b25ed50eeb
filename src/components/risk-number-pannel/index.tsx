import React, { useEffect, useState, useCallback } from 'react';
import { app } from '@tea/app';
import { Button, Select } from '@tencent/tea-component';
import { changeRiskPanelData } from '@src/store/risk-search-panel';
import { RiskFilterTypeEnum, EnvEnum, ALL_RISK_TYPE } from '@src/constant';
import classNames from 'classnames';
import {
  describeGroupAndProductInfosHandle,
} from '@src/service/inspection-settings-modal/final-request';
import {
  describeArchScanRiskInfoHandle,
} from '@src/service/common-service/final-request';
import { reportEvent } from '@src/utils';
import KefuSvg from '@src/assets/svg-component/kefu.svg';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';

interface IRiskNumberPanelProps {
  archScanRiskInfo: any;
  riskFilterType: string;
  pluginPropsData: any;
  riskType: string;
  searchBoxValue: any;
  dispatch: any;
  changeGlobalData: any;
  chatBiCallBacks: any;
}

export default function RiskNumberPanel(props: IRiskNumberPanelProps): React.ReactElement {
  const {
    archScanRiskInfo,
    riskFilterType,
    pluginPropsData,
    riskType,
    searchBoxValue,
    dispatch,
    changeGlobalData,
    chatBiCallBacks,
  } = props;
  const [groupsList, setGroupsList] = useState([]);
  const [groupsDict, setGroupsDict] = useState({});

  // 合并筛选条件
  const mergeSearchFilters = useCallback((newFilter, existingFilters = []) => {
    const existingFilterMap = new Map();
    existingFilters.forEach((filter, index) => {
      existingFilterMap.set(filter.attr.key, { ...filter, _key: index });
    });

    // 如果新筛选条件已存在，则替换；否则添加
    existingFilterMap.set(newFilter.attr.key, { ...newFilter, _key: existingFilterMap.size });

    return Array.from(existingFilterMap.values());
  }, []);

  // 移除特定筛选条件
  const removeSearchFilter = useCallback((filterKey, existingFilters = []) => existingFilters.filter((filter) => filter.attr.key !== filterKey), []);

  // 创建风险等级筛选条件
  const createRiskLevelFilter = useCallback((level, name) => ({
    attr: {
      key: 'Level',
      name: '风险等级',
    },
    values: [{ key: level, name }],
    _key: 0,
  }), []);

  // 创建风险类型筛选条件
  const createRiskTypeFilter = useCallback(() => {
    if (!Number(riskType) || !groupsDict[+riskType]) return null;
    return {
      attr: {
        key: 'GroupIds',
        name: '风险类型',
      },
      values: [{ key: +riskType, name: groupsDict[+riskType] }],
      _key: 0,
    };
  }, [riskType, groupsDict]);

  // 格式化数字显示，最大显示99+
  const formatNumber = useCallback((num) => {
    if (num > 99) return '99+';
    return String(num);
  }, []);

  // 获取产品和维度信息
  const getProductsGroupsInfo = async () => {
    try {
      const res = await describeGroupAndProductInfosHandle({
        env: pluginPropsData.env,
        uin: pluginPropsData.uin,
      });
      if (!res) {
        return;
      }
      const groupDict = {};
      const tmp = [{ text: '全部类型', value: ALL_RISK_TYPE }];
      res?.Groups?.forEach((item) => {
        groupDict[item.Id] = item.GroupName;
        tmp.push({ value: `${item.Id}`, text: item.GroupName });
      });
      setGroupsDict(groupDict);
      setGroupsList(tmp);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{msg}}', { msg }));
    }
  };

  const updateShapesBarInfo = async (riskTypeVal) => {
    const riskInfoResult = await describeArchScanRiskInfoHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          MapId: pluginPropsData.archInfo.archId,
          GroupIds: Number(riskTypeVal) ? [Number(riskTypeVal)] : [],
        },
      },
      uin: pluginPropsData.uin,
    });
    if (riskInfoResult) {
      // 存储公共store
      dispatch(changeGlobalData({
        archScanRiskInfo: riskInfoResult,
      }));
    }
  };

  useEffect(() => {
    getProductsGroupsInfo();
  }, []);

  return (
    <div
      className={s['risk-number-panel-container']}
    >
      <div className={s['risk-number-label']}>风险实例数</div>
      <div
        className={classNames(
          s['high-risk-number'],
          { [s['high-risk-number-selected']]: riskFilterType === RiskFilterTypeEnum.HIGH_RISK_ONLY },
        )}
        onClick={() => {
          let newSearchBoxValue = [...(searchBoxValue || [])];

          if (riskFilterType === RiskFilterTypeEnum.HIGH_RISK_ONLY) {
            // 取消高风险选择
            dispatch(changeGlobalData({
              riskFilterType: RiskFilterTypeEnum.ALL_RISK,
            }));

            // 移除风险等级筛选条件
            newSearchBoxValue = removeSearchFilter('Level', newSearchBoxValue);

            // 如果有风险类型，保留风险类型筛选条件
            const riskTypeFilter = createRiskTypeFilter();
            if (riskTypeFilter) {
              newSearchBoxValue = mergeSearchFilters(riskTypeFilter, newSearchBoxValue);
            }

            dispatch(changeRiskPanelData({
              visible: false,
              searchBoxValue: newSearchBoxValue,
            }));
          } else {
            // 选择高风险
            dispatch(changeGlobalData({
              riskFilterType: RiskFilterTypeEnum.HIGH_RISK_ONLY,
            }));

            // 添加高风险等级筛选条件
            const highRiskFilter = createRiskLevelFilter(3, '高风险');
            newSearchBoxValue = mergeSearchFilters(highRiskFilter, newSearchBoxValue);

            // 如果有风险类型，也添加风险类型筛选条件
            const riskTypeFilter = createRiskTypeFilter();
            if (riskTypeFilter) {
              newSearchBoxValue = mergeSearchFilters(riskTypeFilter, newSearchBoxValue);
            }

            dispatch(changeRiskPanelData({
              visible: true,
              searchBoxValue: newSearchBoxValue,
            }));
            chatBiCallBacks?.changeChatVisibleFlag(false);
          }

          reportEvent(pluginPropsData.env as EnvEnum, {
            archId: pluginPropsData.archInfo.archId,
            subUin: pluginPropsData.uin,
            subUinName: pluginPropsData.userName,
            eventType: 'topBarClickHighRisk', // 顶栏-点击高风险
          });
        }}
      >
        <span className={s['high-risk-number-title']}>高</span>
        <span className={classNames(
          s['high-risk-number-count'],
          { [s['long-content']]: archScanRiskInfo.TotalHighRiskCount > 99 },
        )}
        >
          {formatNumber(archScanRiskInfo.TotalHighRiskCount)}
        </span>
      </div>
      <div className={s['risk-number-panel-split']} style={{ margin: '0 11px' }} />
      <div
        className={classNames(
          s['mid-risk-number'],
          { [s['mid-risk-number-selected']]: riskFilterType === RiskFilterTypeEnum.MID_RISK_ONLY },
        )}
        onClick={() => {
          let newSearchBoxValue = [...(searchBoxValue || [])];

          if (riskFilterType === RiskFilterTypeEnum.MID_RISK_ONLY) {
            // 取消中风险选择
            dispatch(changeGlobalData({
              riskFilterType: RiskFilterTypeEnum.ALL_RISK,
            }));

            // 移除风险等级筛选条件
            newSearchBoxValue = removeSearchFilter('Level', newSearchBoxValue);

            // 如果有风险类型，保留风险类型筛选条件
            const riskTypeFilter = createRiskTypeFilter();
            if (riskTypeFilter) {
              newSearchBoxValue = mergeSearchFilters(riskTypeFilter, newSearchBoxValue);
            }

            dispatch(changeRiskPanelData({
              visible: false,
              searchBoxValue: newSearchBoxValue,
            }));
          } else {
            // 选择中风险
            dispatch(changeGlobalData({
              riskFilterType: RiskFilterTypeEnum.MID_RISK_ONLY,
            }));

            // 添加中风险等级筛选条件
            const midRiskFilter = createRiskLevelFilter(2, '中风险');
            newSearchBoxValue = mergeSearchFilters(midRiskFilter, newSearchBoxValue);

            // 如果有风险类型，也添加风险类型筛选条件
            const riskTypeFilter = createRiskTypeFilter();
            if (riskTypeFilter) {
              newSearchBoxValue = mergeSearchFilters(riskTypeFilter, newSearchBoxValue);
            }

            dispatch(changeRiskPanelData({
              visible: true,
              searchBoxValue: newSearchBoxValue,
            }));
            chatBiCallBacks?.changeChatVisibleFlag(false);
          }

          reportEvent(pluginPropsData.env as EnvEnum, {
            archId: pluginPropsData.archInfo.archId,
            subUin: pluginPropsData.uin,
            subUinName: pluginPropsData.userName,
            eventType: 'topBarClickMediumRisk', // 顶栏-点击中风险
          });
        }}
      >
        <span className={s['mid-risk-number-title']}>中</span>
        <span className={classNames(
          s['mid-risk-number-count'],
          { [s['long-content']]: archScanRiskInfo.TotalMediumRiskCount > 99 },
        )}
        >
          {formatNumber(archScanRiskInfo.TotalMediumRiskCount)}
        </span>
      </div>
      <div className={s['risk-number-panel-split']} style={{ margin: '0 11px' }} />
      <Select
        appearance="button"
        size="s"
        options={groupsList}
        value={riskType}
        matchButtonWidth
        onChange={(value) => {
          dispatch(changeGlobalData({
            riskType: value,
          }));
          updateShapesBarInfo(value);

          // 更新搜索筛选条件
          let newSearchBoxValue = [...(searchBoxValue || [])];

          if (value === ALL_RISK_TYPE) {
            // 选择全部类型，移除风险类型筛选条件
            newSearchBoxValue = removeSearchFilter('GroupIds', newSearchBoxValue);
          } else if (Number(value) && groupsDict[+value]) {
            // 选择特定风险类型，添加或更新风险类型筛选条件
            const riskTypeFilter = {
              attr: {
                key: 'GroupIds',
                name: '风险类型',
              },
              values: [{ key: +value, name: groupsDict[+value] }],
              _key: 0,
            };
            newSearchBoxValue = mergeSearchFilters(riskTypeFilter, newSearchBoxValue);
          }

          // 如果当前有风险等级筛选，保持不变
          const hasLevelFilter = newSearchBoxValue.some((filter) => filter.attr.key === 'Level');

          dispatch(changeRiskPanelData({
            visible: newSearchBoxValue.length > 0 || hasLevelFilter,
            searchBoxValue: newSearchBoxValue,
          }));
          chatBiCallBacks?.changeChatVisibleFlag(false);
        }}
      />
      <div className={s['risk-number-panel-split']} style={{ margin: '0 11px' }} />
      <div
        className={s['chat-bi-trriger']}
        onClick={() => {
          chatBiCallBacks?.changeChatVisibleFlag(true);
          chatBiCallBacks?.setSelectNode?.(undefined);
          dispatch(changeRiskPanelData({ visible: false }));
        }}
      >
        <KefuSvg />
        <Button type="link" style={{ marginLeft: 5 }}>风险分析</Button>
      </div>
    </div>
  );
}
