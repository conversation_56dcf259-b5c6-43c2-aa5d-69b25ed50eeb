import { app } from '@tea/app';
import React, {
  useState,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from 'react';
import {
  Drawer,
  Tabs,
  TabPanel,
  TagSearchBox,
  message,
  Modal,
  Button,
} from '@tencent/tea-component';
import { useDispatch } from 'react-redux';
import {
  useRiskPanelSelector,
  changeRiskPanelData,
} from '@src/store/risk-search-panel';
import useInspectionTask from '@src/hooks/useInspectionTask';
import { useGlobalSelector } from '@src/store/global/index';
import useUpdateShapesBar from '@src/hooks/useUpdateShapesBar';
import IgnoreModal from '@src/components/ignore-modal';
import StartAppSvg from '@src/assets/svg-component/start-app.svg';
import {
  updateRiskItemStatusInInstanceHandle,
  describeArchScanTaskRiskInfoHandle,
} from '@src/service/node-risk-drawer/final-request';
import { reportEvent, deduplicateSearchItems } from '@src/utils';
import { IDescribeArchScanTaskRiskInfoResult } from '@src/service/node-risk-drawer/index.type';

import {
  UpdateInstanceToClaimedOperateEnum, Env, IDriftDetectionTaskEnum, InspectionTaskStatusEnum, EnvEnum,
} from '@src/constant/index';
import { describeArchScanRiskItems } from '@src/service/risk-search-drawer/final-request';
import {
  describeGroupAndProductInfosHandle,
} from '@src/service/inspection-settings-modal/final-request';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';
import Child from './components/virtuoso-child';

/**
 * 风险查找抽屉
 * @returns
 */
export default function RiskSearchDrawer({
  pluginAPI,
  createArchScanReportFile,
}: {
  pluginAPI: AppPluginAPI.PluginAPI;
  createArchScanReportFile: () => void;
}): React.ReactElement {
  const { loginUin } = app.user;
  const { visible, strategyTaskItems, searchBoxValue } = useRiskPanelSelector();
  const drawerRef = useRef<HTMLDivElement>(null);
  const {
    pluginPropsData,
    strategies,
    inspectionTaskStatus,
    driftDetectionTaskStatus,
    inspectionTaskData,
    supportTaskProductList: supportGlobalTaskProductList,
  } = useGlobalSelector();
  const dispatch = useDispatch();
  const { updateShapesBar } = useUpdateShapesBar();
  const { archInfo } = pluginPropsData;
  const { archId } = archInfo;
  const { startArchScanTask } = useInspectionTask({ pluginAPI, createArchScanReportFile });
  const [archScanRiskItemsLoading, setArchScanRiskItemsLoading] = useState(false);
  const [supportTaskProductList, setSupportTaskProductList] = useState([]);
  const [groupsList, setGroupsList] = useState([]);
  const ignoreParams = useRef<{
    instanceRegionList?: {
      InstanceId: string,
      Region: string,
    }[],
    strategyId?: number,
    isCancel?: boolean,
    nodeUuid?: string,
  }>({});
  const [nodeTaskRiskInfo, setNodeTaskRiskInfo] = useState<IDescribeArchScanTaskRiskInfoResult>({
    BindInstanceCount: 0,
    LastScanAt: '',
    NodeChanged: false,
    TotalStrategyCount: 0,
  });
  const attributes = useMemo(
    () => [
      {
        type: ['multiple', { searchable: true }],
        key: 'Product',
        name: '云产品',
        values: supportTaskProductList,
      },
      {
        type: 'input',
        key: 'Strategy',
        name: '评估项名称',
      },
      {
        type: 'input',
        key: 'InsId',
        name: '实例ID',
      },
      {
        type: 'multiple',
        key: 'Level',
        name: '风险等级',
        values: () => [
          { key: 2, name: '中风险' },
          { key: 3, name: '高风险' },
        ],
      },
      {
        type: 'multiple',
        key: 'GroupIds',
        name: '风险类型',
        values: () => groupsList,
      },
    ],
    [supportTaskProductList, strategies, groupsList],
  );
  const [tabs] = useState([{ id: '查找', label: '查找' }]);
  const [ignoreState, setIgnoreState] = useState({
    visible: false,
    submitLoading: false,
  });

  const hasNodeNotFinish = inspectionTaskData?.NodeTaskStatusList?.filter((item) => !item.IsFinish);

  const filters = useMemo(() => searchBoxValue
    ?.map((item) => ({
      Name: item?.attr?.key || 'Strategy',
      Values: item?.values?.map((value) => `${value.key || ''}` || `${value.name || ''}`),
    }))
    ?.filter(
      (v) => !(
        v.Name === 'Strategy'
            && v.Values.length === 1
            && v.Values[0] === ''
      ),
    ), [searchBoxValue]);

  const onIgnore = useCallback(async (data) => {
    ignoreParams.current = data;
    if (data?.instanceRegionList?.length && !data.isCancel) {
      setIgnoreState((prev) => ({
        ...prev,
        visible: true,
        submitLoading: false,
      }));
    }
    if (data?.instanceRegionList?.length && data.isCancel) {
      await Modal.confirm({
        message: '确认取消忽略当前所选实例？',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          updateInstanceToIgnoredStatus('');
        },
      });
    }
  }, [filters, ignoreParams.current]);

  // 获取产品和维度信息
  const getProductsGroupsInfo = async () => {
    try {
      const res = await describeGroupAndProductInfosHandle({
        env: pluginPropsData.env,
        uin: pluginPropsData.uin,
      });
      if (!res) {
        return;
      }

      // 处理产品数据
      const productTmp = [];
      const productDict = {};
      res.Products?.forEach((item) => {
        productDict[item.Product] = item.Name;
        productTmp.push({ key: item.Product, name: item.Name });
      });
      setSupportTaskProductList(productTmp);

      // 处理风险类型数据
      const groupTmp = [];
      res.Groups?.forEach((item) => {
        groupTmp.push({ key: item.Id, name: item.GroupName });
      });
      setGroupsList(groupTmp);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{msg}}', { msg }));
    }
  };

  const onSearch = useCallback(
    (/* e, value */) => {
      fetchArchScanRiskItems();
    },
    [filters],
  );

  const getArchScanTaskRiskInfo = () => {
    describeArchScanTaskRiskInfoHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          MapId: pluginPropsData.archInfo.archId,
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setNodeTaskRiskInfo(rs);
      }
    });
  };
  const fetchArchScanRiskItems = useCallback((callback?: () => void, setLoading = true) => {
    if (filters?.length) {
      if (setLoading) {
        setArchScanRiskItemsLoading(true);
      }
      describeArchScanRiskItems({
        env: pluginPropsData.env as Env,
        uin: pluginPropsData.uin,
        data: {
          MapId: archId,
          Filters: filters,
        },
      }).then((res) => {
        if (res) {
          dispatch(
            changeRiskPanelData({
              strategyTaskItems: res?.StrategyTaskItems ?? [],
            }),
          );
        }
      }).finally(() => {
        if (setLoading) {
          setArchScanRiskItemsLoading(false);
        }
        callback?.();
      });
    } else if (!searchBoxValue || searchBoxValue.length === 0) {
      // 防止value有值但filters为空的情况，避免意外清空数据
      dispatch(
        changeRiskPanelData({
          strategyTaskItems: null,
        }),
      );
    }
  }, [filters, visible, searchBoxValue]);

  const updateInstanceToIgnoredStatus = useCallback(async (reason: string) => {
    const {
      instanceRegionList, strategyId, isCancel, nodeUuid,
    } = ignoreParams.current;
    updateRiskItemStatusInInstanceHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          InstanceRegionList: instanceRegionList,
          StrategyId: strategyId,
          IgnoredReason: reason,
          Operate: isCancel ? UpdateInstanceToClaimedOperateEnum.delete : UpdateInstanceToClaimedOperateEnum.add,
          IgnoredPerson: pluginPropsData?.env === EnvEnum.ISA ? pluginPropsData.userName : (pluginPropsData.userName?.slice(0, pluginPropsData.userName.lastIndexOf('@')) ?? ''),
          ClaimUin: loginUin?.toString() ?? '',
          NodeUuid: nodeUuid,
          MapId: archId,
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        message.success({ content: isCancel ? '取消忽略成功' : '忽略成功' });
        // 更新角标
        updateShapesBar();
        setIgnoreState((prev) => ({
          ...prev,
          visible: false,
          submitLoading: false,
        }));
        dispatch(
          changeRiskPanelData({
            ignoreSelectedKeys: [],
            riskSelectedKeys: [],
          }),
        );
      }
    }).finally(() => {
      // 保持当前筛选条件，不重置value和filters
      if (filters?.length) {
        fetchArchScanRiskItems(() => {
          document.getElementById(`${strategyId}`)?.scrollIntoView({ behavior: 'smooth' });
        }, false);
      }
      setIgnoreState((prev) => ({
        ...prev,
        visible: false,
        submitLoading: false,
      }));
      dispatch(
        changeRiskPanelData({
          refreshInstanceListHash: Date.now(),
        }),
      );
    });
  }, [filters]);
  useEffect(() => {
    if (!(strategies?.length && strategyTaskItems?.length)) {
      dispatch(
        changeRiskPanelData({
          activeCollapseId: '',
        }),
      );
    }
  }, [strategies, strategyTaskItems]);

  useEffect(() => {
    if (visible) {
      getArchScanTaskRiskInfo();
    } else {
      pluginAPI.removeAllNodeClass();
      dispatch(
        changeRiskPanelData({
          activeCollapseId: '',
          ignoreSelectedKeys: [],
          riskSelectedKeys: [],
          positionUuids: [],
        }),
      );
    }
    return () => {
      ignoreParams.current = {};
      pluginAPI.removeAllNodeClass();
      dispatch(
        changeRiskPanelData({
          activeCollapseId: '',
          ignoreSelectedKeys: [],
          riskSelectedKeys: [],
        }),
      );
    };
  }, [visible]);

  useEffect(() => {
    if (visible) {
      if (filters?.length) {
        fetchArchScanRiskItems();
      } else {
        dispatch(
          changeRiskPanelData({
            strategyTaskItems: null,
          }),
        );
      }
    }
  }, [filters, visible]);

  useEffect(() => {
    getProductsGroupsInfo();
  }, []);

  useEffect(() => {
    if (filters?.length) {
      reportEvent(pluginPropsData.env as EnvEnum, {
        archId: pluginPropsData.archInfo.archId,
        subUin: pluginPropsData.uin,
        subUinName: pluginPropsData.userName,
        eventType: 'clickSubmitRiskSearch', // 提交风险查找
        searchCondition: JSON.stringify(filters),
      });
    }
  }, [filters]);

  return (
    <Drawer
      ref={drawerRef}
      placement="right"
      visible={visible}
      style={{ width: 640, userSelect: 'text' }}
      title="风险查找"
      className={s.drawer}
      outerClickClosable={false}
      footer={(
        <div className={s['drawer-footer-container']}>
          <Button
            disabled={inspectionTaskStatus === InspectionTaskStatusEnum.inspectioning
            || driftDetectionTaskStatus !== IDriftDetectionTaskEnum.driftDetectioned
            || hasNodeNotFinish?.length !== 0}
            type="primary"
            onClick={() => {
              reportEvent(pluginPropsData.env as EnvEnum, {
                archId: pluginPropsData.archInfo.archId,
                subUin: pluginPropsData.uin,
                subUinName: pluginPropsData.userName,
                eventType: 'clickRiskSearchCreateScan', // 点击风险查找中的发起架构巡检
              });
              startArchScanTask(supportGlobalTaskProductList, true, false);
            }}
          >
            <StartAppSvg
              // @ts-ignore
              className={`
              ${s['srat-app-icon']} ${inspectionTaskStatus === InspectionTaskStatusEnum.inspectioning ? s.running : ''}
            `}
            />
            发起架构巡检
          </Button>
        </div>
      )}
      onClose={() => {
        dispatch(
          changeRiskPanelData({
            visible: false,
          }),
        );
      }}
    >
      <p className={s.desc}>
        上次巡检时间：
        <span className={s.black}>{nodeTaskRiskInfo.LastScanAt}</span>
      </p>
      <div style={{ marginTop: 50 }} />
      <Tabs tabs={tabs} className={s.tabs}>
        {tabs.map((tab) => (
          <TabPanel id={tab.id} key={tab.id}>
            <p style={{ marginBottom: 10 }}>
              可搜索架构图存在的风险项
            </p>
            <TagSearchBox
              attributes={attributes as any}
              minWidth={570}
              value={searchBoxValue ?? []}
              onChange={(value) => {
                // 检查是否有无效的搜索项（没有选择搜索类型）
                const invalidItems = value?.filter((x) => !x?.attr?.key);

                if (invalidItems?.length > 0) {
                  // 处理无效项，将其默认设置为实例ID搜索
                  const processedValue = value?.map((item) => {
                    if (!item?.attr?.key) {
                      return {
                        ...item,
                        attr: {
                          key: 'InsId',
                          name: '实例ID',
                        },
                      };
                    }
                    return item;
                  });
                  const uniqueValue = deduplicateSearchItems(processedValue);
                  dispatch(
                    changeRiskPanelData({
                      searchBoxValue: uniqueValue,
                    }),
                  );
                } else {
                  dispatch(
                    changeRiskPanelData({
                      searchBoxValue: value,
                    }),
                  );
                }
              }}
              hideHelp
              onSearchButtonClick={onSearch}
              onClearButtonClick={() => {
                dispatch(
                  changeRiskPanelData({
                    searchBoxValue: [],
                  }),
                );
              }}
            />
          </TabPanel>
        ))}
      </Tabs>
      <div className={s.collapse}>
        <Child
          strategies={strategies}
          onIgnore={onIgnore as any}
          loading={archScanRiskItemsLoading}
          pluginAPI={pluginAPI}
          drawerRef={drawerRef}
        />
      </div>
      <IgnoreModal
        visible={ignoreState.visible}
        submitLoading={ignoreState.submitLoading}
        onClose={() => {
          setIgnoreState((prev) => ({
            ...prev,
            visible: false,
            submitLoading: false,
          }));
        }}
        onSubmit={(reason) => {
          setIgnoreState((prev) => ({
            ...prev,
            submitLoading: true,
          }));
          updateInstanceToIgnoredStatus(reason);
        }}
      />
    </Drawer>
  );
}
