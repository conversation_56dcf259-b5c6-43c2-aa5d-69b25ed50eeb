.drawer {
  :global {
    .sdk-cloud-inspection-drawer__body {
      overflow: hidden !important;
    }
  }
}

.isa {
  color: #000;
  font-size: 14px;
  font-weight: 500;
}

.desc {
  color: var(---, rgba(0, 0, 0, .40));
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-align: left;

  .number {
    color: #000;
    font-family: Inter;
    font-style: normal;
    font-weight: 700;
  }

  .strong {
    color: #006eff;
  }

  .hover {
    &:hover {
      cursor: pointer;
    }
  }

  .black {
    color: #000;
  }
}

.tabs {
  color: #000;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;

  :global {
    .sdk-cloud-inspection-tabs__tabpanel {
      box-shadow: 0 2px 4px -4px rgba(0, 0, 0, .1);
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.running {
  animation: rotate 2s linear infinite;
}

.drawer-footer-container {
  display: flex;
  align-items: center;
  justify-content: center;

  .srat-app-icon {
    margin-right: 5px;
    vertical-align: sub;

    :global {
      path {
        fill: #fff !important;
      }
    }
  }
}