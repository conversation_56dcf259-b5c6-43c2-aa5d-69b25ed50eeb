.hover {
  &:hover {
    cursor: pointer;
  }
}

.levelDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.levelDot2 {
  background-color: #ff7200;
}

.levelDot3 {
  background-color: #e54545;
}

.levelDot0 {
  background-color: #dee1e6;
}

.level2 {
  padding-left: 10px;

  &::before {
    position: absolute;
    top: 10%; /* 控制开始位置 */
    left: 0;
    width: 5px;
    height: 80%; /* 控制高度 */
    background-color: #ff7200;
    content: '';
  }

  :global {
    .sdk-cloud-inspection-accordion__header {
      border-left-color: #ff7200 !important;
    }
  }
}

.level3 {
  padding-left: 10px;

  &::before {
    position: absolute;
    top: 10%; /* 控制开始位置 */
    left: 0;
    width: 5px;
    height: 80%; /* 控制高度 */
    background-color: #e54545;
    content: '';
  }

  :global {
    .sdk-cloud-inspection-accordion__header {
      border-left-color: #e54545 !important;
    }
  }
}

.level0 {
  padding-left: 10px;

  &::before {
    position: absolute;
    top: 10%; /* 控制开始位置 */
    left: 0;
    width: 5px;
    height: 80%; /* 控制高度 */
    background-color: #dee1e6;
    content: '';
  }

  :global {
    .sdk-cloud-inspection-accordion__header {
      border-left-color: #dee1e6 !important;
    }
  }
}

.table {
  :global {
    .sdk-cloud-inspection-table__tr--blank-new {
      td {
        display: table-cell;
        width: 100%;

        &>span {
          width: 100% !important;
        }
      }
    }
  }
}

.grey {
  opacity: .4;
}

.unlocation {
  display: flex;
  overflow: hidden;
  width: 60px;
  height: 22px;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  border: 1px solid #006eff;
  background-color: #fff;
  color: #006eff;
  font-size: 12px;
  transform: scale(.85);
}

.location {
  display: flex;
  overflow: hidden;
  width: 60px;
  height: 22px;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  border: 1px solid #006eff;
  background-color: #006eff;
  color: #fff;
  font-size: 12px;
  transform: scale(.85);

  svg {
    margin-right: 4px;
    transform: scale(.7);
  }
}

.operationHover {
  &:hover {
    cursor: pointer;
  }
}

.operationBtn {
  color: #006eff;

  /* Body-R */
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}

.operation {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.kefu {
  display: flex;
  align-items: center;
  justify-content: right;
  margin-top: 10px;
  margin-right: 10px;

  &:hover {
    cursor: pointer;
  }

  span {
    color: #000;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    text-align: center;

    &:hover {
      cursor: pointer;
    }
  }
}

.riskCollapse {
  position: relative;
  display: flex;
  height: 32px;
  align-items: center;
  justify-content: space-between;
  padding-right: 15px;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 32px;

  &:not(:first-child) {
    margin-top: 15px;
  }

  &:hover {
    background-color: #f5f5f5;
  }

  :global {
    .sdk-cloud-inspection-accordion__header-title {
      font-weight: 500;
    }

    .sdk-cloud-inspection-accordion__header-title {
      display: inline-block;
    }

    .sdk-cloud-inspection-accordion__body {
      padding: 0 10px;
    }

    .sdk-cloud-inspection-accordion__header {
      position: relative;
      display: flex;
      height: 30px;
      align-items: center;
      justify-content: space-between;
      padding: 0 5px;
      border-left: 5px solid #fff;
      line-height: 30px;

      &:hover {
        background-color: rgba(245,245,245,1);
      }

      // svg {
      //   display: none;
      // }
    }
  }


}

.strong {
  font-weight: 600;
}

.advice {
  padding: 7px;
  border: 1px solid #7fb7ff;
  border-radius: 5px;
  background: var(----5, #d5e7ff);
}