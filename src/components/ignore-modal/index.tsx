import React from 'react';
import {
  Modal, Button, Form, Radio, Input,
} from '@tencent/tea-component';
import { useForm, Controller } from 'react-hook-form';
import s from './index.module.scss';

interface IIgnoreModalProps {
  visible: boolean;
  showSubTips?: boolean;
  submitLoading: boolean;
  onClose: () => void;
  onSubmit: (reason: string) => void;
}

export default function IgnoreModal(props: IIgnoreModalProps): React.ReactElement {
  const {
    visible,
    showSubTips = true,
    submitLoading,
    onClose,
    onSubmit,
  } = props;
  const {
    control,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm({ mode: 'all' });

  const processingReasonWatch = watch('processingReason');

  const getStatus = (fieldState: any) => {
    if (fieldState?.error?.message) {
      return 'error';
    }
    if (!fieldState.isDirty) {
      return undefined;
    }
    return fieldState.invalid ? 'error' : 'success';
  };

  return (
    <Modal
      visible={visible}
      caption="忽略资源风险"
      size={600}
      onClose={() => {
        onClose();
      }}
    >
      <Modal.Body>
        <div className={s['ignore-tips-1']}>
          请确认该资源风险可以忽略，避免因风险隐患导致服务不可用。
        </div>
        <div className={s['ignore-tips-2']}>
          请您反馈忽略的原因，我们将持续优化巡检策略。
        </div>
        {
          showSubTips && (
          <div className={s['ignore-tips-3']}>
            *忽略该风险项后，资源实例及其他风险项将移入“已认领”或“无风险”
          </div>
          )
        }
        <Form
          style={{ marginTop: 20 }}
          layout="fixed"
          fixedLabelWidth={50}
        >
          <Controller
            name="processingReason"
            control={control}
            rules={{
              validate: (value) => (!value ? '请选择处理原因' : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                required
                label="处理原因"
                className={s['ignore-reason']}
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.User?.message as any}
              >
                <Radio.Group
                  {...field}
                  layout="column"
                >
                  <Radio name="对业务没有实际影响，无需处理">对业务没有实际影响，无需处理</Radio>
                  <Radio name="不关注该类风险">不关注该类风险</Radio>
                  <Radio name="我认为该风险的判断逻辑不合理">我认为该风险的判断逻辑不合理</Radio>
                  <Radio name="其他原因">其他原因</Radio>
                </Radio.Group>
              </Form.Item>
            )}
          />
          {
            processingReasonWatch === '其他原因' && (
              <Controller
                name="otherReasons"
                control={control}
                rules={{
                  validate: (value) => (!value ? '请填写具体原因' : undefined),
                }}
                render={({ field, fieldState }) => (
                  <Form.Item
                    required
                    label="处理原因"
                    status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                    message={errors.otherReasons?.message as any}
                  >
                    <Input
                      maxLength={255}
                      style={{ width: 450 }}
                      {...field}
                      placeholder="请在此填写具体原因，我们会持续改进。"
                    />
                  </Form.Item>
                )}
              />
            )
          }
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          loading={submitLoading}
          type="primary"
          // eslint-disable-next-line @typescript-eslint/no-misused-promises
          onClick={handleSubmit((values) => {
            const { processingReason, otherReasons } = values;
            if (processingReason === '其他原因') {
              onSubmit(otherReasons);
            } else {
              onSubmit(processingReason);
            }
          })}
        >
          确定
        </Button>
        <Button type="weak" onClick={onClose}>
          取消
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
