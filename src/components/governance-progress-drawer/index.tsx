import React from 'react';
import {
  changeGovernanceProgressDrawerState,
  useGovernanceProgressDrawerStateSelector,
} from '@src/store/governance-progress-drawer';
import { useDispatch } from 'react-redux';
import ProgressMain from '@src/components/governance-progress-drawer/components/progress-main';
import ProgressFooter from '@src/components/governance-progress-drawer/components/progress-footer';
import { RiskProgressEnum } from '@src/constant/index';
import InspectionDrawer, { TabEnum } from '../inspection-drawer';

interface IGovernanceProgressDrawerProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

export default function GovernanceProgressDrawer(props: IGovernanceProgressDrawerProps): React.ReactElement {
  const {
    pluginAPI,
  } = props;
  const { visible, progressTab, activeTabId } = useGovernanceProgressDrawerStateSelector();
  const dispatch = useDispatch();

  return (
    <InspectionDrawer
      title={`治理进展${activeTabId === RiskProgressEnum.safeCenter ? ' - 安全中心' : ''}`}
      pluginAPI={pluginAPI}
      drawerVisible={visible}
      nowTab={progressTab}
      onClose={() => {
        dispatch(changeGovernanceProgressDrawerState({
          visible: false,
          riskTableFilter: [],
          riskTablePage: 1,
          progressTab: TabEnum.left,
        }));
      }}
      onTabChange={
        (tab) => {
          dispatch(changeGovernanceProgressDrawerState({
            progressTab: tab,
          }));
        }
      }
      footer={progressTab === TabEnum.left && <ProgressFooter />}
    >
      <ProgressMain />
    </InspectionDrawer>
  );
}
