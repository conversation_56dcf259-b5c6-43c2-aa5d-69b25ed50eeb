import React, {
  useState, useRef, useEffect,
} from 'react';
import {
  LoadingTip,
  Select,
  Button,
  message,
} from '@tencent/tea-component';
import {
  map, clone, find, filter, includes, isEmpty, cloneDeep,
} from 'lodash';
import PlusSvgIcon from '@src/assets/svg-component/plus.svg';
import CloseSvgIcon from '@src/assets/svg-component/close.svg';
import { getTagKeysHandle, getTagValuesHandle } from '@src/service/common-service/final-request';
import { useGlobalSelector } from '@src/store/global/index';
import s from './index.module.scss';

interface ITagSelectProps {
  region?: string;
  setPreviewParams?: (data) => void;
}

const defaultTags = {
  tags: [
    { TagSlice: [] },
  ],
};

export default function TagPicker(props: ITagSelectProps): React.ReactElement {
  const {
    setPreviewParams,
  } = props;
  const { pluginPropsData } = useGlobalSelector();
  const [currPreviewParams, setCurrPreviewParams] = useState<Record<string, any>>(cloneDeep(defaultTags));
  const { tags } = currPreviewParams;
  const keyOptions = useRef([]);
  const valueOptions = useRef([]);
  const [keyToken, setKeyToken] = useState('');
  const [valueToken, setValueToken] = useState('');
  const [currentTagKey, setCurrentTagKey] = useState('');
  const [tagKeyLoading, setTagKeyLoading] = useState(false);
  const [tagValueLoading, setTagValueLoading] = useState(false);
  // 初始化tag组件的时候同步搜索条件
  useEffect(() => {
    fetchTagKeysDataHandle();
  }, []);

  useEffect(() => {
    fetchTagValuesDataHandle(currentTagKey, valueToken);
  }, [valueToken, currentTagKey]);

  const setTags = (tags) => {
    setCurrPreviewParams((last) => ({
      ...last,
      tags,
    }));
  };

  const handleTagPicker = () => {
    let { tags } = currPreviewParams || {};
    if (tags.length > 1 && !tags[tags.length - 1]?.TagSlice[0]?.Value) {
      message.warning({ content: '已选择标签不完整' });
      return;
    }
    if (tags.length === 1) {
      const { Key: key, Value: value } = tags[tags.length - 1]?.TagSlice[0] || {};
      if (key && isEmpty(value)) {
        message.warning({ content: '已选择标签不完整' });
        return;
      }
    }
    tags = tags?.filter((item) => item?.TagSlice?.length);
    setPreviewParams(tags);
  };

  const fetchTagKeysDataHandle = () => {
    setTagKeyLoading(true);
    getTagKeysHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          Language: 'zh-CN',
          MaxResults: 1000,
          PaginationToken: keyToken,
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        const data = rs.TagKeys ?? [];
        const token = rs.PaginationToken ?? '';
        const curKeyList = data.map((v) => ({
          text: v,
          value: v,
        }));
        keyOptions.current = curKeyList;
        if (token) {
          setKeyToken(token);
        }
      }
    }).finally(() => {
      setTagKeyLoading(false);
    });
  };

  const fetchTagValuesDataHandle = (key, token) => {
    setTagValueLoading(true);
    getTagValuesHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          Language: 'zh-CN',
          MaxResults: 1000,
          PaginationToken: token,
          TagKeys: [key],
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        const data = rs.Tags ?? [];
        const token = rs.PaginationToken ?? '';
        const curValueList = data.map((v) => ({
          text: v.TagValue,
          value: v.TagValue,
        }));
        valueOptions.current = curValueList;
        if (token) {
          setValueToken(token);
        }
      }
    }).finally(() => {
      setTagValueLoading(false);
    });
  };

  /**
   * 删除标签功能函数.
   *
   * @param {object} item - 待删除的标签
   * @return {void}
   */
  const handleTagDelete = (item) => {
    const temp = clone(tags);
    setTags(filter(temp, (i) => i.sortIndex !== item.sortIndex));
  };

  /**
   * 标签键改变功能函数.
   *
   * @param {any} value - The new value for the tag key.
   * @param {number} index - The index of the tag to be updated.
   */
  const handleTagKeyChange = (value, index) => {
    const newLists = clone(tags);
    map(newLists, (item) => {
      if (item.sortIndex === index) {
        // eslint-disable-next-line no-param-reassign
        item.TagSlice = [{ Key: value, Value: '' }];
      }
    });
    setTags(newLists);
  };

  /**
   * 标签值改变函数.
   *
   * @param {any} value - The new value for the tag.
   * @param {number} index - The index of the tag to update.
   * @return {void}
   */
  const handleTagValueChange = (value, index) => {
    const newLists = clone(tags);
    map(newLists, (item) => {
      if (item.sortIndex === index) {
        // eslint-disable-next-line no-param-reassign
        item.TagSlice = [{ Key: item.TagSlice[0]?.Key || '', Value: [value] }];
      }
    });
    setTags(newLists);
  };

  /**
   * 新增一个tag.
   *
   * @return {void}
   */
  const handleNewTagCreate = () => {
    const newSelectTags = clone(tags);
    newSelectTags.push({
      sortIndex: +new Date(),
      TagSlice: [],
    });
    setTags(newSelectTags);
  };

  /**
   * 重置Tag列表.
   *
   * @return {void}
   */
  const handleTagReset = () => {
    const newSelectTags = clone(tags);
    const selectTags = map(newSelectTags, (item) => ({ ...item, TagSlice: [] }));
    setTags([selectTags[0]]);
  };

  return (
    <div
      className={s['tag-select-picker']}
    >
      <div className={s['picker-content']}>
        <div className={s['picker-content-hd']}>标签</div>
        <div className={s['tag-select-box']}>
          {
            tags?.map((item) => {
              const { Key: key = '', Value: value = '' } = item.TagSlice[0] || {};
              let options = [];
              const tag = find(tags, (tag) => tag.sortIndex === item.sortIndex) || {};
              if (tagValueLoading) {
                options = [];
              } else {
                options = valueOptions.current.length
                  ? valueOptions.current
                  : [{ text: value, value }];
              }
              const selectKeys = [];
              map(tags ?? [], (item) => {
                if (item?.TagSlice[0]?.Key) {
                  selectKeys.push(item?.TagSlice[0]?.Key);
                }
              });
              return (
                <div key={item} className={s['tag-select-item']}>
                  <span
                    onKeyDown={(e) => e.stopPropagation()}
                    onClick={(e) => e.stopPropagation()}
                    className={s['tag-select-key']}
                  >
                    <Select
                      popupContainer={() => document.getElementById('tag-render-overlay')}
                      searchable
                      size="s"
                      appearance="button"
                      options={
                        keyOptions.current.length
                          ? keyOptions.current.map((item) => ({
                            ...item,
                            disabled: includes(selectKeys, item.text),
                          }))
                          : [{ text: key, value: key }]
                      }
                      value={key}
                      onChange={(value) => handleTagKeyChange(value, item.sortIndex)}
                      bottomTips={tagKeyLoading && <LoadingTip />}
                      tips={
                        <Button
                          type="link"
                          onClick={() => {
                            keyOptions.current = [];
                            fetchTagKeysDataHandle();
                          }}
                        >
                          重新加载
                        </Button>
                      }
                    />
                  </span>
                  <span> : </span>
                  <span
                    onKeyDown={(e) => e.stopPropagation()}
                    className={s['tag-select-value']}
                  >
                    <Select
                      popupContainer={() => document.getElementById('tag-render-overlay')}
                      searchable
                      size="s"
                      appearance="button"
                      disabled={!tag?.TagSlice?.[0]?.Key}
                      onOpen={() => {
                        valueOptions.current = [];
                        setCurrentTagKey(tag?.TagSlice[0]?.Key);
                      }}
                      options={options}
                      value={value?.[0]}
                      onChange={(value) => handleTagValueChange(value, item.sortIndex)}
                      bottomTips={tagValueLoading && <LoadingTip />}
                      tips={
                        <Button
                          type="link"
                          style={{ marginLeft: 10 }}
                          onClick={() => {
                            valueOptions.current = [];
                            fetchTagValuesDataHandle(currentTagKey, valueToken);
                          }}
                        >
                          重新加载
                        </Button>
                      }
                    />
                  </span>
                  {
                    tags?.length > 1
                    && <div
                      onClick={() => handleTagDelete(item)}
                      className={s['tag-select-delete']}
                    >
                      <CloseSvgIcon />
                    </div>
                  }
                </div>
              );
            })
          }
          <div className={s['tag-select-opt']}>
            <PlusSvgIcon
              // @ts-ignore
              className={!tags[tags.length - 1]?.TagSlice[0]?.Value ? s['plus-icon'] : s['plus-icon-active']}
            />
            <Button type="link" onClick={handleNewTagCreate} disabled={!tags[tags.length - 1]?.TagSlice[0]?.Value}>
              添加
            </Button>
          </div>
        </div>
      </div>
      <div className={s['picker-footer']}>
        <Button type="primary" onClick={handleTagPicker}>
          确认
        </Button>
        <Button type="weak" className={s['tag-select-opt-btn']} onClick={handleTagReset}>
          重置
        </Button>
      </div>
    </div>
  );
}
