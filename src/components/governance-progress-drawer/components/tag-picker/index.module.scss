.tag-select-picker {
  padding: 20px;

  .picker-content {
    display: flex;

    .picker-content-hd {
      padding-top: 16px;
      margin-right: 20px;
      color: #888;
    }

    .tag-select-box {
      .tag-select-item {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding-top: 10px;
    
        .tag-select-key {
          margin-right: 8px;
        }
    
        .tag-select-value {
          margin-left: 8px;
        }

        .tag-select-delete {
          margin-left: 6px;
        }

        .select-option-reload {
          margin: 6px 10px;
        }
    
        :global {
          .tea-icon-arrowdown {
            top: 6px;
          }
        }
      }
    
      .tag-select-opt {
        display: flex;
        align-items: center;
        margin-top: 8px;
        
        .plus-icon-active {
          path {
            fill: #006eff;
          }
        }
      }
    }
  }

  .picker-footer {
    margin-top: 19px;

    .tag-select-opt-btn {
      margin-left: 10px;
    }
  }
}

