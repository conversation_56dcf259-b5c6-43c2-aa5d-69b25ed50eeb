import React, {
  useState, useCallback, useEffect, useRef,
} from 'react';
import {
  Input,
  Tooltip,
  Text,
  PopConfirm,
  Button,
} from '@tencent/tea-component';
import { message, Textarea, Loading } from 'tdesign-react';
import CollapseUp from '@src/assets/svg-component/collapse-up.svg';
import CollapseDown from '@src/assets/svg-component/collapse-down.svg';
import Delete from '@src/assets/svg-component/delete.svg';
import Check from '@src/assets/svg-component/check.svg';
import Edit from '@src/assets/svg-component/edit.svg';
import Collect from '@src/assets/svg-component/collect.svg';
import { EnvEnum } from '@src/constant/index';
import {
  getUrlParam,
  deleteUrlParam,
} from '@src/utils/index';
import {
  updateRiskManageSubjectHandle,
  deleteRiskManageSubjectHandle,
  describeArchSvgDataHandle,
} from '@src/service/governance-progress-drawer/final-request';
import ArchSvgComponent from '@src/components/arch-svg-component';
import { SourceEnum } from '../../constants';

import ThemeItem, { ITopicInstanceTableRef } from '../topic-instance-table';
import s from './index.module.scss';

interface IProps {
  style?: React.CSSProperties;
  title?: string;
  description?: string;
  defaultExpanded?: boolean;
  defaultEditing?: boolean;
  id?: number;
  tempId?: string;
  index: number;
  onDelete?: (index: number) => void;
  onOpenDrawer?: (index: number, title: string) => void;
  pluginPropsData: any;
  source: SourceEnum;
  version?: number;
  onSaved?: (
    savedTempId?: string,
    updatedData?: { id: number; title: string; description: string }
  ) => void;
  editingState?: {
    isEditing: boolean;
    editTitle: string;
    editDescription: string;
  };
  onEditingChange?: (itemKey: string | number, editingState: any) => void;
}

/**
 * 自定主题折叠项组件
 * @param props - 组件属性
 * @param props.title - 主题标题
 * @param props.description - 主题描述
 * @param props.defaultExpanded - 默认是否展开
 * @param props.defaultEditing - 默认是否处于编辑状态
 * @param props.onDelete - 删除回调函数
 * @returns React.ReactElement
 */
export default function CollapseTopicTab(props: IProps): React.ReactElement {
  const {
    style,
    title = '',
    description = '',
    source,
    // defaultExpanded = false,
    defaultEditing = false,
    onDelete,
    id,
    tempId,
    index,
    onOpenDrawer,
    pluginPropsData,
    onSaved = () => {},
    editingState,
    onEditingChange,
    version,
  } = props;
  const subjectIdUrl = getUrlParam('subjectIdUrl');
  const itemKey = id || tempId;
  const [svg, setSvg] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [titleError, setTitleError] = useState(false);
  const [descriptionError, setDescriptionError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const topicInstanceTableRef = useRef<ITopicInstanceTableRef>(null);

  // 使用父组件传递的编辑状态，如果没有则使用本地状态
  const isEditing = editingState?.isEditing ?? defaultEditing;
  const editTitle = editingState?.editTitle ?? title ?? '';
  const editDescription = editingState?.editDescription ?? description ?? '';
  const userName = pluginPropsData?.env === EnvEnum.ISA
    ? pluginPropsData.userName
    : pluginPropsData.userName?.slice(
      0,
      pluginPropsData.userName.lastIndexOf('@'),
    ) ?? '';

  const toggleExpanded = () => {
    if (!tempId) {
      setIsExpanded(!isExpanded);
    }
  };

  const getValidString = (str: string) => str?.replace(/^'|'$/g, '') ?? '';

  const handleEdit = () => {
    if (onEditingChange && itemKey) {
      onEditingChange(itemKey, {
        isEditing: true,
        editTitle: title ?? '',
        editDescription: description ?? '',
      });
    }
  };

  const updateEditingState = (newTitle: string, newDescription: string) => {
    // 清除标题错误状态（当用户开始输入时）
    if (titleError && newTitle.trim()) {
      setTitleError(false);
    }

    // 清除描述错误状态（当用户开始输入时）
    if (descriptionError && newDescription.trim()) {
      setDescriptionError(false);
    }

    if (onEditingChange && itemKey) {
      onEditingChange(itemKey, {
        isEditing: true,
        editTitle: newTitle,
        editDescription: newDescription,
      });
    }
  };

  const handleSave = useCallback(() => {
    // 验证主题名称（必填且需要trim）
    const trimmedTitle = editTitle.trim();
    const trimmedDescription = editDescription.trim();

    let hasError = false;

    // 验证主题名称
    if (!trimmedTitle) {
      setTitleError(true);
      message.error('请输入主题名称');
      hasError = true;
    } else if (trimmedTitle.length > 30) {
      setTitleError(true);
      message.error('主题名称不能超过30个字符');
      hasError = true;
    } else {
      setTitleError(false);
    }

    // 验证描述长度
    if (trimmedDescription.length > 200) {
      setDescriptionError(true);
      message.error('主题描述不能超过200个字符');
      hasError = true;
    } else {
      setDescriptionError(false);
    }

    // 如果有错误，停止执行
    if (hasError) {
      return;
    }

    // 检查内容是否有变化（使用trim后的值进行比较）
    const hasContentChanged = trimmedTitle !== (title ?? '')
      || trimmedDescription !== (description ?? '');

    // 如果内容没有变化，只需要清除编辑状态
    if (!hasContentChanged) {
      // 清除子组件中未保存的实例ID
      topicInstanceTableRef.current?.clearUnsavedInstance();

      if (onEditingChange && itemKey) {
        onEditingChange(itemKey, null);
      }
      return;
    }

    setLoading(true);
    updateRiskManageSubjectHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          ArchId: pluginPropsData.archInfo.archId,
          Title: trimmedTitle,
          Description: trimmedDescription,
          ...(id ? { Id: id } : {}),
          Author: userName,
          Source: !id ? SourceEnum.CUSTOM : source || SourceEnum.AGENT,
        },
      },
      uin: pluginPropsData.uin,
    })
      .then((res) => {
        if (res) {
          message.success({ content: !id ? '添加成功' : '编辑成功' });

          // 清除子组件中未保存的实例ID
          topicInstanceTableRef.current?.clearUnsavedInstance();

          // 清除编辑状态
          if (onEditingChange && itemKey) {
            onEditingChange(itemKey, null);
          }
          // 如果是新增的临时项目，传递 tempId；如果是编辑已有项目，传递更新后的数据
          const savedTempId = !id ? tempId : undefined;
          const updatedData = id
            ? { id, title: trimmedTitle, description: trimmedDescription }
            : undefined;
          onSaved?.(savedTempId, updatedData);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [
    editTitle,
    editDescription,
    title,
    description,
    id,
    tempId,
    itemKey,
    onEditingChange,
    onSaved,
    pluginPropsData,
    source,
    userName,
  ]);

  // 删除治理主题的示例函数
  const handleDeleteRiskManageSubject = useCallback(
    async (subjectId: number, archId: string) => {
      setLoading(true);
      try {
        const result = await deleteRiskManageSubjectHandle({
          env: pluginPropsData.env,
          apiParams: {
            data: {
              Id: subjectId,
              ArchId: archId,
              Username: userName,
            },
          },
          uin: pluginPropsData.uin,
        });
        if (result) {
          message.success('删除成功');
          onDelete(index);
        }
        setLoading(false);
      } catch (error) {
        console.error('删除失败:', error);
        setLoading(false);
      }
    },
    [index],
  );

  const getSvg = useCallback(async () => {
    try {
      const result = await describeArchSvgDataHandle({
        env: pluginPropsData.env,
        apiParams: {
          data: {
            ArchId: pluginPropsData?.archInfo?.archId,
            VersionId: version,
            IsThreeDimension: true,
          },
        },
        uin: pluginPropsData.uin,
      });
      if (result) {
        setSvg(result.Svg);
      }
    } catch (error) {
      console.error('获取svg失败');
    }
  }, [version]);

  const handleDelete = async () => {
    if (id) {
      await handleDeleteRiskManageSubject(id, pluginPropsData.archInfo.archId);
    } else if (onDelete) {
      onDelete(index);
    }
  };

  useEffect(() => {
    getSvg();
  }, [version]);

  useEffect(() => {
    if (id === +subjectIdUrl) {
      setIsExpanded(true);
      deleteUrlParam('subjectIdUrl');
    }
  }, [subjectIdUrl]);

  return (
    <Loading size="small" loading={loading} showOverlay>
      <div
        style={{ ...style ?? {} }}
        className={s.container}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className={s.item}>
          <div className={s.left}>
            <div style={{ height: '100%' }}>
              <ArchSvgComponent detail={getValidString(svg) || ''} />
            </div>
          </div>
          <div className={s.right}>
            <div className={s.top}>
              <div className={s.row}>
                <div
                  className={isEditing ? s['title-row-edit'] : s['title-row']}
                >
                  <span className={s.tag}>主题</span>
                  {isEditing ? (
                    <Input
                      value={editTitle}
                      onChange={(value) => {
                        updateEditingState(value, editDescription);
                      }}
                      className={`${s['edit-input']} ${
                        titleError ? s['input-error'] : ''
                      }`}
                      placeholder="请输入主题名称"
                    />
                  ) : (
                    // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
                    <p
                      onClick={() => onOpenDrawer(id, title)}
                      className={`${s.pointer} ${s.title}`}
                    >
                      <Tooltip title={title}>
                        <Text
                          verticalAlign="middle"
                          style={{ maxWidth: 400 }}
                          overflow
                        >
                          {title}
                        </Text>
                      </Tooltip>
                    </p>
                  )}
                </div>
                <span
                  className={s.pointer}
                  style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
                >
                  {isEditing ? (
                    <>
                      <span
                        onClick={() => {
                          handleSave();
                        }}
                        className={s['action-icon']}
                      >
                        <Check />
                      </span>
                      <PopConfirm
                        title="确定删除当前主题吗？"
                        // eslint-disable-next-line react/no-unstable-nested-components
                        footer={(close) => (
                          <>
                            <Button
                              type="link"
                              onClick={() => {
                                handleDelete();
                                close();
                              }}
                            >
                              确定
                            </Button>
                            <Button
                              type="text"
                              onClick={() => {
                                close();
                              }}
                            >
                              取消
                            </Button>
                          </>
                        )}
                        placement="top-start"
                      >
                        <span
                          // eslint-disable-next-line @typescript-eslint/no-misused-promises
                          // onClick={() => handleDelete()}
                          className={s['action-icon']}
                          style={{ transform: 'translateY(2px)' }}
                        >
                          <Delete />
                        </span>
                      </PopConfirm>
                    </>
                  ) : (
                    <span
                      onClick={handleEdit}
                      className={s['action-icon']}
                      style={{
                        opacity: isHovered ? 1 : 0,
                      }}
                    >
                      <Edit />
                    </span>
                  )}
                </span>
              </div>
              <div className={s.row} style={{ marginTop: 5 }}>
                <div className={s['description-row']}>
                  <span className={s.tag}>描述</span>
                  {isEditing ? (
                    <Textarea
                      value={editDescription}
                      onChange={(value) => {
                        updateEditingState(editTitle, value);
                      }}
                      className={`${s['edit-textarea']} ${
                        descriptionError ? s['textarea-error'] : ''
                      }`}
                      placeholder="请输入主题描述"
                      autosize={{ minRows: 2, maxRows: 4 }}
                    />
                  ) : (
                    <Tooltip title={description}>
                      <div
                        className={`${s['description-text']} ${s.pointer}`}
                        onClick={() => onOpenDrawer(id, title)}
                      >
                        {description}
                      </div>
                    </Tooltip>
                  )}
                </div>
              </div>
            </div>
            <div className={s.bottom}>
              <div
                className={`${s.row} ${s.pointer}`}
                onClick={toggleExpanded}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  ...(isExpanded ? { backgroundColor: '#D4E3FB' } : {}),
                  ...(isEditing ? { marginTop: 5 } : {}),
                }}
              >
                <div className={!isExpanded ? s.desc : ''} style={{ alignItems: 'center' }}>
                  <span className={s.tag}>
                    <Collect />
                  </span>
                  <p className={s['no-selected']} style={{ marginLeft: 6 }}>
                    主题相关风险实例
                  </p>
                </div>
                {
                  !tempId && (
                    <span
                      style={{
                        display: 'inline-block',
                        transform: 'translateY(2px)',
                      }}
                    >
                      {isExpanded ? <CollapseUp /> : <CollapseDown />}
                    </span>
                  )
                }
              </div>
            </div>
          </div>
        </div>

        {isExpanded && (
          <div className={s.table}>
            <ThemeItem ref={topicInstanceTableRef} isEditing={isEditing} id={id} />
          </div>
        )}
      </div>
    </Loading>
  );
}
