.container {
  margin-top: 10px;
  margin-left: 2px;
  background: #f3f4f7;
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, .20);
}

.desc {
  display: flex;
  width: 450px;
  align-items: center;

  &:hover {
    background-color: #e4e8ec;
  }
}

.no-selected {
  user-select: none;
}

.title {
  width: 390px;
}

.item {
  display: flex;
  padding: 10px;
  margin-bottom: 15px;
  background-color: #fff;

  .pointer {
    &:hover {
      cursor: pointer;
    }
  }

  .left {
    width: 100px;
    height: 100px;
    flex-shrink: 0;
    aspect-ratio: 1/1;
  }

  .right {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    color: #000;

    /* Body-R */
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;

    .tag {
      display: flex;
      width: 24px;
      height: 20px;
      align-items: center;
      justify-content: center;
      padding: 0px 2px;
      border-radius: 3px;
      margin-right: 5px;
      background: var(----5, #d5e7ff);
      gap: 10px;
    }

    .row {
      display: flex;
      justify-content: space-between;

      &>div {
        display: flex;
      }
    }

    .title-row-edit {
      display: flex;
      flex: 1;
      align-items: center;
      margin-right: 10px;

      &:hover {
        border-radius: 5px;
      }

      p {
        margin: 0;
        margin-left: 5px;
      }
    }

    .title-row {
      display: flex;
      flex: 1;
      align-items: center;
      margin-right: 10px;

      &:hover {
        border-radius: 5px;
        // background-color: #eff1f5;
      }

      p {
        margin: 0;
        margin-left: 5px;
      }
    }

    .description-row {
      display: flex;
      flex: 1;
      align-items: flex-start;
      margin-right: 10px;

      p {
        margin: 0;
        margin-left: 5px;
        line-height: 1.4;
      }
    }

    .description-text {
      display: box;
      
      /* stylelint-disable-next-line */
      display: -webkit-box;
      overflow: hidden;
      max-width: 400px;
      margin-left: 5px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-height: 1.4;
      text-overflow: ellipsis;
      word-break: break-word;
    }

    .edit-input {
      max-width: 360px;
      flex: 1;
      margin-left: 10px;
    }

    .input-error {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, .2) !important;
    }

    .edit-textarea {
      max-width: 357px;
      flex: 1;
      margin-left: 5px;
    }

    .textarea-error {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, .2) !important;
    }

    .action-icon {
      display: flex;
      width: 24px;
      height: 24px;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color .2s ease;

      &:hover {
        background-color: rgba(0, 0, 0, .05);
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .table {
    padding: 10px;
    margin-top: 15px;
    background-color: #fff;
  }
}