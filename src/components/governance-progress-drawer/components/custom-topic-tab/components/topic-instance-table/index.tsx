/* eslint-disable jsx-a11y/anchor-is-valid */
import React, {
  useEffect, useState, useRef, useImperativeHandle, forwardRef,
} from 'react';
import {
  Table,
  Justify,
  Button,
  Icon,
  Bubble,
  message,
  // Tag,
  Input,
} from '@tencent/tea-component';
// import { reportEvent } from '@src/utils';
import TableSearchBox from '@src/components/governance-progress-drawer/components/table-search-box';
import { IArchScanRiskInstanceItem } from '@src/service/governance-progress-drawer/index.type';
import IgnoreModal from '@src/components/ignore-modal';
import { useGlobalSelector } from '@src/store/global/index';
import {
  EnvEnum,
  InspectionTaskStatusEnum,
  UpdateInstanceToClaimedOperateEnum,
  RiskLevelEnum,
} from '@src/constant';
import classNames from 'classnames';
import {
  getRiskManageInstanceList,
  updateInstanceToClaimedStatusInMapHandle,
  updateInstanceToIgnoredStatusInMapHandle,
  createSubscriptionEmailV2Handle,
  updateSubscriptionEmailV2Handle,
  createRiskManageInstanceHandle,
} from '@src/service/governance-progress-drawer/final-request';
import useUpdateShapesBar from '@src/hooks/useUpdateShapesBar';
import { useDispatch } from 'react-redux';
import _ from 'lodash';
import InstanceIdCopy from '@src/components/instance-id-copy';
import AssignModal from '@src/components/node-risk-drawer/components/assign-modal';
import {
  changeGovernanceProgressDrawerState,
  useGovernanceProgressDrawerStateSelector,
} from '@src/store/governance-progress-drawer';
import RiskExpandList from '../../../risk-expand-list';
import s from './index.module.scss';

const {
  pageable, selectable, expandable, autotip, scrollable,
} = Table.addons;

enum RiskIntervalEnum {
  'all' = 'all',
  'seven' = '7',
  'fourteen' = '14',
  'thirty' = '30',
}

interface ITopicInstanceTableProps {
  isEditing?: boolean;
  id?: number;
  onAdded?: () => void;
}

export interface ITopicInstanceTableRef {
  clearUnsavedInstance: () => void;
}

/**
 * 主题实例表格组件
 * @param props - 组件属性
 * @param props.isEditing - 是否处于编辑状态
 * @returns
 */
// eslint-disable-next-line max-len
const TopicInstanceTable = forwardRef<ITopicInstanceTableRef, ITopicInstanceTableProps>((props, ref): React.ReactElement => {
  const { isEditing = false, id, onAdded = () => {} } = props;
  const { pluginPropsData, inspectionTaskStatus } = useGlobalSelector();
  const { riskTableFilter, riskTablePage } = useGovernanceProgressDrawerStateSelector();
  const { visible } = useGovernanceProgressDrawerStateSelector();
  const { updateShapesBar } = useUpdateShapesBar();
  const dispatch = useDispatch();

  const [pageSize, setPageSize] = useState(10);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [tableData, setTableData] = useState<IArchScanRiskInstanceItem[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [assignModalLoading, setAssignModalLoading] = useState(false);
  const [ignoreModalLoading, setIgnoreModalLoading] = useState(false);
  const [ignoreModalVisible, setIgnoreModalVisible] = useState(false);
  const selectedKeysRef = useRef<string[]>([]);
  const [riskInterval] = useState<string>(
    RiskIntervalEnum.all,
  );
  const [timeRange] = useState<[any, any]>([null, null]);
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  const [productList, setProductList] = useState<Array<{ key: string; name: string }>>([]);
  const [isAddingInstance, setIsAddingInstance] = useState(false);
  const [newInstanceId, setNewInstanceId] = useState('');
  const [instanceIdError, setInstanceIdError] = useState(false);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    clearUnsavedInstance: () => {
      if (isAddingInstance) {
        setIsAddingInstance(false);
        setNewInstanceId('');
        setInstanceIdError(false);
      }
    },
  }));

  const columns = [
    {
      key: 'InstanceId',
      header: '实例',
      width: 140,
      render: (record: IArchScanRiskInstanceItem | any) => {
        // Handle new instance being added
        if (record.isNewInstance) {
          return (
            <div style={{ width: '100%' }}>
              <Input
                value={newInstanceId}
                onChange={(value) => {
                  setNewInstanceId(value);
                  // 清除错误状态（当用户开始输入时）
                  if (instanceIdError && value.trim()) {
                    setInstanceIdError(false);
                  }
                }}
                placeholder="请输入实例ID"
                style={{
                  width: '100%',
                  borderColor: instanceIdError ? '#ff4d4f' : undefined,
                  boxShadow: instanceIdError ? '0 0 0 2px rgba(255, 77, 79, 0.2)' : undefined,
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSaveNewInstance();
                  } else if (e.key === 'Escape') {
                    handleCancelNewInstance();
                  }
                }}
                autoFocus
              />
            </div>
          );
        }

        return (
          <div className={s['risk-table-instance-column']}>
            {/* {record.HighRiskCount ? (
              <div className={s['high-circle']} />
            ) : (
              <div className={s['mid-circle']} />
            )} */}
            <div
              className={classNames({
                [s['risk-type-div-3']]:
                 record?.Level === RiskLevelEnum.highRisk,
                [s['risk-type-div-2']]:
                 record?.Level === RiskLevelEnum.mediumRisk,
                [s['risk-type-div']]: true,
              })}
            />
            <div
              className={s['risk-table-instance-inner']}
              style={{
                maxWidth: record.IsChange
                  ? 'calc(100% - 40px)'
                  : 'calc(100% - 18px)',
              }}
            >
              <InstanceIdCopy instanceId={record.InstanceId} url={record.InstanceUrl} />
            </div>
            {record.IsChange && (
              <Bubble
                arrowPointAtCenter
                placement="top"
                content="实例风险已解决，建议巡检更新"
              >
                <Icon type="info" style={{ marginLeft: 5 }} />
              </Bubble>
            )}
          </div>
        );
      },
    },
    // {
    //   key: 'Product',
    //   header: '云产品',
    //   width: 150,
    //   render: (record: IArchScanRiskInstanceItem | any) => {
    //     if (record.isNewInstance) {
    //       return '-';
    //     }

    //     const productName = productList?.find(
    //       (item) => item.key === record.Product,
    //     )?.name;
    //     return productName ? (
    //       <Bubble content={productName}>
    //         <Tag theme="primary" className={s['product-tag']}>
    //           {productName}
    //         </Tag>
    //       </Bubble>
    //     ) : (
    //       '-'
    //     );
    //   },
    // },
    {
      key: 'InstanceTags',
      header: '标签',
      render: (record: IArchScanRiskInstanceItem | any) => {
        if (record.isNewInstance) {
          return '-';
        }

        try {
          const tag = record.InstanceTags;
          const tagArray = tag ? JSON.parse(tag) : [];
          if (tagArray.length === 0) {
            return '-';
          }
          return (
            <Bubble
              arrowPointAtCenter
              placement="top"
              content={
                <div>
                  {tagArray.map((tag: any) => (
                    <div key={tag.Key}>
                      <span>
                        {tag.Key}
                        ：
                      </span>
                      <span>{tag.Value}</span>
                    </div>
                  ))}
                </div>
              }
            >
              <Icon type="tag" />
            </Bubble>
          );
        } catch (error) {
          return '暂无';
        }
      },
    },
    {
      key: 'ClaimPerson',
      header: '跟进人',
      width: 90,
      render: (record: IArchScanRiskInstanceItem | any) => {
        if (record.isNewInstance) {
          return '-';
        }
        return <div>{record.ClaimPerson}</div>;
      },
    },
    {
      key: 'Operate',
      header: '操作',
      width: 100,
      render: (record: any) => {
        if (record.isNewInstance) {
          return (
            <>
              <Button
                type="link"
                onClick={handleSaveNewInstance}
                disabled={!newInstanceId.trim()}
              >
                保存
              </Button>
              <Button
                type="link"
                onClick={handleCancelNewInstance}
              >
                取消
              </Button>
            </>
          );
        }

        return (
          <>
            <Button
              type="link"
              onClick={() => {
                // reportEvent(pluginPropsData.env as EnvEnum, {
                //   archId: pluginPropsData.archInfo.archId,
                //   nodeId: record?.NodeId,
                //   nodeName: record?.NodeName,
                //   subUin: pluginPropsData.uin,
                //   subUinName: pluginPropsData.userName,
                //   eventType: 'riskClickInstanceClaimed',
                //   instanceId: record.InstanceId,
                // });
                setSelectedKeys([`${record.InstanceId}_${record.Product}`]);
                selectedKeysRef.current = [
                  `${record.InstanceId}_${record.Product}`,
                ];
                setAssignModalVisible(true);
              }}
            >
              分配
            </Button>
            <Button
              disabled={pluginPropsData?.env === EnvEnum.ISA}
              type="link"
              onClick={() => {
                // reportEvent(pluginPropsData.env as EnvEnum, {
                //   archId: pluginPropsData.archInfo.archId,
                //   nodeId: record?.NodeId,
                //   nodeName: record?.NodeName,
                //   subUin: pluginPropsData.uin,
                //   subUinName: pluginPropsData.userName,
                //   eventType: 'riskClickInstanceIgnore',
                //   instanceId: record.InstanceId,
                // });
                setSelectedKeys([`${record.InstanceId}_${record.Product}`]);
                selectedKeysRef.current = [
                  `${record.InstanceId}_${record.Product}`,
                ];
                setIgnoreModalVisible(true);
              }}
            >
              忽略
            </Button>
          </>
        );
      },
    },
  ];

  const getTableList = () => {
    const filters = riskTableFilter.filter(
      (item: any) => item?.attr?.key !== 'TagList',
    );
    const filtersParams: any[] = [];
    filters.forEach((item: any) => {
      const obj = {
        Name: item.attr.key,
        Values: item.values.map(
          (valueItem: any) => `${valueItem.key || ''}` || `${valueItem.name || ''}`,
        ),
      };
      filtersParams.push(obj);
    });
    let tagListParams: any = [];
    const filtersTag: any = riskTableFilter.find(
      (item) => item?.attr?.key === 'TagList',
    );
    if (filtersTag?.values) {
      tagListParams = filtersTag?.values?.map((valueItem: any) => ({
        Key: valueItem.key,
        Value: valueItem.name,
      }));
    }
    if (riskInterval && riskInterval !== RiskIntervalEnum.all) {
      filtersParams.push({
        Name: 'RiskDays',
        Values: [riskInterval],
      });
    }
    if (timeRange[0] && timeRange[1]) {
      filtersParams.push({
        Name: 'StartDate',
        Values: [timeRange[0]?.format('YYYY-MM-DD')],
      });
      filtersParams.push({
        Name: 'EndDate',
        Values: [timeRange[1]?.format('YYYY-MM-DD')],
      });
    }
    const apiParams = {
      ArchId: pluginPropsData.archInfo.archId,
      Offset: (riskTablePage - 1) * pageSize,
      Limit: pageSize,
      Filters: filtersParams,
      TagList: tagListParams,
      SubjectId: id,
    };
    setLoading(true);
    getRiskManageInstanceList({
      env: pluginPropsData.env,
      apiParams: {
        data: apiParams,
      },
      uin: pluginPropsData.uin,
    })
      .then((res) => {
        if (res) {
          setTableData(res.InstanceItems);
          setTotal(res.Total);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const updateInstanceToClaimedStatus = async (values: any, emailInfo: any) => {
    setAssignModalLoading(true);
    const userInfoArr = values.User?.split(':');
    try {
      await updateInstanceToClaimedStatusInMapHandle({
        env: pluginPropsData.env,
        apiParams: {
          data: {
            MapId: pluginPropsData.archInfo.archId,
            Operate: UpdateInstanceToClaimedOperateEnum.add,
            MapRiskInstanceList:
              tableData
                ?.filter((item: IArchScanRiskInstanceItem) => selectedKeysRef.current?.includes(
                  `${item.InstanceId}_${item.Product}`,
                ))
                ?.map((item: IArchScanRiskInstanceItem) => ({
                  InstanceId: item.InstanceId,
                  Product: item.Product,
                })) ?? [],
            ClaimPerson: userInfoArr[0],
            ClaimUin: userInfoArr[1],
          },
        },
        uin: pluginPropsData.uin,
      });
      if (values.Email) {
        if (emailInfo.Id !== undefined) {
          await updateSubscriptionEmailV2Handle({
            env: pluginPropsData.env,
            apiParams: {
              data: {
                Id: emailInfo.Id,
                SubUin: userInfoArr[1],
                UserName: emailInfo.UserName
                  ? emailInfo.UserName
                  : userInfoArr[0],
                Email: values.Email,
              },
            },
            uin: pluginPropsData.uin,
          });
        } else {
          await createSubscriptionEmailV2Handle({
            env: pluginPropsData.env,
            apiParams: {
              data: {
                SubUin: userInfoArr[1],
                UserName: userInfoArr[0],
                Email: values.Email,
              },
            },
            uin: pluginPropsData.uin,
          });
        }
      }
      setSelectedKeys([]);
      message.success({ content: '分配成功' });
      getTableList();
      setAssignModalVisible(false);
      setAssignModalLoading(false);
    } catch (err) {
      setAssignModalLoading(false);
    }
  };

  const handleAddInstance = () => {
    setIsAddingInstance(true);
    setNewInstanceId('');
  };

  const handleSaveNewInstance = () => {
    const trimmedInstanceId = newInstanceId.trim();

    // 验证实例ID
    if (!trimmedInstanceId) {
      setInstanceIdError(true);
      message.error({ content: '请输入实例ID' });
      return;
    }

    if (trimmedInstanceId.length > 200) {
      setInstanceIdError(true);
      message.error({ content: '实例ID不能超过200个字符' });
      return;
    }

    // Check if instance ID already exists
    const existingInstance = tableData.find((item) => item.InstanceId === trimmedInstanceId);
    if (existingInstance) {
      setInstanceIdError(true);
      message.error({ content: '实例ID已存在' });
      return;
    }

    // 清除错误状态
    setInstanceIdError(false);

    // Here you can add logic to save the new instance to backend
    // For now, we'll just add it to the local table data
    const newInstance: IArchScanRiskInstanceItem = {
      InstanceId: trimmedInstanceId,
    };
    createRiskManageInstanceHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          ArchId: pluginPropsData.archInfo.archId,
          SubjectId: id,
          InstanceId: trimmedInstanceId,
        },
      },
      uin: pluginPropsData.uin,
    }).then((res) => {
      if (res) {
        setTableData((prev) => [newInstance, ...prev]);
        setTotal((prev) => prev + 1);
        setIsAddingInstance(false);
        setNewInstanceId('');
        setInstanceIdError(false);
        message.success({ content: '实例添加成功' });
        onAdded?.();
        getTableList();
      }
    });
  };

  const handleCancelNewInstance = () => {
    setIsAddingInstance(false);
    setNewInstanceId('');
    setInstanceIdError(false);
  };

  const updateInstanceToIgnoredStatus = async (reason: string) => {
    setIgnoreModalLoading(true);
    updateInstanceToIgnoredStatusInMapHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          MapId: pluginPropsData.archInfo.archId,
          Operate: UpdateInstanceToClaimedOperateEnum.add,
          MapRiskInstanceList:
            tableData
              ?.filter((item: IArchScanRiskInstanceItem) => selectedKeysRef.current?.includes(
                `${item.InstanceId}_${item.Product}`,
              ))
              ?.map((item: IArchScanRiskInstanceItem) => ({
                InstanceId: item.InstanceId,
                Product: item.Product,
              })) ?? [],
          Reason: reason,
          Person:
            pluginPropsData?.env === EnvEnum.ISA
              ? pluginPropsData.userName
              : pluginPropsData.userName?.slice(
                0,
                pluginPropsData.userName.lastIndexOf('@'),
              ) ?? '',
        },
      },
      uin: pluginPropsData.uin,
    })
      .then((rs) => {
        if (rs) {
          setSelectedKeys([]);
          message.success({ content: '忽略成功' });
          getTableList();
          setIgnoreModalVisible(false);
          // 忽略成功后需要重新更新角标
          updateShapesBar();
        }
      })
      .finally(() => {
        setIgnoreModalLoading(false);
      });
  };

  useEffect(() => {
    if (pluginPropsData.archInfo.archId && visible) {
      setTimeout(() => {
        setExpandedKeys([]);
        getTableList();
      }, 0);
    }
  }, [
    pluginPropsData,
    riskTablePage,
    pageSize,
    riskTableFilter,
    riskInterval,
    timeRange,
    visible,
  ]);

  useEffect(() => {
    if (inspectionTaskStatus === InspectionTaskStatusEnum.inspectionCompleted) {
      setExpandedKeys([]);
      getTableList();
    }
  }, [inspectionTaskStatus]);

  return (
    <div style={{ background: '#fff', paddingBottom: 10 }}>
      <Table.ActionPanel style={{ padding: '10px' }}>
        <Justify
          className={s['risk-table-justify']}
          left={
            <div style={{ width: 252 }}>
              <Button
                disabled={selectedKeys.length === 0}
                type="primary"
                onClick={() => {
                  // reportEvent(pluginPropsData.env as EnvEnum, {
                  //   archId: pluginPropsData.archInfo.archId,
                  //   subUin: pluginPropsData.uin,
                  //   subUinName: pluginPropsData.userName,
                  //   eventType: 'riskClickBatchInstanceClaimed',
                  // });
                  setAssignModalVisible(true);
                }}
              >
                分配
              </Button>
              <Button
                disabled={
                  selectedKeys.length === 0
                  || pluginPropsData?.env === EnvEnum.ISA
                }
                onClick={() => {
                  // reportEvent(pluginPropsData.env as EnvEnum, {
                  //   archId: pluginPropsData.archInfo.archId,
                  //   subUin: pluginPropsData.uin,
                  //   subUinName: pluginPropsData.userName,
                  //   eventType: 'riskClickBatchInstanceIgnore',
                  // });
                  setIgnoreModalVisible(true);
                }}
              >
                忽略
              </Button>

              {isEditing && (
                <Button
                  onClick={handleAddInstance}
                  disabled={isAddingInstance || !id}
                >
                  添加实例
                </Button>
              )}
            </div>
          }
          right={
            <div className={s['right-search-box']}>
              <TableSearchBox
                value={riskTableFilter}
                productListCallback={(val: Array<{ key: string; name: string }>) => {
                  setProductList(val);
                }}
                onChange={(values: any) => {
                  const valuesCopy = _.cloneDeep(values);
                  const valuesCopyFilter = valuesCopy.filter(
                    (item: any) => !!item?.attr,
                  );
                  valuesCopyFilter.forEach((item: any) => {
                    // eslint-disable-next-line no-param-reassign
                    delete item.attr?.render;
                    // eslint-disable-next-line no-param-reassign
                    delete item.attr?.values;
                  });
                  dispatch(
                    changeGovernanceProgressDrawerState({
                      riskTableFilter: valuesCopyFilter,
                      riskTablePage: 1,
                    }),
                  );
                }}
              />
            </div>

          }
        />
      </Table.ActionPanel>
      <Table
        className={s['risk-table']}
        verticalTop
        records={isAddingInstance ? [{ isNewInstance: true, InstanceId: 'new-instance' }, ...tableData] : tableData}
        recordKey={(record) => (record.isNewInstance ? 'new-instance' : `${record.InstanceId}_${record.Product}`)}
        columns={columns}
        bordered
        addons={[
          scrollable({
            maxHeight: 'calc(100vh - 440px)',
          }),
          pageable({
            pageIndex: riskTablePage,
            recordCount: total,
            onPagingChange: (query) => {
              dispatch(
                changeGovernanceProgressDrawerState({
                  riskTablePage: query.pageIndex,
                }),
              );
              setPageSize(query.pageSize);
            },
          }),
          selectable({
            value: selectedKeys,
            onChange: (keys: string[]) => {
              selectedKeysRef.current = keys;
              setSelectedKeys(keys);
            },
            shouldRecordExcludeFromAll: (record: any) => record.isNewInstance,
          }),
          expandable({
            expandedKeys,
            onExpandedKeysChange: (keys: string[], { event }: any) => {
              const lastKey = keys[keys.length - 1];
              event.stopPropagation();
              setExpandedKeys([lastKey]);
              if (lastKey) {
                // reportEvent(pluginPropsData.env as EnvEnum, {
                //   archId: pluginPropsData.archInfo.archId,
                //   subUin: pluginPropsData.uin,
                //   subUinName: pluginPropsData.userName,
                //   eventType: 'riskClickExpandInstanceRisk', // 有风险—展开实例风险
                //   instanceId: lastKey,
                // });
              }
            },
            render(record: IArchScanRiskInstanceItem & {isNewInstance: boolean}) {
              if (record.isNewInstance) {
                return null;
              }
              return (
                <RiskExpandList
                  mapId={pluginPropsData.archInfo.archId}
                  nodeUuid={record.NodeUuid}
                  nodeName={record.NodeName}
                  taskId={record.TaskId}
                  instanceId={record.InstanceId}
                  region={record.Region}
                  // onCustomerServiceClick={(env, params) => {
                  //   reportEvent(env, {
                  //     ...params,
                  //     eventType: 'riskClickAICustomerService', // 有风险—唤起智能客服
                  //   });
                  // }}
                  // onIgnoreClick={(env, params) => {
                  //   reportEvent(env, {
                  //     ...params,
                  //     eventType: 'riskClickIgnoreRisk', // 有风险—忽略风险
                  //   });
                  // }}
                  onSuccess={() => {
                    getTableList();
                  }}
                />
              );
            },
            gapCell: 2,
            shouldRecordExpandable: (record: any) => !record.isNewInstance,
          }),
          autotip({
            isLoading: loading,
          }),
        ]}
      />
      <AssignModal
        visible={assignModalVisible}
        submitLoading={assignModalLoading}
        onClose={() => {
          setAssignModalVisible(false);
        }}
        onSubmit={(user: any, emailInfo: any) => {
          updateInstanceToClaimedStatus(user, emailInfo);
        }}
      />
      <IgnoreModal
        visible={ignoreModalVisible}
        submitLoading={ignoreModalLoading}
        showSubTips={false}
        onClose={() => {
          setIgnoreModalVisible(false);
        }}
        onSubmit={(reason) => {
          updateInstanceToIgnoredStatus(reason);
        }}
      />
    </div>
  );
});

TopicInstanceTable.displayName = 'TopicInstanceTable';

export default TopicInstanceTable;
