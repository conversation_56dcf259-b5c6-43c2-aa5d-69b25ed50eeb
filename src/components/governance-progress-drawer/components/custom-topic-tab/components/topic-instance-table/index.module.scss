.container {
  width: 100%;
}

.risk-type-div {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 10px;
  background-color: #e54545;
}

.risk-type-div-3 {
  background-color: #e54545;
}

.risk-type-div-2 {
  background-color: #ff7200;
}

.risk-table-justify {
  :global(.sdk-cloud-inspection-justify-grid__col--right) {
    width: 400px;
  }
}

.right-search-box {
  width: 320px;

  :global(> div) {
    width: 320px !important;
  }
}

.risk-table-instance-column {
  display: flex;
  overflow: hidden;
  width: 100%;
  align-items: center;

  .high-circle {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    margin-right: 10px;
    background-color: #e54545;
  }

  .mid-circle {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    margin-right: 10px;
    background-color: #ff7200;
  }

  .risk-table-instance-inner {
    max-width: calc(100% - 40px);
  }
}

.risk-table {
  :global(.tr__detailrow) {
    td {
      background-color: #fff !important;
    }
  }
}

.product-tag {
  margin-top: 0!important;
  margin-right: 0!important;

  :global span {
    overflow: hidden!important;
    max-width: 120px!important;
    white-space: nowrap!important;
  }
}