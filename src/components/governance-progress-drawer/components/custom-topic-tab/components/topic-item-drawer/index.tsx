import React, { useEffect, useState, useCallback } from 'react';
import { Drawer, TagSearchBox } from '@tencent/tea-component';
import { useGlobalSelector } from '@src/store/global/index';
import { omit } from 'lodash';
import { getRiskManageSubjectList } from '@src/service/governance-progress-drawer/final-request';
import { AttributesNameEnum } from '../../constants';
import TopicTable from '../topic-table';
import s from './index.module.scss';

interface IProps {
  visible?: boolean;
  onClose?: () => void;
  pluginPropsData: any;
  defaultId?: number;
  defaultTopicTitle?: string;
}

/**
 * 二级抽屉组件
 * @param props - 组件属性
 * @param props.visible - 是否可见属性
 * @param props.onClose - 关闭抽屉方法
 * @returns React.ReactElement
 */
export default function SecondaryDrawer(props: IProps): React.ReactElement {
  const attributes = [
    {
      type: 'input',
      key: AttributesNameEnum.TOPICS,
      name: '主题',
    },
  ];
  const {
    visible, onClose, pluginPropsData, defaultTopicTitle,
  } = props;
  const { inspectionDrawerWidth } = useGlobalSelector();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const DEFAULT_WIDTH = 640;
  const [value, setValue] = useState([]);
  const [query, setQuery] = useState<any>({
    ArchId: pluginPropsData.archInfo.archId,
    Limit: 10,
    Offset: 0,
  });
  const [initialized, setInitialized] = useState(false);
  const onChange = (v) => {
    setValue((last) => {
      if (JSON.stringify(last) === JSON.stringify(v)) {
        setQuery((lastQ) => ({
          ...lastQ,
          hash: `${new Date().getTime()}`,
        }));
      }
      const directInputItem = v.find((k: any) => !k?.attr);
      // 删除场景
      if (v.length < last.length) {
        return v;
      }
      // 已存在关键字场景
      if (
        v.filter(
          (k: any) => k?.attr?.key === AttributesNameEnum.TOPICS || !k?.attr,
        ).length > 1
      ) {
        return last;
      }
      // 直输关键字场景
      if (directInputItem && typeof directInputItem === 'object') {
        (directInputItem as any).attr = {
          type: 'input',
          key: AttributesNameEnum.TOPICS,
          name: '主题',
        };
        return v;
      }
      return v;
    });
  };

  const fetchRiskManageSubjectList = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getRiskManageSubjectList({
        env: pluginPropsData.env,
        apiParams: {
          data: omit(query, 'hash') as any,
        },
        uin: pluginPropsData.uin,
      });
      if (res) {
        const serverData = res?.Items ?? [];
        setData(serverData);
        setTotal(res?.Total ?? 0);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  }, [pluginPropsData, query]);

  // 初始化默认搜索值和查询参数
  useEffect(() => {
    if (defaultTopicTitle && !initialized) {
      const defaultValue = [
        {
          attr: attributes[0],
          values: [{ name: defaultTopicTitle }],
          _edit: undefined,
        },
      ];

      const filters = [
        {
          Name: 'Title',
          Values: [defaultTopicTitle],
        },
      ];

      setValue(defaultValue);
      setQuery((last) => ({
        ...last,
        Filters: filters,
      }));
      setInitialized(true);
    } else if (!defaultTopicTitle && !initialized) {
      // 没有默认标题时，设置空的 filters
      setQuery((last) => ({
        ...last,
        Filters: [],
      }));
      setInitialized(true);
    }
  }, [defaultTopicTitle, initialized]);

  // 处理搜索值变化（仅在初始化完成后）
  useEffect(() => {
    if (!initialized) return;

    const filters = [];
    (value || []).forEach((v) => {
      if (v?.attr?.key === AttributesNameEnum.TOPICS) {
        filters.push({
          Name: 'Title',
          Values: v.values.map((item) => item.name),
        });
      }
    });
    setQuery((last) => ({
      ...last,
      Filters: filters,
      Offset: 0, // 重置偏移量，从第一页开始查询
    }));
  }, [value, initialized]);

  // 执行查询（仅在初始化完成后）
  useEffect(() => {
    if (!initialized) return;
    fetchRiskManageSubjectList();
  }, [JSON.stringify(query), initialized]);

  return (
    <div className={s.container}>
      <Drawer
        size="m"
        placement="right"
        disableAnimation
        outerClickClosable
        showMask={false}
        visible={visible}
        title="治理主题"
        style={{
          width: DEFAULT_WIDTH,
          right: inspectionDrawerWidth ?? DEFAULT_WIDTH,
        }}
        subtitle=""
        footer={null}
        onClose={() => onClose && onClose()}
      >
        <div>
          <TagSearchBox
            attributes={attributes as any[]}
            minWidth="100%"
            value={value as any[]}
            onChange={onChange}
            hideHelp
            // eslint-disable-next-line @typescript-eslint/no-misused-promises
            onSearchButtonClick={() => fetchRiskManageSubjectList()}
          />
        </div>
        <div>
          <TopicTable
            pluginPropsData={pluginPropsData}
            list={data}
            loading={loading}
            total={total}
            limit={query.Limit}
            offset={query.Offset}
            setQuery={setQuery}
          />
        </div>
      </Drawer>
    </div>
  );
}
