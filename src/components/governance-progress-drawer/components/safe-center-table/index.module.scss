.risk-table-justify {
  :global {
    .sdk-cloud-inspection-justify-grid__col--right {
      width: 400px;
    }
  }
}

.risk-interval-justify {
  margin-bottom: 10px;

  .risk-interval {
    margin-right: 0!important;
  }
}

.unlocation {
  display: flex;
  overflow: hidden;
  width: 60px;
  height: 22px;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  border: 1px solid #006eff;
  background-color: #fff;
  color: #006eff;
  font-size: 12px;
  transform: scale(.85);
}

.location {
  display: flex;
  overflow: hidden;
  width: 60px;
  height: 22px;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  border: 1px solid #006eff;
  background-color: #006eff;
  color: #fff;
  font-size: 12px;
  transform: scale(.85);

  svg {
    margin-right: 4px;
    transform: scale(.7);
  }
}

.operationHover {
  &:hover {
    cursor: pointer;
  }
}

.risk-table-instance-column {
  display: flex;
  overflow: hidden;
  width: 100%;
  align-items: center;

  .high-cirle {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    margin-right: 10px;
    background-color: #e54545;
  }

  .mid-cirle {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    margin-right: 10px;
    background-color: #ff7200;
  }

  .risk-table-instance-inner {
    max-width: calc(100% - 40px);
  }
}

.risk-table {
  :global {
    .tr__detailrow {
      td {
        background-color: #fff !important;
      }
    }
  }
}

.search-date-wrap {
  display: flex;
  align-items: center;
  justify-content: end;

  .search-date-label {
    margin-right: 10px;
    font-size: 12px;
  }
}

.product-tag {
  margin-top: 0!important;
  margin-right: 0!important;

  :global span {
    overflow: hidden!important;
    max-width: 120px!important;
    white-space: nowrap!important;
  }
}