import React from 'react';
import {
  Button,
} from '@tencent/tea-component';
import { useDispatch } from 'react-redux';
import { changeGlobalData, useGlobalSelector } from '@src/store/global/index';
import StartAppSvg from '@src/assets/svg-component/start-app.svg';
import {
  InspectionTaskStatusEnum,
  IDriftDetectionTaskEnum,
} from '@src/constant/index';
import classNames from 'classnames';
import s from './index.module.scss';

/**
 * 治理进展抽屉底部
 * @returns
 */

export default function ProgressFooter(): React.ReactElement {
  const dispatch = useDispatch();
  const {
    inspectionTaskData,
    inspectionTaskStatus,
    driftDetectionTaskStatus,
    asyncTriggerScanTask,
  } = useGlobalSelector();
  return (
    <div className={s['drawer-footer-container']}>
      <Button
        disabled={
          !inspectionTaskData?.IsFinish
          || inspectionTaskStatus !== InspectionTaskStatusEnum.inspectionCompleted
          || driftDetectionTaskStatus !== IDriftDetectionTaskEnum.driftDetectioned
        }
        type="primary"
        onClick={() => {
          dispatch(changeGlobalData({
            asyncTriggerScanTask: !asyncTriggerScanTask,
          }));
        }}
      >
        <StartAppSvg
          // @ts-ignore
          className={classNames(s['start-app-icon'], { [s['start-app-icon-runing']]: inspectionTaskStatus === InspectionTaskStatusEnum.inspectioning })}
        />
        发起架构巡检
      </Button>
    </div>
  );
}
