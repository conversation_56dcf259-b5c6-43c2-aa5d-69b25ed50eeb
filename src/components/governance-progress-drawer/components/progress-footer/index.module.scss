.drawer-footer-container {
  display: flex;
  align-items: center;
  justify-content: center;

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .start-app-icon {
    margin-right: 5px;
    vertical-align: sub;

    :global {
      path {
        fill: #fff !important;
      }
    }
  }

  .start-app-icon-runing {
    animation: rotate 2s linear infinite;
  }
}