/* eslint-disable jsx-a11y/anchor-is-valid */
import React, {
  useEffect, useState, useRef,
} from 'react';
import {
  Table,
  Justify,
  Button,
  Icon,
  Bubble,
  message,
  Segment,
  DatePicker,
  Tag,
} from '@tencent/tea-component';
import { reportEvent } from '@src/utils';
import TableSearchBox from '@src/components/governance-progress-drawer/components/table-search-box';
import { IRiskInstanceItem } from '@src/service/governance-progress-drawer/index.type';
import IgnoreModal from '@src/components/ignore-modal';
import { useGlobalSelector } from '@src/store/global/index';
import {
  EnvEnum,
  InspectionTaskStatusEnum,
  UpdateInstanceToClaimedOperateEnum,
} from '@src/constant';
import {
  describeRiskInstancesInMapHandle,
  updateInstanceToClaimedStatusInMapHandle,
  updateInstanceToIgnoredStatusInMapHandle,
  createSubscriptionEmailV2Handle,
  updateSubscriptionEmailV2Handle,
} from '@src/service/governance-progress-drawer/final-request';
import useUpdateShapesBar from '@src/hooks/useUpdateShapesBar';
import { useDispatch } from 'react-redux';
import _ from 'lodash';
import InstanceIdCopy from '@src/components/instance-id-copy';
import AssignModal from '@src/components/node-risk-drawer/components/assign-modal';
import {
  changeGovernanceProgressDrawerState,
  useGovernanceProgressDrawerStateSelector,
} from '@src/store/governance-progress-drawer';
import moment from 'moment';
import RiskExpandList from '../risk-expand-list';
import s from './index.module.scss';

const {
  pageable, selectable, expandable, autotip, scrollable,
} = Table.addons;
const { RangePicker } = DatePicker;

enum riskIntervalEnum {
  'all' = 'all',
  'seven' = '7',
  'fourteen' = '14',
  'thirty' = '30'
}

/**
 * 节点风险表格
 * @returns
 */
export default function RiskInstanceTable(): React.ReactElement {
  const {
    pluginPropsData,
    inspectionTaskStatus,
  } = useGlobalSelector();
  const { riskTableFilter, riskTablePage } = useGovernanceProgressDrawerStateSelector();
  const { visible } = useGovernanceProgressDrawerStateSelector();
  const { updateShapesBar } = useUpdateShapesBar();
  const dispatch = useDispatch();

  const [pageSize, setPageSize] = useState(10);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [tableData, setTableData] = useState<IRiskInstanceItem[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [assignModalLoading, setAssignModalLoading] = useState(false);
  const [ignoreModalLoading, setIgnoreModalLoading] = useState(false);
  const [ignoreModalVisible, setIgnoreModalVisible] = useState(false);
  const selectedKeysRef = useRef([]);
  const [riskInterval, setRiskInterval] = useState<string>(riskIntervalEnum.all);
  const [timeRange, setTimeRange] = useState<any>([null, null]);
  const [productList, setProductList] = useState([]);

  const columns = [
    {
      key: 'InstanceId',
      header: '实例',
      width: 140,
      render: (record: IRiskInstanceItem) => (
        <div className={s['risk-table-instance-column']}>
          {
            record.HighRiskCount ? (
              <div className={s['high-cirle']} />
            ) : (
              <div className={s['mid-cirle']} />
            )
          }
          <div
            className={s['risk-table-instance-inner']}
            style={{ maxWidth: record.IsChange ? 'calc(100% - 40px)' : 'calc(100% - 18px)' }}
          >
            <InstanceIdCopy instanceId={record.InstanceId} url={record.Url} />
          </div>
          {
            record.IsChange && (
              <Bubble
                arrowPointAtCenter
                placement="top"
                content="实例风险已解决，建议巡检更新"
              >
                <Icon type="info" style={{ marginLeft: 5 }} />
              </Bubble>
            )
          }
        </div>
      ),
    },
    {
      key: 'Product',
      header: '云产品',
      width: 150,
      render: (record) => {
        const productName = productList?.find((item) => item.key === record.Product)?.name;
        return productName ? <Bubble content={productName}>
          <Tag theme="primary" className={s['product-tag']}>
            {
              productName
            }
          </Tag>
        </Bubble> : '-';
      },
    },
    {
      key: 'Tag',
      header: '标签',
      render: (record: IRiskInstanceItem) => {
        try {
          const tag = record.Tag;
          const tagArray = tag ? JSON.parse(tag) : [];
          if (tagArray.length === 0) {
            return '-';
          }
          return (
            <Bubble
              arrowPointAtCenter
              placement="top"
              content={(
                <div>
                  {
                    tagArray.map((tag) => (
                      <div key={tag.Key}>
                        <span>
                          {tag.Key}
                          ：
                        </span>
                        <span>{tag.Value}</span>
                      </div>
                    ))
                  }
                </div>
              )}
            >
              <Icon type="tag" />
            </Bubble>
          );
        } catch (error) {
          return '暂无';
        }
      },
    },
    {
      key: 'ClaimPerson',
      header: '跟进人',
      width: 90,
      render: (record) => <div>
        {record.ClaimPerson}
                          </div>,
    },
    {
      key: 'Operate',
      header: '操作',
      width: 100,
      render: (record) => (
        <>
          <Button
            type="link"
            onClick={() => {
              reportEvent(pluginPropsData.env as EnvEnum, {
                archId: pluginPropsData.archInfo.archId,
                nodeId: record?.NodeId,
                nodeName: record?.NodeName,
                subUin: pluginPropsData.uin,
                subUinName: pluginPropsData.userName,
                eventType: 'riskClickInstanceClaimed',
                instanceId: record.InstanceId,
              });
              setSelectedKeys([`${record.InstanceId}_${record.Product}`]);
              selectedKeysRef.current = [`${record.InstanceId}_${record.Product}`];
              setAssignModalVisible(true);
            }}
          >
            分配
          </Button>
          <Button
            disabled={pluginPropsData?.env === EnvEnum.ISA}
            type="link"
            onClick={() => {
              reportEvent(pluginPropsData.env as EnvEnum, {
                archId: pluginPropsData.archInfo.archId,
                nodeId: record?.NodeId,
                nodeName: record?.NodeName,
                subUin: pluginPropsData.uin,
                subUinName: pluginPropsData.userName,
                eventType: 'riskClickInstanceIgnore',
                instanceId: record.InstanceId,
              });
              setSelectedKeys([`${record.InstanceId}_${record.Product}`]);
              selectedKeysRef.current = [`${record.InstanceId}_${record.Product}`];
              setIgnoreModalVisible(true);
            }}
          >
            忽略
          </Button>
        </>
      ),
    },
  ];

  const getTabelList = () => {
    const filters = riskTableFilter.filter((item) => item?.attr?.key !== 'TagList');
    const filtersParams = [];
    filters.forEach((item) => {
      const obj = {
        Name: item.attr.key,
        Values: item.values.map((valueItem) => `${valueItem.key || ''}` || `${valueItem.name || ''}`),
      };
      filtersParams.push(obj);
    });
    let tagListParams: any = [];
    const filtersTag: any = riskTableFilter.find((item) => item?.attr?.key === 'TagList');
    if (filtersTag?.values) {
      tagListParams = filtersTag?.values?.map((valueItem) => ({
        Key: valueItem.key,
        Value: valueItem.name,
      }));
    }
    if (riskInterval && riskInterval !== riskIntervalEnum.all) {
      filtersParams.push({
        Name: 'RiskDays',
        Values: [riskInterval],
      });
    }
    if (timeRange[0] && timeRange[1]) {
      filtersParams.push({
        Name: 'StartDate',
        Values: [timeRange[0]?.format('YYYY-MM-DD')],
      });
      filtersParams.push({
        Name: 'EndDate',
        Values: [timeRange[1]?.format('YYYY-MM-DD')],
      });
    }
    const apiParams = {
      MapId: pluginPropsData.archInfo.archId,
      Offset: (riskTablePage - 1) * pageSize,
      Limit: pageSize,
      Filters: filtersParams,
      TagList: tagListParams,
    };
    setLoading(true);
    describeRiskInstancesInMapHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: apiParams,
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setTableData(rs.RiskInstanceList);
        setTotal(rs.TotalCount);
      }
    }).finally(() => {
      setLoading(false);
    });
  };

  const updateInstanceToClaimedStatus = async (values, emailInfo) => {
    setAssignModalLoading(true);
    const userInfoArr = values.User?.split(':');
    try {
      await updateInstanceToClaimedStatusInMapHandle({
        env: pluginPropsData.env,
        apiParams: {
          data: {
            MapId: pluginPropsData.archInfo.archId,
            Operate: UpdateInstanceToClaimedOperateEnum.add,
            MapRiskInstanceList: tableData?.filter((item) => selectedKeysRef.current?.includes(`${item.InstanceId}_${item.Product}`))?.map((item) => ({
              InstanceId: item.InstanceId,
              Product: item.Product,
            })) ?? [],
            ClaimPerson: userInfoArr[0],
            ClaimUin: userInfoArr[1],
          },
        },
        uin: pluginPropsData.uin,
      });
      if (values.Email) {
        if (emailInfo.Id !== undefined) {
          await updateSubscriptionEmailV2Handle({
            env: pluginPropsData.env,
            apiParams: {
              data: {
                Id: emailInfo.Id,
                SubUin: userInfoArr[1],
                UserName: emailInfo.UserName ? emailInfo.UserName : userInfoArr[0],
                Email: values.Email,
              },
            },
            uin: pluginPropsData.uin,
          });
        } else {
          await createSubscriptionEmailV2Handle({
            env: pluginPropsData.env,
            apiParams: {
              data: {
                SubUin: userInfoArr[1],
                UserName: userInfoArr[0],
                Email: values.Email,
              },
            },
            uin: pluginPropsData.uin,
          });
        }
      }
      setSelectedKeys([]);
      message.success({ content: '分配成功' });
      getTabelList();
      setAssignModalVisible(false);
      setAssignModalLoading(false);
    } catch (err) {
      setAssignModalLoading(false);
    }
  };

  const updateInstanceToIgnoredStatus = async (reason) => {
    setIgnoreModalLoading(true);
    updateInstanceToIgnoredStatusInMapHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          MapId: pluginPropsData.archInfo.archId,
          Operate: UpdateInstanceToClaimedOperateEnum.add,
          MapRiskInstanceList: tableData?.filter((item) => selectedKeysRef.current?.includes(`${item.InstanceId}_${item.Product}`))?.map((item) => ({
            InstanceId: item.InstanceId,
            Product: item.Product,
          })) ?? [],
          Reason: reason,
          Person: pluginPropsData?.env === EnvEnum.ISA ? pluginPropsData.userName : (pluginPropsData.userName?.slice(0, pluginPropsData.userName.lastIndexOf('@')) ?? ''),
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setSelectedKeys([]);
        message.success({ content: '忽略成功' });
        getTabelList();
        setIgnoreModalVisible(false);
        // 忽略成功后需要重新更新角标
        updateShapesBar();
      }
    }).finally(() => {
      setIgnoreModalLoading(false);
    });
  };

  useEffect(() => {
    if (pluginPropsData.archInfo.archId && visible) {
      setTimeout(() => {
        setExpandedKeys([]);
        getTabelList();
      }, 0);
    }
  }, [pluginPropsData, riskTablePage, pageSize, riskTableFilter, riskInterval, timeRange, visible]);

  useEffect(() => {
    if (inspectionTaskStatus === InspectionTaskStatusEnum.inspectionCompleted) {
      setExpandedKeys([]);
      getTabelList();
    }
  }, [inspectionTaskStatus]);

  return (
    <>
      <Justify
        className={s['risk-interval-justify']}
        left={
          <Segment
            className={s['risk-interval']}
            value={riskInterval}
            options={
              [
                { text: '全部风险', value: riskIntervalEnum.all },
                { text: '超7天', value: riskIntervalEnum.seven },
                { text: '超14天', value: riskIntervalEnum.fourteen },
                { text: '超30天', value: riskIntervalEnum.thirty },
              ]
            }
            onChange={
              (val) => {
                setTimeRange([null, null]);
                setRiskInterval(val);
                dispatch(changeGovernanceProgressDrawerState({
                  riskTablePage: 1,
                }));
              }
            }
          />
        }
        right={
          <div className={s['search-date-wrap']}>
            <div className={s['search-date-label']}>发生时间</div>
            <RangePicker
              value={timeRange}
              range={[moment().subtract(29, 'd'), moment()]}
              clearable
              onChange={
                (val) => {
                  setRiskInterval(riskIntervalEnum.all);
                  setTimeRange(val);
                  dispatch(changeGovernanceProgressDrawerState({
                    riskTablePage: 1,
                  }));
                }
              }
            />
          </div>
        }
      />
      <Table.ActionPanel>
        <Justify
          className={s['risk-table-justify']}
          left={(
            <>
              <Button
                disabled={selectedKeys.length === 0}
                type="primary"
                onClick={() => {
                  reportEvent(pluginPropsData.env as EnvEnum, {
                    archId: pluginPropsData.archInfo.archId,
                    subUin: pluginPropsData.uin,
                    subUinName: pluginPropsData.userName,
                    eventType: 'riskClickBatchInstanceClaimed',
                  });
                  setAssignModalVisible(true);
                }}
              >
                分配
              </Button>
              <Button
                disabled={selectedKeys.length === 0 || pluginPropsData?.env === EnvEnum.ISA}
                onClick={() => {
                  reportEvent(pluginPropsData.env as EnvEnum, {
                    archId: pluginPropsData.archInfo.archId,
                    subUin: pluginPropsData.uin,
                    subUinName: pluginPropsData.userName,
                    eventType: 'riskClickBatchInstanceIgnore',
                  });
                  setIgnoreModalVisible(true);
                }}
              >
                忽略
              </Button>
            </>
          )}
          right={(
            <TableSearchBox
              value={riskTableFilter}
              productListCallback={
                (val) => {
                  setProductList(val);
                }
              }
              onChange={(values) => {
                const valuesCopy = _.cloneDeep(values);
                const valuesCopyFilter = valuesCopy.filter((item) => !!item?.attr);
                valuesCopyFilter.forEach((item: any) => {
                  // eslint-disable-next-line no-param-reassign
                  delete item.attr?.render;
                  // eslint-disable-next-line no-param-reassign
                  delete item.attr?.values;
                });
                dispatch(changeGovernanceProgressDrawerState({
                  riskTableFilter: valuesCopyFilter,
                  riskTablePage: 1,
                }));
              }}
            />
          )}
        />
      </Table.ActionPanel>
      <Table
        className={s['risk-table']}
        verticalTop
        records={tableData}
        recordKey={
          (record) => `${record.InstanceId}_${record.Product}`
        }
        columns={columns}
        bordered
        addons={[
          scrollable(
            {
              maxHeight: 'calc(100vh - 464px)',
            },
          ),
          pageable({
            pageIndex: riskTablePage,
            recordCount: total,
            onPagingChange: (query) => {
              dispatch(changeGovernanceProgressDrawerState({
                riskTablePage: query.pageIndex,
              }));
              setPageSize(query.pageSize);
            },
          }),
          selectable({
            value: selectedKeys,
            onChange: (keys) => {
              selectedKeysRef.current = keys;
              setSelectedKeys(keys);
            },
          }),
          expandable({
            expandedKeys,
            onExpandedKeysChange: (keys, { event }) => {
              const lastKey = keys[keys.length - 1];
              event.stopPropagation();
              setExpandedKeys([lastKey]);
              if (lastKey) {
                reportEvent(pluginPropsData.env as EnvEnum, {
                  archId: pluginPropsData.archInfo.archId,
                  subUin: pluginPropsData.uin,
                  subUinName: pluginPropsData.userName,
                  eventType: 'riskClickExpandInstanceRisk', // 有风险—展开实例风险
                  instanceId: lastKey,
                });
              }
            },
            render(record) {
              return (
                <RiskExpandList
                  mapId={pluginPropsData.archInfo.archId}
                  nodeUuid={record.NodeId}
                  nodeName={record.NodeName}
                  taskId={record.TaskId}
                  instanceId={record.InstanceId}
                  region={record.Region}
                  onCustomerServiceClick={(env, params) => {
                    reportEvent(env, {
                      ...params,
                      eventType: 'riskClickAICustomerService', // 有风险—唤起智能客服
                    });
                  }}
                  onIgnoreClick={(env, params) => {
                    reportEvent(env, {
                      ...params,
                      eventType: 'riskClickIgnoreRisk', // 有风险—忽略风险
                    });
                  }}
                  onSuccess={() => {
                    getTabelList();
                  }}
                />
              );
            },
            gapCell: 2,
            rowExpand: true,
          }),
          autotip({
            isLoading: loading,
          }),
        ]}
      />
      <AssignModal
        visible={assignModalVisible}
        submitLoading={assignModalLoading}
        onClose={() => {
          setAssignModalVisible(false);
        }}
        onSubmit={(user, emailInfo) => {
          updateInstanceToClaimedStatus(user, emailInfo);
        }}
      />
      <IgnoreModal
        visible={ignoreModalVisible}
        submitLoading={ignoreModalLoading}
        showSubTips={false}
        onClose={() => {
          setIgnoreModalVisible(false);
        }}
        onSubmit={(reason) => {
          updateInstanceToIgnoredStatus(reason);
        }}
      />
    </>
  );
}
