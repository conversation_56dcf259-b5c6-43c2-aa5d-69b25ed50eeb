import React, { useState, useEffect, useRef } from 'react';
import { IStrategiesItem } from '@src/service/node-risk-drawer/index.type';
import { simpleMarkdownToHTML } from '@src/utils/index';
import { Button, Status, message } from '@tencent/tea-component';
import CustomerService from '@src/components/customer-service';
import { RiskLevelEnum, UpdateInstanceToClaimedOperateEnum, EnvEnum } from '@src/constant/index';
import { useGlobalSelector } from '@src/store/global/index';
import {
  describeRiskItemsForInstanceHandle,
  updateRiskItemStatusInInstanceHandle,
} from '@src/service/node-risk-drawer/final-request';
import IgnoreModal from '@src/components/ignore-modal';
import classNames from 'classnames';
import DownCollapseSvg from '@src/assets/svg-component/down-collapse.svg';
import UpCollapseSvg from '@src/assets/svg-component/up-collapse.svg';
import useUpdateShapesBar from '@src/hooks/useUpdateShapesBar';
import _ from 'lodash';
import { app } from '@tea/app';
import s from './index.module.scss';

interface IRiskExpandListProps {
  mapId: string;
  nodeUuid: string;
  nodeName: string;
  taskId: string;
  instanceId: string;
  region: string;
  onSuccess: () => void;
  onCustomerServiceClick?: (env: EnvEnum, params: any) => void;
  onIgnoreClick?: (env: EnvEnum, params: any) => void;
}

export default function RiskExpandList(props: IRiskExpandListProps): React.ReactElement {
  const { loginUin } = app.user;
  const {
    mapId,
    nodeUuid,
    nodeName,
    taskId,
    instanceId,
    region,
    onSuccess,
    onCustomerServiceClick = () => {},
    onIgnoreClick = () => {},
  } = props;
  const { pluginPropsData, strategies } = useGlobalSelector();
  const [ignoreModalLoading, setIgnoreModalLoading] = useState(false);
  const [ignoreModalVisible, setIgnoreModalVisible] = useState(false);
  const [listData, setListData] = useState<IStrategiesItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentExpandId, setCurrentExpandId] = useState(undefined);
  const currentStrategyId = useRef(undefined);
  const { updateShapesBar } = useUpdateShapesBar();

  const describeRiskItemsForInstance = () => {
    setLoading(true);
    describeRiskItemsForInstanceHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          MapId: mapId,
          NodeUuid: nodeUuid,
          TaskId: taskId,
          InstanceId: instanceId,
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        const data = [];
        rs.InstanceRiskItemList.forEach((item) => {
          const strategieItem = strategies.find((strategie) => strategie.StrategyId === item.StrategyId);
          const strategieItemCopy = _.cloneDeep(strategieItem);
          const conditions = strategieItemCopy.Conditions
            .filter((condition) => condition.ConditionId === item.ConditionId);
          strategieItemCopy.Conditions = conditions;
          data.push(strategieItemCopy);
        });
        setListData(data);
      }
    }).finally(() => {
      setLoading(false);
    });
  };

  const updateRiskItemStatusInInstance = async (reason) => {
    setIgnoreModalLoading(true);
    updateRiskItemStatusInInstanceHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          InstanceRegionList: [
            {
              InstanceId: instanceId,
              Region: region,
            },
          ],
          StrategyId: currentStrategyId.current,
          IgnoredReason: reason,
          Operate: UpdateInstanceToClaimedOperateEnum.add,
          IgnoredPerson: pluginPropsData?.env === EnvEnum.ISA ? pluginPropsData.userName : (pluginPropsData.userName?.slice(0, pluginPropsData.userName.lastIndexOf('@')) ?? ''),
          ClaimUin: loginUin?.toString() ?? '',
          MapId: pluginPropsData.archInfo.archId,
          NodeUuid: nodeUuid,
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setIgnoreModalVisible(false);
        message.success({ content: '忽略成功' });
        describeRiskItemsForInstance();
        onSuccess();
        // 更新角标
        updateShapesBar();
      }
    }).finally(() => {
      setIgnoreModalLoading(false);
    });
  };

  useEffect(() => {
    describeRiskItemsForInstance();
  }, [mapId, nodeUuid, taskId, instanceId]);

  return (
    <div className={s['risk-expand-list-container']}>
      {
        loading && (
          <Status
            icon="loading"
            size="xs"
            description="加载中..."
          />
        )
      }
      {
        listData.map((item) => (
          <div
            key={item.StrategyId}
            className={s['risk-item']}
            onClick={() => {
              setCurrentExpandId((prev) => {
                if (prev === item.StrategyId) {
                  return undefined;
                }
                return item.StrategyId;
              });
            }}
          >
            <div className={classNames(
              s['risk-item-inner'],
            )}
            >
              <div
                className={classNames({
                  [s['risk-type-div-3']]:
                 item?.Conditions?.[0]?.Level === RiskLevelEnum.highRisk,
                  [s['risk-type-div-2']]:
                 item?.Conditions?.[0]?.Level === RiskLevelEnum.mediumRisk,
                  [s['risk-type-div']]: true,
                })}
              />
              <span className={s['risk-name']}>{item.Name}</span>
              <span className={s['risk-icon']}>
                {
                  currentExpandId === item.StrategyId ? (
                    <UpCollapseSvg />
                  ) : (
                    <DownCollapseSvg />
                  )
                }
              </span>
            </div>
            {
              currentExpandId === item.StrategyId && (
              <div
                className={s['risk-expand-info']}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <div className={s['strategy-name']}>
                  <span className={s['strategy-name-span']}>策略：</span>
                  <span className={s['strategy-desc-span']}>
                    {item.Desc}
                  </span>
                </div>
                <div className={s['conditions-div']}>
                  <span className={s['conditions-label']}>条件：</span>
                  <span className={s['conditions-tips']}>
                    <span
                      className={classNames({
                        [s['conditions-level-3']]:
                       item?.Conditions?.[0]?.Level === RiskLevelEnum.highRisk,
                        [s['conditions-level-2']]:
                       item?.Conditions?.[0]?.Level === RiskLevelEnum.mediumRisk,
                      })}
                    >
                      { item.Conditions?.[0]?.Level === RiskLevelEnum.highRisk ? '高风险' : '中风险'}
                    </span>
                    <span className={s['conditions-desc']}>
                      -
                      {item.Conditions?.[0]?.Desc}
                    </span>
                  </span>
                </div>
                <div className={s['repair-div']}>
                  <div className={s['repair-div-label']}>
                    优化建议
                  </div>
                  <div
                    className={s['repair-md']}
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{
                      __html: simpleMarkdownToHTML(item.Repair),
                    }}
                  />
                </div>
                <div className={s['risk-footer']}>
                  <Button
                    disabled={pluginPropsData?.env === EnvEnum.ISA}
                    type="link"
                    onClick={() => {
                      onIgnoreClick?.(pluginPropsData.env as EnvEnum, {
                        archId: pluginPropsData.archInfo.archId,
                        nodeId: nodeUuid,
                        nodeName,
                        subUin: pluginPropsData.uin,
                        subUinName: pluginPropsData.userName,
                        instanceId,
                        strategyId: item.StrategyId.toString(),
                      });
                      currentStrategyId.current = item.StrategyId;
                      setIgnoreModalVisible(true);
                    }}
                  >
                    忽略
                  </Button>
                  <CustomerService
                    strategyText={item.Name}
                    onClick={() => {
                      onCustomerServiceClick?.(pluginPropsData.env as EnvEnum, {
                        archId: pluginPropsData.archInfo.archId,
                        nodeId: nodeUuid,
                        nodeName,
                        subUin: pluginPropsData.uin,
                        subUinName: pluginPropsData.userName,
                        instanceId,
                        strategyId: item.StrategyId.toString(),
                      });
                    }}
                  />
                </div>
              </div>
              )
            }
          </div>
        ))
      }
      <IgnoreModal
        visible={ignoreModalVisible}
        submitLoading={ignoreModalLoading}
        showSubTips
        onClose={() => {
          setIgnoreModalVisible(false);
        }}
        onSubmit={(reason) => {
          updateRiskItemStatusInInstance(reason);
        }}
      />
    </div>
  );
}
