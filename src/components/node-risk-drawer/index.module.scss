.node-risk-drawer-header {
  height: 30px;
  color: rgba(0, 0, 0, .40);
  font-size: 12px;

  .node-risk-drawer-header-number {
    color: #000;
    font-size: 12px;
    font-weight: 500;
  }

  .node-risk-drawer-header-jump {
    color: #006eff;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
  }
}

.download-report-button {
  display: flex;
  height: 20px;
  align-items: center;
  color: #006eff;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;

  &>span {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .download-report-button-inner-disabled {
    color: rgba(0, 0, 0, .40);

    .download-svg {
      path: {
        fill: rgba(0, 0, 0, .40) !important;
      }

      :global {
        path {
          fill: rgba(0, 0, 0, .40) !important;
        }
      }
    }
  }

  .haschange-tips {
    margin-left: 5px;
    color: #000;
    font-size: 12px;
  }
}

.node-risk-tabs {
  margin-top: 35px;

  :global {
    .status-tip-fixed {
      width: 100% !important;
    }
  }
}

.drawer-footer-container {
  display: flex;
  align-items: center;
  justify-content: center;

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
  
    100% {
      transform: rotate(360deg);
    }
  }

  .start-app-icon {
    margin-right: 5px;
    vertical-align: sub;

    :global {
      path {
        fill: #fff !important;
      }
    }
  }

  .start-app-icon-runing {
    animation: rotate 2s linear infinite;
  }
}

.node-risk-drawer-content {
  width: 100%;
  height: auto;

  .node-risk-drawer-no-inspect {
    color: #000;
    font-size: 12px;
  }
}

.node-risk-drawer {
  :global {
    .sdk-cloud-inspection-drawer__body {
      .sdk-cloud-inspection-drawer__body-inner {
        display: flex;
        height: 100%;
        flex-direction: column;
      }
    }
  }
}

.node-risk-drawer-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 700;

  .node-risk-drawer-title-text {
    overflow: hidden;
    max-width: 180px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .chat-bi-trriger {
    position: relative;
    top: 2px;
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
  }
}