import React, { useEffect, useMemo, useState } from 'react';
import { Button, TabPanel, Tabs } from '@tencent/tea-component';
import { useDispatch } from 'react-redux';
import { changeGlobalData, useGlobalSelector } from '@src/store/global/index';
import DownloadSvg from '@src/assets/svg-component/download.svg';
import StartAppSvg from '@src/assets/svg-component/start-app.svg';
import {
  EnvEnum,
  IDriftDetectionTaskEnum,
  InspectionTaskStatusEnum,
  NodeInspectionTaskStatusEnum,
  RiskTabEnum,
} from '@src/constant/index';
import { changeReportData, useReportSelector } from '@src/store/report/index';
import { useNodeRiskDrawerStateSelector } from '@src/store/node-risk-drawer/index';
import { describeArchScanTaskRiskInfoHandle } from '@src/service/node-risk-drawer/final-request';
import { IDescribeArchScanTaskRiskInfoResult } from '@src/service/node-risk-drawer/index.type';
import classNames from 'classnames';
import { reportEvent } from '@src/utils';
import useNodeInspectionTask from '@src/hooks/useNodeInspectionTask';
import useUpdateNodeRiskCount from '@src/hooks/useUpdateNodeRiskCount';
import { useAppContext } from '@src/context/AppContext';
import RiskTabel from './components/risk-tabel';
import CiaimedTabel from './components/claimed-tabel';
import IgnoredTabel from './components/ignored-table';
import NoRiskTabel from './components/no-risk-table';
import InspectionDrawer, { TabEnum } from '../inspection-drawer';
import s from './index.module.scss';

interface INodeRiskDrawerProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

/**
 * 节点风险详情抽屉
 * @returns
 */
export default function NodeRiskDrawer(props: INodeRiskDrawerProps): React.ReactElement {
  const {
    pluginAPI,
  } = props;
  const {
    nodeRiskDrawerVisible,
    selectNodeInfo,
    pluginPropsData,
    inspectionTaskData,
    supportTaskProductList,
    inspectionTaskStatus,
    driftDetectionTaskStatus,
    nodeInspectionTaskStatus,
    archScanRiskInfo,
  } = useGlobalSelector();
  const { nodeReportState } = useReportSelector();
  const { riskNodeCount } = useNodeRiskDrawerStateSelector();
  const { startNodeScanTask } = useNodeInspectionTask({ pluginAPI: pluginPropsData });
  const { updateNodeRiskCount } = useUpdateNodeRiskCount();
  const { chatBiCallBacks } = useAppContext();
  const [nodeTaskRiskinfo, setNodeTaskRiskinfo] = useState<IDescribeArchScanTaskRiskInfoResult>({
    BindInstanceCount: 0,
    LastScanAt: '',
    NodeChanged: false,
    TotalStrategyCount: 0,
    Product: '',
  });
  const [currentTab, setCurrentTab] = useState(TabEnum.left);

  const dispatch = useDispatch();

  const nodeLoading = useMemo(() => {
    if (!nodeRiskDrawerVisible) {
      setCurrentTab(TabEnum.left);
      return false;
    }
    if (inspectionTaskStatus !== InspectionTaskStatusEnum.inspectionCompleted) {
      return false;
    }
    if (driftDetectionTaskStatus !== IDriftDetectionTaskEnum.driftDetectioned) {
      return false;
    }
    const item = inspectionTaskData?.NodeTaskStatusList?.find((item) => item.NodeUuid === selectNodeInfo?.key);
    if (!item) {
      return false;
    }
    return !item.IsFinish;
  }, [inspectionTaskData, selectNodeInfo, nodeRiskDrawerVisible,
    inspectionTaskData, inspectionTaskStatus, driftDetectionTaskStatus]);

  const showUnInspectionTips = useMemo(() => {
    if (!nodeRiskDrawerVisible) {
      return true;
    }
    const item = archScanRiskInfo?.NodeRiskItems.find((item) => item.NodeUuid === selectNodeInfo?.key);
    return !item?.LastSuccessTaskId;
  }, [archScanRiskInfo, selectNodeInfo, nodeRiskDrawerVisible]);

  const nodeLabel = useMemo(() => {
    if (!selectNodeInfo) {
      return '-';
    }
    const nodeList = pluginPropsData?.archInfo?.nodeList;
    if (!nodeList.length) {
      return '-';
    }
    const item = nodeList.find((item) => item?.DiagramId === selectNodeInfo?.key);
    return item?.NodeName || '-';
  }, [selectNodeInfo, pluginPropsData]);

  const tabs = useMemo(() => [
    {
      id: RiskTabEnum.risky,
      label: `有风险 (${riskNodeCount?.RiskInstancesTotal ?? 0})`,
    },
    {
      id: RiskTabEnum.claimed,
      label: `已认领 (${riskNodeCount?.ClaimedInstancesTotal ?? 0})`,
    },
    {
      id: RiskTabEnum.ignored,
      label: `已忽略 (${riskNodeCount?.IgnoredInstancesTotal ?? 0})`,
    },
    {
      id: RiskTabEnum.noRisk,
      label: `无风险 (${riskNodeCount?.SafeInstancesTotal ?? 0})`,
    },
  ], [riskNodeCount]);

  const eventType = {
    [RiskTabEnum.risky]: 'clickAtRiskTab',
    [RiskTabEnum.claimed]: 'clickClaimedTab',
    [RiskTabEnum.ignored]: 'clickIgnoredTab',
    [RiskTabEnum.noRisk]: 'clickNoRiskTab',
  };

  const tableMap: any = {
    [RiskTabEnum.risky]: <RiskTabel onUpdateCount={updateNodeRiskCount} />,
    [RiskTabEnum.claimed]: <CiaimedTabel onUpdateCount={updateNodeRiskCount} />,
    [RiskTabEnum.ignored]: <IgnoredTabel onUpdateCount={updateNodeRiskCount} />,
    [RiskTabEnum.noRisk]: <NoRiskTabel />,
  };

  const getArchScanTaskRiskInfo = () => {
    describeArchScanTaskRiskInfoHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          MapId: pluginPropsData.archInfo.archId,
          NodeUuid: selectNodeInfo.key,
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setNodeTaskRiskinfo(rs);
      }
    });
  };

  useEffect(() => {
    if (selectNodeInfo && pluginPropsData && nodeRiskDrawerVisible && !showUnInspectionTips) {
      getArchScanTaskRiskInfo();
      // 初始化时获取节点风险数量统计
      updateNodeRiskCount();
    }
    if (!nodeRiskDrawerVisible) {
      setNodeTaskRiskinfo({
        BindInstanceCount: 0,
        LastScanAt: '',
        NodeChanged: false,
        TotalStrategyCount: 0,
        Product: '',
      });
    }
  }, [selectNodeInfo, pluginPropsData, nodeRiskDrawerVisible, showUnInspectionTips]);

  useEffect(() => {
    if (nodeInspectionTaskStatus === NodeInspectionTaskStatusEnum.inspectionCompleted
      && nodeRiskDrawerVisible && !showUnInspectionTips) {
      getArchScanTaskRiskInfo();
    }
  }, [nodeInspectionTaskStatus, nodeRiskDrawerVisible, showUnInspectionTips]);

  return (
    <InspectionDrawer
      nowTab={currentTab}
      title={nodeLabel}
      pluginAPI={pluginAPI}
      drawerVisible={nodeRiskDrawerVisible}
      onClose={() => {
        dispatch(changeGlobalData({
          nodeRiskDrawerVisible: false,
        }));
      }}
      onTabChange={(tab) => {
        setCurrentTab(tab);
        if (tab === TabEnum.right) {
          chatBiCallBacks?.setSelectNode?.(selectNodeInfo?.key);
        }
      }}
      footer={
        currentTab === TabEnum.left
        && <div className={s['drawer-footer-container']}>
          <Button
            disabled={
              !inspectionTaskData?.IsFinish
              || inspectionTaskStatus !== InspectionTaskStatusEnum.inspectionCompleted
              || driftDetectionTaskStatus !== IDriftDetectionTaskEnum.driftDetectioned
              || nodeLoading
            }
            type="primary"
            onClick={() => {
              reportEvent(pluginPropsData.env as EnvEnum, {
                archId: pluginPropsData.archInfo.archId,
                nodeId: selectNodeInfo.key,
                nodeName: selectNodeInfo.name,
                subUin: pluginPropsData.uin,
                subUinName: pluginPropsData.userName,
                eventType: 'clickNodeScan',
              });
              startNodeScanTask(supportTaskProductList, selectNodeInfo.key);
            }}
          >
            <StartAppSvg
              // @ts-ignore
              className={classNames(s['start-app-icon'], { [s['start-app-icon-runing']]: nodeLoading })}
            />
            发起节点巡检
          </Button>
        </div>
      }
    >
      <div className={s['node-risk-drawer-content']}>
        {
          showUnInspectionTips ? (
            <div className={s['node-risk-drawer-no-inspect']}>尚未对业务架构节点风险进行巡检，请点击“发起节点巡检”按钮</div>
          ) : (
            <>
              <div className={s['node-risk-drawer-header']}>
                上次巡检时间：
                <span className={s['node-risk-drawer-header-number']}>
                  {nodeTaskRiskinfo.LastScanAt || '未发起过巡检'}
                </span>
              </div>
              <div className={s['download-report-button']}>
                <span
                  className={classNames({
                    [s['download-report-button-inner-disabled']]: nodeTaskRiskinfo.NodeChanged,
                  })}
                  onClick={() => {
                    reportEvent(pluginPropsData.env as EnvEnum, {
                      archId: pluginPropsData.archInfo.archId,
                      nodeId: selectNodeInfo.key,
                      nodeName: selectNodeInfo.name,
                      subUin: pluginPropsData.uin,
                      subUinName: pluginPropsData.userName,
                      eventType: 'clickGenerateNodeReport',
                    });
                    if (!nodeTaskRiskinfo.NodeChanged) {
                      dispatch(changeReportData({ nodeReportState: { ...nodeReportState, visible: true } }));
                    }
                  }}
                >
                  <DownloadSvg
                    // @ts-ignore
                    className={s['download-svg']}
                    style={{ marginRight: 5 }}
                  />
                  生成节点风险报告
                </span>
                {
                  nodeTaskRiskinfo.NodeChanged && (
                    <span className={s['haschange-tips']}>该节点绑定资源配置已变化，请点击下方节点巡检以更新风险状态</span>
                  )
                }
              </div>
              <div className={s['node-risk-tabs']}>
                <Tabs
                  tabs={tabs}
                  onActive={(tab) => {
                    reportEvent(pluginPropsData.env as EnvEnum, {
                      archId: pluginPropsData.archInfo.archId,
                      nodeId: selectNodeInfo.key,
                      nodeName: selectNodeInfo.name,
                      subUin: pluginPropsData.uin,
                      subUinName: pluginPropsData.userName,
                      eventType: eventType[tab.id],
                    });
                  }}
                >
                  {tabs.map((tab) => (
                    <TabPanel id={tab.id} key={tab.id}>
                      {
                        tableMap[tab.id]
                      }
                    </TabPanel>
                  ))}
                </Tabs>
              </div>
            </>
          )
        }
      </div>
    </InspectionDrawer>
  );
}
