import React, { useEffect, useState } from 'react';
import {
  Modal, Button, Form, Select, Input,
} from '@tencent/tea-component';
import { useForm, Controller } from 'react-hook-form';
import { describeSubAccountsByMainAccountHandle, describeSubscriptionEmailListV2Handle } from '@src/service/node-risk-drawer/final-request';
import { useGlobalSelector } from '@src/store/global/index';
import s from './index.module.scss';

interface IAssignModalProps {
  visible: boolean;
  submitLoading: boolean,
  onClose: () => void;
  onSubmit: (value: {
    User?: string,
    Email?: string
  }, emailInfo?: {
      Id?: number,
      SubUin?: string,
      UserName?: string,
      Email?: string,
    }) => void;
}

export default function AssignModal(props: IAssignModalProps): React.ReactElement {
  const {
    visible,
    submitLoading,
    onClose,
    onSubmit,
  } = props;
  const { pluginPropsData } = useGlobalSelector();
  const [accountList, setAccountList] = useState([]);
  const [emailInfoList, setEmailInfoList] = useState([]);
  const [emailInputDisabled, setEmailInputDisabled] = useState(false);
  const [matchEmailInfo, setMatchEmailInfo] = useState({});
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const formValues = watch();

  const getStatus = (fieldState: any) => {
    if (fieldState?.error?.message) {
      return 'error';
    }
    if (!fieldState.isDirty) {
      return undefined;
    }
    return fieldState.invalid ? 'error' : 'success';
  };

  const getDescribeSubscriptionEmailListV2 = () => {
    describeSubscriptionEmailListV2Handle({
      env: pluginPropsData.env,
      uin: pluginPropsData.uin,
      apiParams: {
        data: {
          Limit: -1,
          Offset: 0,
        },
      },
    }).then((rs) => {
      if (rs) {
        setEmailInfoList(rs.EmailDetails ?? []);
      }
    });
  };

  const getSubAccountsByMainAccount = () => {
    describeSubAccountsByMainAccountHandle({
      env: pluginPropsData.env,
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setAccountList(rs.SubAccountInfoList);
      }
    });
  };

  useEffect(() => {
    if (visible) {
      getSubAccountsByMainAccount();
      getDescribeSubscriptionEmailListV2();
    } else {
      reset();
      setEmailInputDisabled(false);
      setMatchEmailInfo({});
    }
  }, [visible]);
  useEffect(() => {
    if (formValues?.User) {
      const subUin = formValues.User?.split(':')[1];
      const item = emailInfoList.find((item) => item?.SubUin === subUin);
      if (item) {
        setValue('Email', item.Email);
        setEmailInputDisabled(true);
      } else {
        setValue('Email', '');
        setEmailInputDisabled(false);
      }
    }
  }, [formValues?.User, emailInfoList]);
  useEffect(() => {
    if (formValues?.Email) {
      const item = emailInfoList.find((item) => item?.Email === formValues?.Email);
      if (item) {
        setMatchEmailInfo(item);
      }
    }
  }, [formValues?.Email, emailInfoList]);

  return (
    <Modal
      visible={visible}
      caption="分配风险处理人"
      size={600}
      onClose={() => {
        onClose();
      }}
    >
      <Modal.Body>
        <div className={s['assign-tips']}>
          选择分配风险处理人后您可以根据我们的优化建议来消除风险，并跟踪该风险的变化情况。如您在处理风险的过程中遇到了疑问，也可以联系您的客户经理或提交工单。
        </div>
        <Form
          layout="fixed"
          fixedLabelWidth={50}
        >
          <Controller
            name="User"
            control={control}
            rules={{
              validate: (value) => (!value ? '请选择处理人' : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                required
                label="处理人"
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.User?.message as any}
              >
                <Select
                  searchable
                  {...field}
                  style={{ width: 450 }}
                  boxStyle={{ width: '250px' }}
                  placeholder="请选择处理人"
                  appearance="button"
                  options={accountList.map((item) => ({
                    text: `${item.Name}(${item.Uin})`,
                    value: `${item.Name}:${item.Uin}`,
                  }))}
                />
              </Form.Item>
            )}
          />
          <Controller
            name="Email"
            control={control}
            rules={{
              validate: (value) => ((!/^(?!.*\s)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value) && value?.trim()?.length > 0) ? '邮箱格式不正确' : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                label="邮箱"
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.Email?.message as any}
              >
                <Input
                  disabled={emailInputDisabled}
                  {...field}
                  style={{ width: 450 }}
                  placeholder="子账号未有邮箱，可补充填写以便接收风险分配通知"
                />
              </Form.Item>
            )}
          />
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          loading={submitLoading}
          type="primary"
           // eslint-disable-next-line @typescript-eslint/no-misused-promises
          onClick={handleSubmit((values) => {
            onSubmit(values, matchEmailInfo);
          })}
        >
          确定
        </Button>
        <Button type="weak" onClick={onClose}>
          取消
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
