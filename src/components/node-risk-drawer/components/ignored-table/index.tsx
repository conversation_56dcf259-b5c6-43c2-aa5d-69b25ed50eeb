/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useState, useRef } from 'react';
import {
  Table,
  Justify,
  Button,
  Icon,
  Bubble,
  message,
  Modal,
} from '@tencent/tea-component';
import TableSearchBox from '@src/components/table-search-box';
import { IDescribeIgnoredInstancesInNodeItem } from '@src/service/node-risk-drawer/index.type';
import { useGlobalSelector } from '@src/store/global/index';
import { reportEvent } from '@src/utils';
import { useNodeRiskDrawerStateSelector, changeNodeRiskDrawerState } from '@src/store/node-risk-drawer/index';
import { useDispatch } from 'react-redux';
import {
  describeIgnoredInstancesInNodeHandle,
} from '@src/service/node-risk-drawer/final-request';
import {
  updateInstanceToIgnoredStatusHandle,
} from '@src/service/common-service/final-request';
import { UpdateInstanceToClaimedOperateEnum, NodeInspectionTaskStatusEnum, EnvEnum } from '@src/constant/index';
import useUpdateShapesBar from '@src/hooks/useUpdateShapesBar';
import InstanceIdCopy from '@src/components/instance-id-copy';
import _ from 'lodash';
import s from './index.module.scss';

interface IIgnoredTabelProps {
  onUpdateCount?: () => Promise<void>;
}

const {
  pageable, selectable, autotip, scrollable,
} = Table.addons;

/**
 * 已忽略风险表格
 * @returns
 */
export default function IgnoredTabel({ onUpdateCount }: IIgnoredTabelProps = {}): React.ReactElement {
  const {
    pluginPropsData, selectNodeInfo, archScanRiskInfo, nodeRiskDrawerVisible, nodeInspectionTaskStatus,
  } = useGlobalSelector();
  const { ignoredTableFilter, ignoredTablePage } = useNodeRiskDrawerStateSelector();
  const { updateShapesBar } = useUpdateShapesBar();
  const [pageSize, setPageSize] = useState(10);

  const [selectedKeys, setSelectedKeys] = useState([]);
  const [tableData, setTableData] = useState<IDescribeIgnoredInstancesInNodeItem[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const selectedKeysRef = useRef([]);
  const containerRef = useRef(null);
  const dispatch = useDispatch();

  const columns = [
    {
      key: 'InstanceId',
      header: '实例',
      width: 150,
      render: (record: IDescribeIgnoredInstancesInNodeItem) => (
        <div className={s['risk-table-instance-column']}>
          <div className={s.cirle} />
          <div
            className={s['ignored-table-instance-inner']}
          >
            <InstanceIdCopy instanceId={record.InstanceId} url={record.Url} />
          </div>
        </div>
      ),
    },
    {
      key: 'IgnoredTime',
      header: '忽略时间',
      width: 150,
    },
    {
      key: 'IgnoredReasons',
      header: '原因',
      width: 50,
      render: (record) => (
        <Bubble
          arrowPointAtCenter
          placement="top"
          content={record.IgnoredReasons}
        >
          <Icon type="info" />
        </Bubble>
      ),
    },
    {
      key: 'IgnoredPerson',
      header: '处理人',
      width: 150,
    },
    {
      key: 'Operate',
      header: '操作',
      width: 100,
      render: (record) => (
        <>
          <Button
            disabled={pluginPropsData?.env === EnvEnum.ISA}
            type="link"
            onClick={() => {
              reportEvent(pluginPropsData.env as EnvEnum, {
                archId: pluginPropsData.archInfo.archId,
                nodeId: selectNodeInfo.key,
                nodeName: selectNodeInfo.name,
                subUin: pluginPropsData.uin,
                subUinName: pluginPropsData.userName,
                eventType: 'ignoredClickUnignore', // 已忽略—点击取消忽略
                instanceId: record.InstanceId,
              });
              selectedKeysRef.current = [record.InstanceId];
              setSelectedKeys([record.InstanceId]);
              updateInstanceToIgnoredStatus([record.InstanceId]);
            }}
          >
            取消忽略
          </Button>
        </>
      ),
    },
  ];

  const getTabelList = () => {
    const nodeUuid = selectNodeInfo.key;
    const riskItem = archScanRiskInfo.NodeRiskItems.find((item) => item.NodeUuid === nodeUuid);
    if (riskItem) {
      const filters = ignoredTableFilter.filter((item) => item?.attr?.key !== 'TagList');
      const filtersParams = [];
      filters.forEach((item) => {
        const obj = {
          Name: item.attr.key,
          Values: item.values.map((valueItem) => `${valueItem.key || ''}` || `${valueItem.name || ''}`),
        };
        filtersParams.push(obj);
      });
      let tagListParams: any = [];
      const filtersTag: any = ignoredTableFilter.find((item) => item?.attr?.key === 'TagList');
      if (filtersTag?.values) {
        tagListParams = filtersTag?.values?.map((valueItem) => ({
          Key: valueItem.key,
          Value: valueItem.name,
        }));
      }
      const apiParams = {
        MapId: pluginPropsData.archInfo.archId,
        NodeUuid: selectNodeInfo.key,
        TaskId: riskItem.LastSuccessTaskId,
        Offset: (ignoredTablePage - 1) * pageSize,
        Limit: pageSize,
        Filters: filtersParams,
        TagList: tagListParams,
      };
      setLoading(true);
      describeIgnoredInstancesInNodeHandle({
        env: pluginPropsData.env,
        apiParams: {
          data: apiParams,
        },
        uin: pluginPropsData.uin,
      }).then((rs) => {
        if (rs) {
          setTableData(rs.NodeIgnoredInstanceList);
          setTotal(rs.TotalCount);
        }
      }).finally(() => {
        setLoading(false);
      });
    }
  };

  const updateInstanceToIgnoredStatus = async (keys) => {
    await Modal.confirm({
      popupContainer: () => containerRef.current,
      message: '确认取消忽略当前所选实例？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const rs = await updateInstanceToIgnoredStatusHandle({
          env: pluginPropsData.env,
          apiParams: {
            data: {
              MapId: pluginPropsData.archInfo.archId,
              NodeUuid: selectNodeInfo.key,
              Operate: UpdateInstanceToClaimedOperateEnum.delete,
              InstanceIdList: keys,
              Reason: '',
              Person: pluginPropsData?.env === EnvEnum.ISA ? pluginPropsData.userName : (pluginPropsData.userName?.slice(0, pluginPropsData.userName.lastIndexOf('@')) ?? ''),
            },
          },
          uin: pluginPropsData.uin,
        });
        if (rs) {
          message.success({ content: '取消忽略成功' });
          getTabelList();
          // 更新角标
          updateShapesBar();
          // 取消忽略成功后更新节点风险数量
          onUpdateCount?.();
          setSelectedKeys([]);
        }
      },
    });
  };

  useEffect(() => {
    if (selectNodeInfo) {
      setTableData([]);
      setTotal(0);
    }
  }, [selectNodeInfo]);

  useEffect(() => {
    if (pluginPropsData.archInfo.archId && selectNodeInfo
       && archScanRiskInfo.NodeRiskItems.length && nodeRiskDrawerVisible) {
      getTabelList();
    }
  }, [pluginPropsData, selectNodeInfo, archScanRiskInfo,
    ignoredTablePage, pageSize, ignoredTableFilter, nodeRiskDrawerVisible]);

  useEffect(() => {
    if (nodeInspectionTaskStatus === NodeInspectionTaskStatusEnum.inspectionCompleted) {
      getTabelList();
    }
  }, [nodeInspectionTaskStatus]);

  return (
    <>
      <Table.ActionPanel>
        <Justify
          className={s['risk-table-justify']}
          left={(
            <>
              <Button
                disabled={selectedKeys.length === 0 || pluginPropsData?.env === EnvEnum.ISA}
                onClick={() => {
                  updateInstanceToIgnoredStatus(selectedKeys);
                }}
              >
                取消忽略
              </Button>
            </>
          )}
          right={(
            <TableSearchBox
              value={ignoredTableFilter}
              hasLevelFilter={false}
              onChange={(values) => {
                const valuesCopy = _.cloneDeep(values);
                const valuesCopyFilter = valuesCopy.filter((item) => !!item?.attr);
                valuesCopyFilter.forEach((item: any) => {
                  // eslint-disable-next-line no-param-reassign
                  delete item.attr?.render;
                  // eslint-disable-next-line no-param-reassign
                  delete item.attr?.values;
                });
                dispatch(changeNodeRiskDrawerState({
                  ignoredTableFilter: valuesCopyFilter,
                  ignoredTablePage: 1,
                }));
              }}
            />
          )}
        />
      </Table.ActionPanel>
      <Table
        ref={containerRef}
        verticalTop
        records={tableData}
        recordKey="InstanceId"
        columns={columns}
        bordered
        addons={[
          scrollable(
            {
              maxHeight: 'calc(100vh - 430px)',
            },
          ),
          pageable({
            pageIndex: ignoredTablePage,
            recordCount: total,
            onPagingChange: (query) => {
              dispatch(changeNodeRiskDrawerState({
                ignoredTablePage: query.pageIndex,
              }));
              setPageSize(query.pageSize);
            },
          }),
          selectable({
            value: selectedKeys,
            onChange: (keys) => {
              selectedKeysRef.current = keys;
              setSelectedKeys(keys);
            },
          }),
          autotip({
            isLoading: loading,
          }),
        ]}
      />
    </>
  );
}
