/* eslint-disable jsx-a11y/anchor-is-valid */
import React, {
  useEffect, useState, useRef, useMemo,
} from 'react';
import {
  Table,
  Justify,
  Button,
  Icon,
  Bubble,
  message,
} from '@tencent/tea-component';
import { reportEvent } from '@src/utils';
import TableSearchBox from '@src/components/table-search-box';
import { INodeRiskInstanceItem } from '@src/service/node-risk-drawer/index.type';
import IgnoreModal from '@src/components/ignore-modal';
import { useGlobalSelector } from '@src/store/global/index';
import { useNodeRiskDrawerStateSelector, changeNodeRiskDrawerState } from '@src/store/node-risk-drawer/index';
import {
  describeRiskInstancesInNodeHandle,
  updateInstanceToClaimedStatusHandle,
} from '@src/service/node-risk-drawer/final-request';
import {
  EnvEnum,
  NodeInspectionTaskStatusEnum,
  UpdateInstanceToClaimedOperateEnum,
} from '@src/constant';
import {
  updateInstanceToIgnoredStatusHandle,
} from '@src/service/common-service/final-request';
import useUpdateShapesBar from '@src/hooks/useUpdateShapesBar';
import { useDispatch } from 'react-redux';
import _ from 'lodash';
import InstanceIdCopy from '@src/components/instance-id-copy';
import {
  createSubscriptionEmailV2Handle,
  updateSubscriptionEmailV2Handle,
} from '@src/service/governance-progress-drawer/final-request';
import RiskExpandList from '../risk-expand-list';
import AssignModal from '../assign-modal';
import s from './index.module.scss';

interface IRiskTabelProps {
  onUpdateCount?: () => Promise<void>;
}

const {
  pageable, selectable, expandable, autotip, scrollable,
} = Table.addons;

/**
 * 节点风险表格
 * @returns
 */
export default function RiskTabel({ onUpdateCount }: IRiskTabelProps = {}): React.ReactElement {
  const {
    pluginPropsData, selectNodeInfo, archScanRiskInfo, nodeRiskDrawerVisible, nodeInspectionTaskStatus,
  } = useGlobalSelector();
  const { riskTableFilter, riskTablePage } = useNodeRiskDrawerStateSelector();
  const { updateShapesBar } = useUpdateShapesBar();
  const dispatch = useDispatch();

  const [pageSize, setPageSize] = useState(10);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [tableData, setTableData] = useState<INodeRiskInstanceItem[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [assignModalLoading, setAssignModalLoading] = useState(false);
  const [ignoreModalLoading, setIgnoreModalLoading] = useState(false);
  const [ignoreModalVisible, setIgnoreModalVisible] = useState(false);
  const selectedKeysRef = useRef([]);

  const lastSuccessTaskId = useMemo(() => {
    const nodeUuid = selectNodeInfo.key;
    const riskItem = archScanRiskInfo.NodeRiskItems.find((item) => item.NodeUuid === nodeUuid);
    return riskItem?.LastSuccessTaskId || '';
  }, [archScanRiskInfo, selectNodeInfo]);

  const columns = [
    {
      key: 'InstanceId',
      header: '实例',
      width: 160,
      render: (record: INodeRiskInstanceItem) => (
        <div className={s['risk-table-instance-column']}>
          {
            record.HighRiskCount ? (
              <div className={s['high-cirle']} />
            ) : (
              <div className={s['mid-cirle']} />
            )
          }
          <div
            className={s['risk-table-instance-inner']}
            style={{ maxWidth: record.IsChange ? 'calc(100% - 40px)' : 'calc(100% - 18px)' }}
          >
            <InstanceIdCopy instanceId={record.InstanceId} url={record.Url} />
          </div>
          {
            record.IsChange && (
            <Bubble
              arrowPointAtCenter
              placement="top"
              content="实例风险已解决，建议巡检更新"
            >
              <Icon type="info" style={{ marginLeft: 5 }} />
            </Bubble>
            )
          }
        </div>
      ),
    },
    {
      key: 'Tag',
      header: '标签',
      render: (record: INodeRiskInstanceItem) => {
        try {
          const tag = record.Tag;
          const tagArray = tag ? JSON.parse(tag) : [];
          if (tagArray.length === 0) {
            return '-';
          }
          return (
            <Bubble
              arrowPointAtCenter
              placement="top"
              content={(
                <div>
                  {
                    tagArray.map((tag) => (
                      <div key={tag.Key}>
                        <span>
                          {tag.Key}
                          ：
                        </span>
                        <span>{tag.Value}</span>
                      </div>
                    ))
                  }
                </div>
              )}
            >
              <Icon type="tag" />
            </Bubble>
          );
        } catch (error) {
          return '暂无';
        }
      },
    },
    {
      key: 'Operate',
      header: '操作',
      render: (record) => (
        <>
          <Button
            type="link"
            onClick={() => {
              reportEvent(pluginPropsData.env as EnvEnum, {
                archId: pluginPropsData.archInfo.archId,
                nodeId: selectNodeInfo.key,
                nodeName: selectNodeInfo.name,
                subUin: pluginPropsData.uin,
                subUinName: pluginPropsData.userName,
                eventType: 'riskClickInstanceClaimed',
                instanceId: record.InstanceId,
              });
              setSelectedKeys([record.InstanceId]);
              selectedKeysRef.current = [record.InstanceId];
              setAssignModalVisible(true);
            }}
          >
            分配
          </Button>
          <Button
            disabled={pluginPropsData?.env === EnvEnum.ISA}
            type="link"
            onClick={() => {
              reportEvent(pluginPropsData.env as EnvEnum, {
                archId: pluginPropsData.archInfo.archId,
                nodeId: selectNodeInfo.key,
                nodeName: selectNodeInfo.name,
                subUin: pluginPropsData.uin,
                subUinName: pluginPropsData.userName,
                eventType: 'riskClickInstanceIgnore',
                instanceId: record.InstanceId,
              });
              setSelectedKeys([record.InstanceId]);
              selectedKeysRef.current = [record.InstanceId];
              setIgnoreModalVisible(true);
            }}
          >
            忽略
          </Button>
        </>
      ),
    },
  ];

  const getTabelList = () => {
    const nodeUuid = selectNodeInfo.key;
    const riskItem = archScanRiskInfo.NodeRiskItems.find((item) => item.NodeUuid === nodeUuid);
    if (riskItem) {
      const filters = riskTableFilter.filter((item) => item?.attr?.key !== 'TagList');
      const filtersParams = [];
      filters.forEach((item) => {
        const obj = {
          Name: item.attr.key,
          Values: item.values.map((valueItem) => `${valueItem.key || ''}` || `${valueItem.name || ''}`),
        };
        filtersParams.push(obj);
      });
      let tagListParams: any = [];
      const filtersTag: any = riskTableFilter.find((item) => item?.attr?.key === 'TagList');
      if (filtersTag?.values) {
        tagListParams = filtersTag?.values?.map((valueItem) => ({
          Key: valueItem.key,
          Value: valueItem.name,
        }));
      }
      const apiParams = {
        MapId: pluginPropsData.archInfo.archId,
        NodeUuid: selectNodeInfo.key,
        TaskId: riskItem.LastSuccessTaskId,
        Offset: (riskTablePage - 1) * pageSize,
        Limit: pageSize,
        Filters: filtersParams,
        TagList: tagListParams,
      };
      setLoading(true);
      describeRiskInstancesInNodeHandle({
        env: pluginPropsData.env,
        apiParams: {
          data: apiParams,
        },
        uin: pluginPropsData.uin,
      }).then((rs) => {
        if (rs) {
          setTableData(rs.NodeRiskInstanceList);
          setTotal(rs.TotalCount);
        }
      }).finally(() => {
        setLoading(false);
      });
    }
  };

  const updateInstanceToClaimedStatus = async (values, emailInfo) => {
    setAssignModalLoading(true);
    const userInfoArr = values.User?.split(':');
    try {
      await updateInstanceToClaimedStatusHandle({
        env: pluginPropsData.env,
        apiParams: {
          data: {
            MapId: pluginPropsData.archInfo.archId,
            NodeUuid: selectNodeInfo.key,
            Operate: UpdateInstanceToClaimedOperateEnum.add,
            InstanceIdList: selectedKeysRef.current,
            ClaimPerson: userInfoArr[0],
            ClaimUin: userInfoArr[1],
          },
        },
        uin: pluginPropsData.uin,
      });
      if (values.Email) {
        if (emailInfo.Id !== undefined) {
          await updateSubscriptionEmailV2Handle({
            env: pluginPropsData.env,
            apiParams: {
              data: {
                Id: emailInfo.Id,
                SubUin: userInfoArr[1],
                UserName: emailInfo.UserName ? emailInfo.UserName : userInfoArr[0],
                Email: values.Email,
              },
            },
            uin: pluginPropsData.uin,
          });
        } else {
          await createSubscriptionEmailV2Handle({
            env: pluginPropsData.env,
            apiParams: {
              data: {
                SubUin: userInfoArr[1],
                UserName: userInfoArr[0],
                Email: values.Email,
              },
            },
            uin: pluginPropsData.uin,
          });
        }
      }
      setSelectedKeys([]);
      message.success({ content: '分配成功' });
      getTabelList();
      setAssignModalVisible(false);
      setAssignModalLoading(false);
      // 分配成功后更新节点风险数量
      onUpdateCount?.();
      setSelectedKeys([]);
    } catch (err) {
      setAssignModalLoading(false);
    }
  };

  const updateInstanceToIgnoredStatus = async (reason) => {
    setIgnoreModalLoading(true);
    updateInstanceToIgnoredStatusHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          MapId: pluginPropsData.archInfo.archId,
          NodeUuid: selectNodeInfo.key,
          Operate: UpdateInstanceToClaimedOperateEnum.add,
          InstanceIdList: selectedKeysRef.current,
          Reason: reason,
          Person: pluginPropsData?.env === EnvEnum.ISA ? pluginPropsData.userName : (pluginPropsData.userName?.slice(0, pluginPropsData.userName.lastIndexOf('@')) ?? ''),
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setSelectedKeys([]);
        message.success({ content: '忽略成功' });
        getTabelList();
        setIgnoreModalVisible(false);
        // 忽略成功后需要重新更新角标
        updateShapesBar();
        // 忽略成功后更新节点风险数量
        onUpdateCount?.();
        setSelectedKeys([]);
      }
    }).finally(() => {
      setIgnoreModalLoading(false);
    });
  };

  useEffect(() => {
    if (selectNodeInfo) {
      setTableData([]);
      setTotal(0);
    }
  }, [selectNodeInfo]);

  useEffect(() => {
    if (pluginPropsData.archInfo.archId && selectNodeInfo
       && archScanRiskInfo.NodeRiskItems.length && nodeRiskDrawerVisible) {
      getTabelList();
    }
  }, [pluginPropsData, selectNodeInfo, archScanRiskInfo, riskTablePage,
    pageSize, riskTableFilter, nodeRiskDrawerVisible]);

  useEffect(() => {
    if (nodeInspectionTaskStatus === NodeInspectionTaskStatusEnum.inspectionCompleted) {
      getTabelList();
    }
  }, [nodeInspectionTaskStatus]);

  return (
    <>
      <Table.ActionPanel>
        <Justify
          className={s['risk-table-justify']}
          left={(
            <>
              <Button
                disabled={selectedKeys.length === 0}
                type="primary"
                onClick={() => {
                  reportEvent(pluginPropsData.env as EnvEnum, {
                    archId: pluginPropsData.archInfo.archId,
                    nodeId: selectNodeInfo.key,
                    nodeName: selectNodeInfo.name,
                    subUin: pluginPropsData.uin,
                    subUinName: pluginPropsData.userName,
                    eventType: 'riskClickBatchInstanceClaimed',
                  });
                  setAssignModalVisible(true);
                }}
              >
                分配
              </Button>
              <Button
                disabled={selectedKeys.length === 0 || pluginPropsData?.env === EnvEnum.ISA}
                onClick={() => {
                  reportEvent(pluginPropsData.env as EnvEnum, {
                    archId: pluginPropsData.archInfo.archId,
                    nodeId: selectNodeInfo.key,
                    nodeName: selectNodeInfo.name,
                    subUin: pluginPropsData.uin,
                    subUinName: pluginPropsData.userName,
                    eventType: 'riskClickBatchInstanceIgnore',
                  });
                  setIgnoreModalVisible(true);
                }}
              >
                忽略
              </Button>
            </>
          )}
          right={(
            <TableSearchBox
              value={riskTableFilter}
              onChange={(values) => {
                const valuesCopy = _.cloneDeep(values);
                const valuesCopyFilter = valuesCopy.filter((item) => !!item?.attr);
                valuesCopyFilter.forEach((item: any) => {
                  // eslint-disable-next-line no-param-reassign
                  delete item.attr?.render;
                  // eslint-disable-next-line no-param-reassign
                  delete item.attr?.values;
                });
                dispatch(changeNodeRiskDrawerState({
                  riskTableFilter: valuesCopyFilter,
                  riskTablePage: 1,
                }));
              }}
            />
          )}
        />
      </Table.ActionPanel>
      <Table
        className={s['risk-table']}
        verticalTop
        records={tableData}
        recordKey="InstanceId"
        columns={columns}
        bordered
        addons={[
          scrollable(
            {
              maxHeight: 'calc(100vh - 430px)',
            },
          ),
          pageable({
            pageIndex: riskTablePage,
            recordCount: total,
            onPagingChange: (query) => {
              dispatch(changeNodeRiskDrawerState({
                riskTablePage: query.pageIndex,
              }));
              setPageSize(query.pageSize);
            },
          }),
          selectable({
            value: selectedKeys,
            onChange: (keys) => {
              selectedKeysRef.current = keys;
              setSelectedKeys(keys);
            },
          }),
          expandable({
            expandedKeys,
            onExpandedKeysChange: (keys, { event }) => {
              const lastKey = keys[keys.length - 1];
              event.stopPropagation();
              setExpandedKeys([lastKey]);
              if (lastKey) {
                reportEvent(pluginPropsData.env as EnvEnum, {
                  archId: pluginPropsData.archInfo.archId,
                  nodeId: selectNodeInfo.key,
                  nodeName: selectNodeInfo.name,
                  subUin: pluginPropsData.uin,
                  subUinName: pluginPropsData.userName,
                  eventType: 'riskClickExpandInstanceRisk', // 有风险—展开实例风险
                  instanceId: lastKey,
                });
              }
            },
            render(record) {
              return (
                <RiskExpandList
                  mapId={pluginPropsData.archInfo.archId}
                  nodeUuid={selectNodeInfo.key}
                  taskId={lastSuccessTaskId}
                  instanceId={record.InstanceId}
                  region={record.Region}
                  onCustomerServiceClick={(env, params) => {
                    reportEvent(env, {
                      ...params,
                      eventType: 'riskClickAICustomerService', // 有风险—唤起智能客服
                    });
                  }}
                  onIgnoreClick={(env, params) => {
                    reportEvent(env, {
                      ...params,
                      eventType: 'riskClickIgnoreRisk', // 有风险—忽略风险
                    });
                  }}
                  onSuccess={() => {
                    getTabelList();
                  }}
                />
              );
            },
            gapCell: 2,
            rowExpand: true,
          }),
          autotip({
            isLoading: loading,
          }),
        ]}
      />
      <AssignModal
        visible={assignModalVisible}
        submitLoading={assignModalLoading}
        onClose={() => {
          setAssignModalVisible(false);
        }}
        onSubmit={(user, emailInfo) => {
          updateInstanceToClaimedStatus(user, emailInfo);
        }}
      />
      <IgnoreModal
        visible={ignoreModalVisible}
        submitLoading={ignoreModalLoading}
        showSubTips={false}
        onClose={() => {
          setIgnoreModalVisible(false);
        }}
        onSubmit={(reason) => {
          updateInstanceToIgnoredStatus(reason);
        }}
      />
    </>
  );
}
