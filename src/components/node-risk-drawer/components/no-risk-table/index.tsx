/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useState } from 'react';
import {
  Table,
  Justify,
  Icon,
  Bubble,
} from '@tencent/tea-component';
import TableSearchBox from '@src/components/table-search-box';
import { INodeSafeInstanceItem } from '@src/service/node-risk-drawer/index.type';
import { useGlobalSelector } from '@src/store/global/index';
import { useNodeRiskDrawerStateSelector, changeNodeRiskDrawerState } from '@src/store/node-risk-drawer/index';
import { useDispatch } from 'react-redux';
import { NodeInspectionTaskStatusEnum } from '@src/constant/index';
import {
  describeSafeInstancesInNodeHandle,
} from '@src/service/node-risk-drawer/final-request';
import InstanceIdCopy from '@src/components/instance-id-copy';
import _ from 'lodash';
import s from './index.module.scss';

const {
  pageable, autotip, scrollable,
} = Table.addons;

/**
 * 无风险表格
 * @returns
 */
export default function NoRiskTabel(): React.ReactElement {
  const {
    pluginPropsData, selectNodeInfo, archScanRiskInfo, nodeRiskDrawerVisible, nodeInspectionTaskStatus,
  } = useGlobalSelector();
  const { noRiskTableFilter, noRiskTablePage } = useNodeRiskDrawerStateSelector();
  const dispatch = useDispatch();
  const [pageSize, setPageSize] = useState(10);

  const [tableData, setTableData] = useState<INodeSafeInstanceItem[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  const columns = [
    {
      key: 'InstanceId',
      header: '实例',
      width: 150,
      render: (record: INodeSafeInstanceItem) => (
        <div className={s['risk-table-instance-column']}>
          <div className={s.cirle} />
          <div
            className={s['no-risk-table-instance-inner']}
          >
            <InstanceIdCopy instanceId={record.InstanceId} url={record.Url} />
          </div>
        </div>
      ),
    },
    {
      key: '备注',
      header: '备注',
      width: 150,
      render: (record) => {
        if (record.IsIgnored) {
          return '实例曾被忽略，建议及时巡检';
        }
        return '-';
      },
    },
    {
      key: 'Tag',
      header: '标签',
      width: 100,
      render: (record) => {
        try {
          const tag = record.Tag;
          const tagArray = tag ? JSON.parse(tag) : [];
          if (tagArray.length === 0) {
            return '-';
          }
          return (
            <Bubble
              arrowPointAtCenter
              placement="top"
              content={(
                <div>
                  {
                    tagArray.map((tag) => (
                      <div key={tag.Key}>
                        <span>
                          {tag.Key}
                          ：
                        </span>
                        <span>{tag.Value}</span>
                      </div>
                    ))
                  }
                </div>
              )}
            >
              <Icon type="tag" />
            </Bubble>
          );
        } catch (error) {
          return '暂无';
        }
      },
    },
  ];

  const getTabelList = () => {
    const nodeUuid = selectNodeInfo.key;
    const riskItem = archScanRiskInfo.NodeRiskItems.find((item) => item.NodeUuid === nodeUuid);
    if (riskItem) {
      const filters = noRiskTableFilter.filter((item) => item?.attr?.key !== 'TagList');
      const filtersParams = [];
      filters.forEach((item) => {
        const obj = {
          Name: item.attr.key,
          Values: item.values.map((valueItem) => `${valueItem.key || ''}` || `${valueItem.name || ''}`),
        };
        filtersParams.push(obj);
      });
      let tagListParams: any = [];
      const filtersTag: any = noRiskTableFilter.find((item) => item?.attr?.key === 'TagList');
      if (filtersTag?.values) {
        tagListParams = filtersTag?.values?.map((valueItem) => ({
          Key: valueItem.key,
          Value: valueItem.name,
        }));
      }
      const apiParams = {
        MapId: pluginPropsData.archInfo.archId,
        NodeUuid: selectNodeInfo.key,
        TaskId: riskItem.LastSuccessTaskId,
        Offset: (noRiskTablePage - 1) * pageSize,
        Limit: pageSize,
        Filters: filtersParams,
        TagList: tagListParams,
      };
      setLoading(true);
      describeSafeInstancesInNodeHandle({
        env: pluginPropsData.env,
        apiParams: {
          data: apiParams,
        },
        uin: pluginPropsData.uin,
      }).then((rs) => {
        if (rs) {
          setTableData(rs.NodeSafeInstanceList);
          setTotal(rs.TotalCount);
        }
      }).finally(() => {
        setLoading(false);
      });
    }
  };

  useEffect(() => {
    if (selectNodeInfo) {
      setTableData([]);
      setTotal(0);
    }
  }, [selectNodeInfo]);

  useEffect(() => {
    if (pluginPropsData.archInfo.archId && selectNodeInfo
       && archScanRiskInfo.NodeRiskItems.length && nodeRiskDrawerVisible) {
      getTabelList();
    }
  }, [pluginPropsData, selectNodeInfo, archScanRiskInfo,
    noRiskTablePage, pageSize, noRiskTableFilter, nodeRiskDrawerVisible]);

  useEffect(() => {
    if (nodeInspectionTaskStatus === NodeInspectionTaskStatusEnum.inspectionCompleted) {
      getTabelList();
    }
  }, [nodeInspectionTaskStatus]);

  return (
    <>
      <Table.ActionPanel>
        <Justify
          className={s['risk-table-justify']}
          right={(
            <TableSearchBox
              value={noRiskTableFilter}
              hasLevelFilter={false}
              onChange={(values) => {
                const valuesCopy = _.cloneDeep(values);
                const valuesCopyFilter = valuesCopy.filter((item) => !!item?.attr);
                valuesCopyFilter.forEach((item: any) => {
                  // eslint-disable-next-line no-param-reassign
                  delete item.attr?.render;
                  // eslint-disable-next-line no-param-reassign
                  delete item.attr?.values;
                });
                dispatch(changeNodeRiskDrawerState({
                  noRiskTableFilter: valuesCopyFilter,
                  noRiskTablePage: 1,
                }));
              }}
            />
          )}
        />
      </Table.ActionPanel>
      <Table
        verticalTop
        records={tableData}
        recordKey="InstanceId"
        columns={columns}
        bordered
        addons={[
          scrollable(
            {
              maxHeight: 'calc(100vh - 430px)',
            },
          ),
          pageable({
            pageIndex: noRiskTablePage,
            recordCount: total,
            onPagingChange: (query) => {
              dispatch(changeNodeRiskDrawerState({
                noRiskTablePage: query.pageIndex,
              }));
              setPageSize(query.pageSize);
            },
          }),
          autotip({
            isLoading: loading,
          }),
        ]}
      />
    </>
  );
}
