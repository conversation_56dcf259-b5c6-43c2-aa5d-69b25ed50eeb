.risk-table-justify {
  :global {
    .sdk-cloud-inspection-justify-grid__col--right {
      width: 400px;
    }
  }
}

.risk-table-instance-column {
  display: flex;
  overflow: hidden;
  width: 100%;
  align-items: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  
  .high-cirle {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    margin-right: 10px;
    background-color: #e54545;
  }

  .mid-cirle {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    margin-right: 10px;
    background-color: #ff7200;
  }

  .instance-id-div {
    overflow: hidden;
    width: 0;
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .claimed-table-instance-inner {
    width: 0;
    flex: 1;
  }
}

.claimed-table {
  :global {
    .tr__detailrow {
      td {
        background-color: #fff !important;
      }
    }
  }
}