import React, {
  useRef, useState, useEffect, useMemo,
} from 'react';
import { Drawer } from '@tencent/tea-component';
import cn from 'classnames';
import s from './index.module.scss';
import ErrorBoundary from '../error-boundary';

const DEFAULT_STYLE = {
  width: 550,
  minWidth: 300,
  maxWidth: 1500,
  zIndex: 999,
};

interface PluginDrawerProps {
  drawerVisible?: boolean;
  drawerProps?: any;
  closeHandler?: any;

  size?: 'm' | 'l';
  children?: React.ReactNode;
  title?: React.ReactNode;
  footer?: React.ReactNode;
  subtitle?: React.ReactNode;
  extra?: object; // 自定义其他属性/覆盖其他属性
  style?: React.CSSProperties;
  className?: string;
  draggable?: boolean; // 默认为true，抽屉可拖拽

  maxSize?: number; // 最大宽度
  minSize?: number; // 最小宽度

  ref?: React.RefObject<HTMLDivElement>;

  onWidthChange?: (width: number) => void;
}

// 添加宽度解析函数，有可能有字符串类型如500px
function parseWidth(width: string | number): number {
  if (!width) return DEFAULT_STYLE.width;
  if (typeof width === 'number') {
    return width;
  }

  // 提取数字部分（支持小数）
  const numMatch = width?.match(/\d+(\.\d+)?/);
  if (numMatch) {
    return parseFloat(numMatch[0]);
  }

  return DEFAULT_STYLE.width;
}

/**
 * 提供给插件的添加可拖拽能力的抽屉
 * @param props
 * @returns
 */
export default function DraggableDrawer(props: PluginDrawerProps): React.ReactElement {
  const {
    drawerVisible,
    drawerProps,
    closeHandler,
    size,
    children,
    title,
    footer,
    extra,
    style,
    className,
    minSize,
    maxSize,
    onWidthChange = () => {},
  } = props;

  const [drawerWidth, setDrawerWidth] = useState<number>(parseWidth(drawerProps?.style?.width) ?? DEFAULT_STYLE.width);
  const isDragging = useRef(false);
  const startX = useRef(0);
  const startWidth = useRef(0);

  useEffect(() => {
    if (drawerProps?.style?.width) {
      setDrawerWidth(parseWidth(drawerProps.style.width));
    }
  }, [drawerProps?.style?.width]);

  useEffect(() => {
    if (drawerProps?.maxSize || maxSize) {
      DEFAULT_STYLE.maxWidth = drawerProps?.maxSize ?? maxSize;
    }
  }, [drawerProps?.maxSize, maxSize]);

  useEffect(() => {
    if (drawerProps?.minSize || minSize) {
      DEFAULT_STYLE.minWidth = drawerProps?.minSize ?? minSize;
    }
  }, [drawerProps?.minSize, minSize]);

  const handleMouseDown = (e: React.MouseEvent) => {
    isDragging.current = true;
    startX.current = e.clientX;
    startWidth.current = drawerWidth;
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.current) return;

    const diff = e.clientX - startX.current;
    const newWidth = startWidth.current - diff;
    // 限制最小和最大宽度
    if (newWidth > DEFAULT_STYLE.minWidth && newWidth < DEFAULT_STYLE.maxWidth) {
      setDrawerWidth(newWidth);
    }
  };

  const handleMouseUp = () => {
    isDragging.current = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  };

  // eslint-disable-next-line max-len
  const startDragMode = useMemo(() => drawerProps?.draggable !== false && drawerProps?.draggable !== null, [drawerProps?.draggable]);

  useEffect(() => {
    // 没有手动指定为false / null，则默认开启拖拽
    if (startDragMode) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [drawerProps?.draggable]);

  useEffect(() => {
    onWidthChange?.(drawerWidth);
  }, [drawerWidth]);

  return (
    <Drawer
      title={drawerProps?.title ?? title}
      size={drawerProps?.size ?? size ?? 'm'}
      style={{ ...(drawerProps?.style ?? style ?? DEFAULT_STYLE), width: drawerWidth }}
      placement="right"
      className={cn(s['draggable-drawer'], drawerProps?.className ?? className)}
      onClose={closeHandler}
      visible={drawerVisible}
      footer={drawerProps?.footer ?? footer}
      destroyOnClose={false}
      maskStyle={{
        zIndex: 999,
      }}
      {
        ...(drawerProps?.extra ?? extra ?? {})
      }
    >
      <div
        className={startDragMode ? s['drag-handle'] : null}
        onMouseDown={handleMouseDown}
      />

      {/* @ts-ignore */}
      <ErrorBoundary>
        {drawerProps?.children ?? children ?? <></>}
      </ErrorBoundary>
    </Drawer>
  );
}
