import React, { useState, useEffect } from 'react';
import {
  Table,
  Justify,
  Button,
  SearchBox,
  Card,
  Layout,
} from '@tencent/tea-component';
import {
  describeGroupAndProductInfosHandle,
  listIgnoreStrategiesHandle,
  describeConfigHandle,
  modifyArchIgnoreStrategiesHandle,
  describeIgnoredStrategyHandle,
} from '@src/service/inspection-settings-modal/final-request';
import { useGlobalSelector } from '@src/store/global/index';
import {
  useInspectionsSettingsModalStateSelector,
  changeInspectionsSettingsModalState,
} from '@src/store/inspection-settings-modal/index';
import { useDispatch } from 'react-redux';
import { app } from '@tea/app';
import { t } from '@tea/app/i18n';
import AsyncSwitch from '../async-switch/index';
import ConditionEditor from '../condition-editor/index';
import s from './index.module.scss';

const { Content } = Layout;
const {
  pageable, filterable, scrollable, autotip,
} = Table.addons;

// 类别分类项
const TYPE_OPTS = ['安全', '可靠', '性能', '成本', '服务限制'];

enum RuleMode {
  VIEW = 'view',
  EDIT = 'edit',
}

interface ISettingsTableProps {
  currentMapId: string;
}

export default function SettingsTable(props: ISettingsTableProps) {
  const {
    currentMapId,
  } = props;
  const { pluginPropsData } = useGlobalSelector();
  const { products } = useInspectionsSettingsModalStateSelector();
  const dispatch = useDispatch();
  // 部分接口需要传的参数
  const apiParams = { Filters: [{ Name: 'mapId', Values: [currentMapId] }] };
  // 云产品选项
  const [PRODUCT_MAP, setPRODUCTMAP] = useState({});
  const [PRODUCT_OPTS, setPRODUCTOPTS] = useState([]);
  // 输入框筛选值
  const [searchInput, setSearchInput] = useState('');
  // 类别筛选值
  const [types, setTypes] = useState<Array<string>>([]);
  // 表格数据
  const [strategyList, setStrategyList] = useState([]);
  // 输入框筛选之后的表格数据
  const [filterStrategyList, setFilterStrategyList] = useState([]);
  // 当前页码
  const [pageIndex, setPageIndex] = useState(1);
  // 加载条
  const [loading, setLoading] = useState(false);
  // 加载错误
  const [error, setError] = useState(false);
  // 自定义阈值弹窗
  const [showConditionEditor, setShowConditionEditor] = useState<boolean>(false);
  // 当前点击的评估项
  const [currentStrategyId, setCurrentStrategyId] = useState<number>(0);
  // 巡检中忽略的巡检项列表
  const [onInspectIgnoreList, setOnInspectIgnoreList] = useState([]);
  const [ruleModalMode, setRuleModalMode] = useState(RuleMode.VIEW);

  let filteredRecords = filterStrategyList.slice();
  if (types.length > 0) {
    filteredRecords = filteredRecords.filter((record) => types.indexOf(record.type) > -1);
  }
  if (products.length > 0) {
    filteredRecords = filteredRecords.filter((record) => products.indexOf(record.product) > -1);
  }

  const getDescribeIgnoredStrategy = async () => {
    try {
      const res = await describeIgnoredStrategyHandle({
        env: pluginPropsData.env,
        filters: [{ Name: 'archId', Values: [currentMapId] }],
      });
      if (!res) {
        return;
      }
      setOnInspectIgnoreList(res.IgnoredStrategyList || []);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{msg}}', { msg }));
    }
  };

  useEffect(() => {
    getProductsGroupsInfo();
    getDescribeIgnoredStrategy();
  }, []);

  // 输入框值变化时过滤表数据
  useEffect(() => {
    setFilterStrategyList(strategyList
      .filter((strategy) => strategy.strategyName.toLowerCase().indexOf(searchInput.toLowerCase()) > -1));
  }, [searchInput]);

  useEffect(() => {
    getStrategies();
  }, [PRODUCT_MAP]);

  // 获取产品和维度信息
  const getProductsGroupsInfo = async () => {
    try {
      const res = await describeGroupAndProductInfosHandle({
        env: pluginPropsData.env,
      });
      if (!res) {
        return;
      }
      const tmp = [];
      const productDict = {};
      res.Products.forEach((item) => {
        productDict[item.Product] = item.Name;
        tmp.push({ value: item.Product, text: item.Name });
      });
      setPRODUCTMAP(productDict);
      setPRODUCTOPTS(tmp);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{msg}}', { msg }));
    }
  };

  // 拉取评估项列表数据
  const getStrategies = async () => {
    setSearchInput('');
    setLoading(true);
    setError(false);
    setStrategyList([]);
    setFilterStrategyList([]);
    try {
      const res = await listIgnoreStrategiesHandle({
        env: pluginPropsData.env,
        filters: apiParams.Filters,
      });
      if (!res) {
        return;
      }
      const igStrategyIds: Array<number> = res.StrategyIds;
      const configList = await describeConfigHandle({
        env: pluginPropsData.env,
        filters: apiParams.Filters,
      });
      if (!configList) {
        return;
      }
      const groupList = configList.Groups;
      const tempStrategyList = [];

      groupList.forEach((group) => {
        group.Strategies.forEach((configStrategy) => {
          tempStrategyList.push({
            strategyId: configStrategy.StrategyId,
            type: group.Name,
            productShortName: configStrategy.Product,
            product: configStrategy.Product,
            strategyName: configStrategy.Name,
            strategyDescribe: configStrategy.Desc,
            IsSupportCustom: configStrategy.IsSupportCustom,
            isAssessing: !igStrategyIds.includes(configStrategy.StrategyId),
            Conditions: configStrategy?.Conditions || [],
          });
        });
      });
      sortStragegyList(tempStrategyList);
      setStrategyList(tempStrategyList);
      setFilterStrategyList(tempStrategyList);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{Message}}', { msg }));
      setError(true);
    }
    setLoading(false);
  };

  // 排序评估项列表，排序方式：类别(依次为安全，可靠，性能，成本，服务限制) -> 产品名称(字母顺序，忽略大小写) -> 策略ID（由小到大）
  const sortStragegyList = (strategyList): void => {
    const rankMap = new Map([
      ['安全', 0],
      ['可靠', 1],
      ['性能', 2],
      ['成本', 3],
      ['服务限制', 4],
    ]);
    strategyList.sort((prev, curr) => {
      if (typeof rankMap.get(prev.type) === 'number' && typeof rankMap.get(curr.type) === 'number') {
        const typeNumberDiff = rankMap.get(prev.type) - rankMap.get(curr.type);
        if (typeNumberDiff !== 0) {
          return typeNumberDiff;
        } if (prev.productShortName !== curr.productShortName) {
          return prev.productShortName.toLowerCase() < curr.productShortName.toLowerCase() ? -1 : 1;
        }
        return prev.strategyId - curr.strategyId;
      }
      return 0;
    });
  };

  // 根据评估项开关更新评估项列表
  const updateStrategyList = (id: number, checked: boolean): void => {
    // eslint-disable-next-line no-underscore-dangle
    const _strategyList = strategyList.slice();
    _strategyList.forEach((strategy) => {
      if (strategy.strategyId === id) {
        // eslint-disable-next-line no-param-reassign
        strategy.isAssessing = checked;
      }
    });
    setStrategyList(_strategyList);
  };

  const updateIngoreStrategies = (values: any) => {
    modifyArchIgnoreStrategiesHandle({
      env: pluginPropsData.env,
      modifyParams: values,
    });
  };

  return (
    <section style={{ border: '1px solid #ddd' }}>
      <Content className={s['settings-table-content']}>
        <Content.Body>
          <Table.ActionPanel>
            <Justify
              right={
                <>
                  <SearchBox
                    onSearch={(value) => {
                      setSearchInput(value);
                      setPageIndex(1);
                    }}
                    onClear={() => {
                      setSearchInput('');
                      setPageIndex(1);
                    }}
                    placeholder="按照评估项名称进行过滤"
                    style={{ width: '400px' }}
                    maxLength={60}
                  />
                  <Button
                    icon="refresh"
                    onClick={() => {
                      getStrategies();
                      getDescribeIgnoredStrategy();
                    }}
                  />
                </>
              }
            />
          </Table.ActionPanel>
          <Card>
            <Table
              verticalTop
              records={filteredRecords}
              // recordKey="instanceId"
              columns={[
                {
                  key: 'type',
                  header: t('类别'),
                  width: '8%',
                },
                {
                  key: 'product',
                  header: t('云产品'),
                  width: '15%',
                  render: (item: any) => <>{PRODUCT_MAP[item.product]}</>,
                },
                {
                  key: 'strategyName',
                  header: t('评估项名称'),
                  width: '20%',
                },
                {
                  key: 'strategyDescribe',
                  header: t('评估项描述'),
                },
                {
                  key: 'assessSwitch',
                  header: t('评估项开关'),
                  render: (record: any) => (
                    <AsyncSwitch
                      isAssessing={record.isAssessing}
                      data={{ StrategyId: record.strategyId, StrategyName: record.strategyName, ...apiParams }}
                      onInspectIgnoreList={onInspectIgnoreList}
                      task={updateIngoreStrategies}
                      onHandleCheckedChange={(checked: boolean) => {
                        updateStrategyList(record.strategyId, checked);
                      }}
                    />
                  ),
                  width: '10%',
                },
                {
                  key: 'StrategyId',
                  header: t('操作'),
                  render: (item: any) => (<Button
                    className="settings-table-rule-button"
                    type="link"
                    onClick={() => {
                      setCurrentStrategyId(item.strategyId);
                      setShowConditionEditor(true);
                      if (item.IsSupportCustom) {
                        setRuleModalMode(RuleMode.EDIT);
                      } else {
                        setRuleModalMode(RuleMode.VIEW);
                      }
                    }}
                  >
                    {
                      item.IsSupportCustom
                        ? t('调整规则')
                        : t('查看规则')
                    }
                  </Button>),
                  width: '10%',
                },
              ]}
              addons={[
                scrollable({ maxHeight: 600 }),
                autotip({
                  isLoading: loading,
                  isError: error,
                }),
                pageable({
                  pageIndex,
                  pageSizeOptions: [20, 50, 100, 200],
                  onPagingChange: (pagingQuery) => {
                    setPageIndex(pagingQuery.pageIndex);
                  },
                }),
                filterable({
                  type: 'multiple',
                  column: 'type',
                  value: types,
                  onChange: (value) => {
                    setTypes(value.filter((x) => x !== 'all'));
                    setPageIndex(1);
                  },
                  all: {
                    value: 'all',
                    text: t('全部'),
                  },
                  options:
                    TYPE_OPTS.map((item) => ({
                      value: item,
                      text: item,
                    })) || [],
                }),
                filterable({
                  type: 'multiple',
                  column: 'product',
                  value: products,
                  searchable: true,
                  onChange: (value) => {
                    dispatch(changeInspectionsSettingsModalState({
                      products: value.filter((x) => x !== 'all'),
                    }));
                    setPageIndex(1);
                  },
                  all: {
                    value: 'all',
                    text: t('全部'),
                  },
                  options: PRODUCT_OPTS,
                }),
              ]}
            />
          </Card>
          <ConditionEditor
            apiParams={apiParams}
            StrategyId={currentStrategyId}
            Visible={showConditionEditor}
            mode={ruleModalMode}
            handleVisible={() => {
              setShowConditionEditor(false);
            }}
            StrategyItem={strategyList.find((i) => i.strategyId === currentStrategyId)}
          />
        </Content.Body>
      </Content>
    </section>
  );
}
