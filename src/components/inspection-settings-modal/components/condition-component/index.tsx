import React, { useState, useEffect, useMemo } from 'react';
import { t } from '@tea/app/i18n';
import { Text } from '@tencent/tea-component/lib/text';
import { H5 } from '@tencent/tea-component';
import { ConditionItem, PolicyItem } from '@src/service/inspection-settings-modal/index.type';
import _, { isEmpty, find } from 'lodash';
import PolicyComponent from '../policy-component/index';

interface Prop {
  conditionItem: ConditionItem,
  index: number,
  handleUpdateCondition: (index: number, newCondition: ConditionItem) => void,
  key: string,
  isEdit?: boolean,
  deflautConditions: any,
}

const LOGICMAP = new Map([
  ['or', 'OR'],
  ['and', 'AND'],
]);

export default function ConditionComponent({
  conditionItem, index, handleUpdateCondition, key, isEdit, deflautConditions,
}: Prop) {
  const RISK_LEVEL = new Map([
    ['3', t('高风险')],
    ['2', t('中风险')],
  ]);
  // 条件连接符
  const [logicalOperator] = useState<string>(conditionItem.LogicalOperators);
  // policys
  const [policys, setPolicys] = useState<Array<PolicyItem>>(conditionItem.Policy);

  const newCondition = _.cloneDeep(conditionItem);

  // 定义policys更新函数，用于policy子组件进行回调
  function handleUpdatePolicy(index: number, newPolicyItem: PolicyItem) {
    const newPolicys = _.cloneDeep(policys);
    newPolicys[index] = newPolicyItem;
    setPolicys(newPolicys);
  }

  const policyDesc = useMemo(() => {
    const policy = find(deflautConditions, (item) => (
      item.ConditionId === conditionItem.ConditionId
      && item.Level === conditionItem.RiskLevel
    )) || {};
    return isEmpty(policy) ? '' : policy.Desc;
  }, [deflautConditions, conditionItem]);

  // 监听policys 和 logicalOperator 有变化及时回调父组件函数，刷新Condition
  useEffect(() => {
    newCondition.Policy = policys;
    newCondition.LogicalOperators = logicalOperator;
    handleUpdateCondition(index, newCondition);
  }, [policys, logicalOperator]);

  return (
    <div key={key}>
      <H5>
        {RISK_LEVEL.get(conditionItem.RiskLevel.toString())}
        {t('触发条件')}
        :
      </H5>
      <div style={{ marginLeft: 10 }}>
        {
          !isEmpty(policys)
            ? policys.map((p, i) => <div key={p.MetricName}>
              <div style={{ marginLeft: 10 }}>
                <PolicyComponent policyItem={p} index={i} handleUpdatePolicy={handleUpdatePolicy as any} isEdit={isEdit} />
              </div>
              {/* 如果策略数量大于1个，则显示条件连接符修改 */
              i + 1 < policys.length
              && <>
                <div>
                  <Text theme="strong">{LOGICMAP.get(logicalOperator)}</Text>
                </div>
              </>
            }
            </div>)
            : <div
                style={
                {
                  marginLeft: '-10px',
                  marginTop: '10px',
                }
              }
            >
              {policyDesc}
            </div>
        }
      </div>
      <br />
    </div>
  );
}
