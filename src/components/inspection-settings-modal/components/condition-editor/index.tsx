import React, { useState, useEffect } from 'react';
import { t } from '@tea/app/i18n';
import { Icon } from '@tencent/tea-component/lib/icon';
import { Text } from '@tencent/tea-component/lib/text';
import {
  modifyCustomThresholdConditionHandle,
  describeCustomThresholdConditionHandle,
} from '@src/service/inspection-settings-modal/final-request';
import {
  Button, Card, message, Modal, Alert,
} from '@tencent/tea-component';
import { Strategy, ConditionItem } from '@src/service/inspection-settings-modal/index.type';
import _ from 'lodash';
import { app } from '@tea/app';
import { useGlobalSelector } from '@src/store/global/index';
import ConditionComponent from '../condition-component/index';

export const getPopupContainer = (): HTMLElement => document.querySelector(
  '#micro-frontend-root div[data-qiankun="isa-cloud-arch"]',
) || document.querySelector('#tea-overlay-root');

interface Prop {
  StrategyId: number,
  Visible: boolean,
  handleVisible: (flag?: boolean) => void,
  StrategyItem?: Strategy,
  strategyName?: string,
  strategyDescribe?: string,
  // 新增接口参数
  apiParams?: any,
  mode?: string;
}

export default function ConditionEditor({
  StrategyId,
  Visible,
  handleVisible,
  StrategyItem,
  strategyName,
  strategyDescribe,
  apiParams = null,
  mode,
}: Prop) {
  const isEdit = mode === 'edit';
  const { pluginPropsData } = useGlobalSelector();
  // 策略清单
  const [conditions, setConditions] = useState<Array<ConditionItem>>([]);
  // 获取评估项自定义阈值策略loading
  const [loading, setLoading] = useState<boolean>(false);

  // new 策略清单
  const [newConditions, setNewConditions] = useState<Array<ConditionItem>>([]);

  // 获取评估项的自定义阈值策略
  const getThresholdCondition = async () => {
    setLoading(true);
    try {
      let params: any = { StrategyId };
      if (apiParams) {
        params = { ...params, ...apiParams };
      }
      const res = await describeCustomThresholdConditionHandle({
        env: pluginPropsData.env,
        ...params,
      });
      if (!res) {
        return;
      }
      setConditions(res.Condition || []);
      setNewConditions(res.Condition || []);
      setLoading(false);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
      setLoading(false);
    }
  };
  const [submitLoading, setSubmitLoading] = useState(false);
  // 更新评估项的自定义阈值策略
  const updateThresholdCondition = async () => {
    setSubmitLoading(true);
    const submitConditions = _.cloneDeep(newConditions);
    submitConditions.forEach((item: any) => {
      const newPolicy = item.Policy.map((el) => {
        const obj = {};
        Object.keys(el)?.forEach((val) => {
          if (val !== 'PercentList') {
            obj[val] = el[val];
          }
        });
        return obj;
      });
      // eslint-disable-next-line no-param-reassign
      item.Policy = newPolicy as any;
    });
    try {
      let params: any = { Condition: submitConditions };
      if (apiParams) {
        params = { ...params, ...apiParams };
      }
      const res = await modifyCustomThresholdConditionHandle({
        env: pluginPropsData.env,
        ...params,
      });
      if (!res) {
        return;
      }
      handleVisible(true); // 关闭页面
      if (pluginPropsData.env === 'ISA') {
        app.tips.success(t('保存成功!'));
      } else {
        message.success({
          content: t('保存成功!'),
        });
      }
      // message.success({ content: t('保存成功!') });
      setSubmitLoading(false);
    } catch (err) {
      setSubmitLoading(false);
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  };

  // 定义condition更新函数，用于conditioncomponents子组件调用
  function handleUpdateCondition(index: number, newCondition: ConditionItem) {
    const tmp = _.cloneDeep(newConditions);
    tmp[index] = newCondition;
    setNewConditions(tmp);
  }

  // 页面初始化
  useEffect(() => {
    if (!StrategyId) {
      return;
    }
    if (Visible) {
      getThresholdCondition();
    }
  }, [Visible]);

  return (
    <>
      <Modal
        className="inspect-sdk-condition-modal"
        visible={Visible}
        onClose={() => {
          handleVisible();
        }}
        caption={isEdit ? t('调整规则') : t('查看规则')}
        size="l"
      >
        <Modal.Body>
          {
            StrategyItem?.IsSupportCustom && <Alert>{t('您可以根据实际需要，调整评估项风险触发的阈值条件，您的设置将在下一次巡检时生效')}</Alert>
          }
          <Card>
            <Card.Body title={StrategyItem ? StrategyItem.strategyName : strategyName}>
              <div style={{ overflow: 'hidden' }}>
                <div>
                  <Text>{StrategyItem ? StrategyItem.strategyDescribe : strategyDescribe}</Text>
                </div>
                <br />
                {
                  loading ? <Icon type="loading" />
                    : <>
                      {
                      conditions.map((item, index) => <ConditionComponent
                        key={item.ConditionId.toString()}
                        isEdit={isEdit}
                        conditionItem={item}
                        index={index}
                        handleUpdateCondition={handleUpdateCondition as any}
                        deflautConditions={StrategyItem?.Conditions}
                      />)
                    }
                    </>
                }
              </div>
            </Card.Body>
          </Card>
        </Modal.Body>
        {
          StrategyItem?.IsSupportCustom && <Modal.Footer>
            <Button
              loading={submitLoading}
              onClick={() => {
                updateThresholdCondition();
              }}
              type="primary"
            >
              {t('保存')}
            </Button>
            <Button
              onClick={() => {
                handleVisible();
              }}
              type="weak"
            >
              {t('取消')}
            </Button>
          </Modal.Footer>
        }
      </Modal>
    </>
  );
}
