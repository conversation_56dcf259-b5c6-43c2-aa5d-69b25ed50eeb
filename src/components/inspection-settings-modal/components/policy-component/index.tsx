import React, { useState, useEffect } from 'react';
import { t } from '@tea/app/i18n';
import { Text } from '@tencent/tea-component/lib/text';
import { InputAdornment, InputNumber, Select } from '@tencent/tea-component';
import { PolicyItem } from '@src/service/inspection-settings-modal/index.type';
import _ from 'lodash';
import s from './index.scss';

interface Prop {
  policyItem: PolicyItem,
  index: number,
  handleUpdatePolicy: (index: number, currentPolicyItem: PolicyItem) => void,
  isEdit?: boolean,
}

const FactorMap = new Map([
  ['>', '大于'],
  ['<', '小于'],
]);

export default function PolicyComponent({
 policyItem, index, handleUpdatePolicy, isEdit,
}: Prop) {
  // 定义分位置 、 分位置阈值 、 持续时间
  const [maxPercent, setMaxPercent] = useState<number>(policyItem.MaxPercent);
  const [value, setValue] = useState<number>(policyItem.Value);
  const [days, setDays] = useState<number>(policyItem.Days);

  // clone policyItem
  const currentPolicyItem = _.cloneDeep(policyItem);

  // 数值修改后，传递到父组件
  useEffect(() => {
    currentPolicyItem.Days = days;
    currentPolicyItem.Value = value;
    currentPolicyItem.MaxPercent = maxPercent;
    handleUpdatePolicy(index, currentPolicyItem);
  }, [days, value, maxPercent]);

  return (
    <div key={policyItem.MetricName}>
      <Text>
        {t('连续')}
        &nbsp;
      </Text>
      <InputAdornment after={t('天')} appearance="pure">
        <Select
          options={['1', '2', '3', '4', '5', '6', '7'].map((value) => ({ value }))}
          disabled={!isEdit}
          value={days.toString()}
          appearance="button"
          onChange={(v) => {
            setDays(parseInt(v, 10));
          }}
        />
      </InputAdornment>
      &nbsp;&nbsp;
      <Text>
        {policyItem.MetricNameAlias}
          &nbsp;
        {t('的')}
      </Text>
      &nbsp;&nbsp;
      <Select
        options={policyItem?.PercentList?.map((value) => ({ value: String(value) }))}
        disabled={!isEdit}
        value={maxPercent.toString()}
        appearance="button"
        onChange={(v) => {
          setMaxPercent(parseInt(v, 10));
        }}
      />
      &nbsp;&nbsp;
      <Text>{t('分位值')}</Text>
      &nbsp;&nbsp;
      <Text>{FactorMap.get(policyItem.Factor)}</Text>
      &nbsp;&nbsp;
      <InputAdornment after={policyItem.Unit} appearance="pure">
        <InputNumber
          className={s['input-number']}
          disabled={!isEdit}
          value={value}
          min={0}
          max={policyItem.Unit === '%' ? 100 : Infinity}
          step={1}
          precision={2}
          formatter={(value: any) => {
            if (!isNaN(+value)) {
              return String(+(value));
            }
            return value;
          }}
          onChange={(next) => {
            setValue(next);
          }}
        />
      </InputAdornment>
    </div>
  );
}
