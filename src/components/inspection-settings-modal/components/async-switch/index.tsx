import React, { useState, useRef, useEffect } from 'react';
import { Switch } from '@tencent/tea-component/lib/switch';
import { t } from '@tea/app/i18n';
import { app } from '@tea/app';
import { Bubble } from '@tencent/tea-component/lib/bubble';

export interface Props {
  isAssessing?: boolean
  data?: any
  task?: any
  onHandleCheckedChange?: any
  onInspectIgnoreList?: any
}

export default function AsyncSwitch({
  isAssessing, data, task, onHandleCheckedChange, onInspectIgnoreList,
}: Props) {
  const [checked, loading, change] = useAsyncCheck();

  // 这里用 React Hooks 实现，使用 class 的同学可以自行改造
  /**
     * @returns {[boolean, boolean, (checked: boolean) => {}]}
     */
  function useAsyncCheck() {
    // 选中和 loading 态
    const [checked, setChecked] = useState(isAssessing);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
      setChecked(isAssessing);
    }, [isAssessing]);

    // 当前执行中的任务
    const taskRef = useRef(null);

    // 组件 unmount 的时候清理当前任务
    useEffect(
      () => () => {
        if (taskRef?.current) {
          taskRef.current.cancel();
          taskRef.current = null;
        }
      },
      [],
    );

    // 处理切换逻辑
    const change = async (newChecked) => {
      // 先切目标状态，给用户操作反馈
      setChecked(newChecked);
      setLoading(true);

      // 创建切换异步任务
      const task = asyncTask();
      // 任务挂载起来，可以取消之
      taskRef.current = task;

      // 执行异步任务
      try {
        const params = {};
        for (const key in data) {
          if (key !== 'StrategyName') {
            params[key] = data[key];
          }
        }
        await task.run({ Operate: checked ? 'add' : 'delete', ...params });
        app.tips.success(t('操作成功'));
        onHandleCheckedChange(newChecked);
      } catch (err) {
        // 失败了恢复到原来的勾选状态
        setChecked(checked);
        app.tips.error(t('操作失败'));
      } finally {
        // 无论成功失败，都退出 loading 态，并清理任务引用
        taskRef.current = null;
        setLoading(false);
      }
    };

    return [checked, loading, change];
  }

  function asyncTask() {
    return {
      cancel: () => { },
      run: task,

    };
  }
  let disabled = false;
  onInspectIgnoreList?.forEach((item) => {
    if (item.StrategyId === data.StrategyId && item.IgnoreType === 1) {
      disabled = true;
    }
  });
  return (
    <Bubble content={disabled && <div>
      该巡检项已经被关闭，无法在架构巡检中单独开启， 如需开启请访问：
      <a
        href={`advisor/switch?strategyName=${encodeURIComponent(data.StrategyName)}`}
        target="_blank"
        rel="noreferrer"
      >
        评估设置
      </a>
    </div>}
    >
      <Switch
            // @ts-ignore
        value={checked}
            // @ts-ignore
        loading={loading}
            // @ts-ignore
        disabled={loading || disabled}
            // @ts-ignore
        onChange={(value) => change(value)}
      />
    </Bubble>
  );
}
