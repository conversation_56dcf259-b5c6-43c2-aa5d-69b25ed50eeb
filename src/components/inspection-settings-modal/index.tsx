import React from 'react';
import { Modal } from '@tencent/tea-component';
import { useDispatch } from 'react-redux';
import { useGlobalSelector } from '@src/store/global/index';
import {
  useInspectionsSettingsModalStateSelector,
  changeInspectionsSettingsModalState,
} from '@src/store/inspection-settings-modal/index';
import SettingsTable from './components/settings-table';
/**
 * 巡检设置弹窗
 * @returns
 */
export default function InspectionSettingsModal(): React.ReactElement {
  const { pluginPropsData } = useGlobalSelector();
  const { inspectionSettingsModalVisible } = useInspectionsSettingsModalStateSelector();
  const dispatch = useDispatch();
  return (
    <Modal
      size="70%"
      disableEscape
      visible={inspectionSettingsModalVisible}
      caption="巡检设置"
      onClose={() => {
        dispatch(changeInspectionsSettingsModalState({
          inspectionSettingsModalVisible: false,
        }));
      }}
    >
      <Modal.Body>
        <SettingsTable currentMapId={pluginPropsData.archInfo.archId} />
      </Modal.Body>
    </Modal>
  );
}
