import React, { createContext, useContext, useReducer } from 'react';

export enum ActionEnum {
  SET_CHAT_BI_CALLBACKS = 'SET_CHAT_BI_CALLBACKS',
}

export interface IAppContext {
  chatBiCallBacks: any,
  dispatch?: React.Dispatch<IContextAction>;
}

export interface IContextAction {
  type: ActionEnum;
  value?: any;
  [key: string]: any;
}

interface AppProviderProps {
  children: React.ReactNode;
}

const initState = { chatBiCallBacks: undefined };

export const AppContext = createContext<IAppContext>(initState);

export function useAppContext(): IAppContext {
  const context = useContext(AppContext);
  return context;
}

const reducer = (state: IAppContext, action: IContextAction): IAppContext => {
  switch (action.type) {
    case ActionEnum.SET_CHAT_BI_CALLBACKS:
      return { ...state, chatBiCallBacks: action.value };
    default:
      return state;
  }
};

/**
 * @param props
 * @returns
 */
export default function AppProvider(
  props: AppProviderProps,
): React.ReactElement {
  const { children } = props;
  const [state, dispatch] = useReducer(reducer, initState);

  return (
    <AppContext.Provider value={{ ...state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}
