:root,
:root[theme-mode='light'] {
  --td-brand-color-1: #d5e7ff;
  --td-brand-color-2: #aacfff;
  --td-brand-color-3: #7fb7ff;
  --td-brand-color-4: #559eff;
  --td-brand-color-5: #2a86ff;
  --td-brand-color-6: #006eff;
  --td-brand-color-7: #0058df;
  --td-brand-color-8: #0042bf;
  --td-brand-color-9: #002da0;
  --td-brand-color-10: #001780;
  --td-warning-color-1: #ffe8d5;
  --td-warning-color-2: #ffd0aa;
  --td-warning-color-3: #ffb97f;
  --td-warning-color-4: #ffa155;
  --td-warning-color-5: #ff8a2a;
  --td-warning-color-6: #ff7200;
  --td-warning-color-7: #df5900;
  --td-warning-color-8: #bf4000;
  --td-warning-color-9: #a02800;
  --td-warning-color-10: #800f00;
  --td-error-color-1: #fbe0e0;
  --td-error-color-2: #f6c1c1;
  --td-error-color-3: #f2a2a2;
  --td-error-color-4: #ee8383;
  --td-error-color-5: #e96464;
  --td-error-color-6: #e54545;
  --td-error-color-7: #cc3842;
  --td-error-color-8: #b42b3f;
  --td-error-color-9: #9c1f3b;
  --td-error-color-10: #831238;
  --td-success-color-1: #d6f4e4;
  --td-success-color-2: #adeac8;
  --td-success-color-3: #84dfad;
  --td-success-color-4: #5cd492;
  --td-success-color-5: #33ca76;
  --td-success-color-6: #0abf5b;
  --td-success-color-7: #09a755;
  --td-success-color-8: #078f50;
  --td-success-color-9: #06774a;
  --td-success-color-10: #055f44;
  --td-gray-color-1: #f3f4f7;
  --td-gray-color-2: #e7eaef;
  --td-gray-color-3: #cfd5de;
  --td-gray-color-4: #b7c1ce;
  --td-gray-color-5: #9eacbe;
  --td-gray-color-6: #8697ad;
  --td-gray-color-7: #6e829d;
  --td-gray-color-8: #60708a;
  --td-gray-color-9: #525e76;
  --td-gray-color-10: #444c63;
  --td-gray-color-11: #363a50;
  --td-gray-color-12: #292c3d;
  --td-gray-color-13: #1e222d;
  --td-gray-color-14: #151822;
  --td-font-white-1: #fff;
  --td-font-white-2: rgba(255, 255, 255, .55);
  --td-font-white-3: rgba(255, 255, 255, .35);
  --td-font-white-4: rgba(255, 255, 255, .22);
  --td-font-gray-1: rgba(0, 0, 0, .9);
  --td-font-gray-2: rgba(0, 0, 0, .6);
  --td-font-gray-3: rgba(0, 0, 0, .4);
  --td-font-gray-4: rgba(0, 0, 0, .25);
  --td-brand-color: var(--td-brand-color-6);
  --td-warning-color: var(--td-warning-color-6);
  --td-error-color: var(--td-error-color-6);
  --td-success-color: var(--td-success-color-6);
  --td-brand-color-hover: var(--td-brand-color-5);
  --td-brand-color-focus: var(--td-brand-color-2);
  --td-brand-color-active: var(--td-brand-color-7);
  --td-brand-color-disabled: var(--td-brand-color-2);
  --td-brand-color-light: var(--td-brand-color-1);
  --td-warning-color-hover: var(--td-warning-color-5);
  --td-warning-color-focus: var(--td-warning-color-2);
  --td-warning-color-active: var(--td-warning-color-7);
  --td-warning-color-disabled: var(--td-warning-color-2);
  --td-warning-color-light: var(--td-warning-color-1);
  --td-error-color-hover: var(--td-error-color-5);
  --td-error-color-focus: var(--td-error-color-2);
  --td-error-color-active: var(--td-error-color-7);
  --td-error-color-disabled: var(--td-error-color-2);
  --td-error-color-light: var(--td-error-color-1);
  --td-success-color-hover: var(--td-success-color-5);
  --td-success-color-focus: var(--td-success-color-2);
  --td-success-color-active: var(--td-success-color-7);
  --td-success-color-disabled: var(--td-success-color-2);
  --td-success-color-light: var(--td-success-color-1);
  --td-mask-active: rgba(0, 0, 0, .8);
  --td-mask-disabled: rgba(255, 255, 255, .6);
  --td-bg-color-page: #ebeef2;
  --td-bg-color-container: #fff;
  --td-bg-color-container-hover: var(--td-gray-color-2);
  --td-bg-color-container-active: var(--td-gray-color-3);
  --td-bg-color-container-select: #fff;
  --td-bg-color-secondarycontainer: var(--td-gray-color-1);
  --td-bg-color-secondarycontainer-hover: var(--td-gray-color-2);
  --td-bg-color-secondarycontainer-active: var(--td-gray-color-3);
  --td-bg-color-component: var(--td-gray-color-2);
  --td-bg-color-component-hover: var(--td-gray-color-3);
  --td-bg-color-component-active: var(--td-gray-color-4);
  --td-bg-color-component-disabled: var(--td-gray-color-2);
  --td-bg-color-secondarycomponent: var(--td-gray-color-3);
  --td-bg-color-secondarycomponent-hover: var(--td-gray-color-4);
  --td-bg-color-secondarycomponent-active: var(--td-gray-color-5);
  --td-bg-color-specialcomponent: #fff;
  --td-text-color-primary: var(--td-font-gray-1);
  --td-text-color-secondary: var(--td-font-gray-2);
  --td-text-color-placeholder: var(--td-font-gray-3);
  --td-text-color-disabled: var(--td-font-gray-4);
  --td-text-color-anti: #fff;
  --td-text-color-brand: var(--td-brand-color-6);
  --td-text-color-link: var(--td-brand-color-6);
  --td-border-level-1-color: var(--td-gray-color-2);
  --td-component-stroke: var(--td-gray-color-2);
  --td-border-level-2-color: var(--td-gray-color-3);
  --td-component-border: var(--td-gray-color-3);
  --td-shadow-1: 0 2px 3px rgba(0, 0, 0, .2);
  --td-shadow-2: 0 2px 4px 0 rgba(54, 58, 80, .32);
  --td-shadow-3: 0 0 20px 0 rgba(19, 41, 75, .2);
  --td-shadow-inset-top: inset 0 .5px 0 #e7eaef;
  --td-shadow-inset-right: inset .5px 0 0 #e7eaef;
  --td-shadow-inset-bottom: inset 0 -.5px 0 #e7eaef;
  --td-shadow-inset-left: inset -.5px 0 0 #e7eaef;
  --td-table-shadow-color: rgba(0, 0, 0, .08);
  --td-scrollbar-color: rgba(0, 0, 0, .1);
  --td-scrollbar-hover-color: rgba(0, 0, 0, .3);
  --td-scroll-track-color: #fff;
  --td-radius-small: 0px;
  --td-radius-default: 0px;
  --td-radius-medium: 0px;
  --td-radius-large: 0px;
  --td-radius-extraLarge: 12px;
  --td-radius-round: 999px;
  --td-radius-circle: 50%;
  --td-font-family: PingFang SC, Microsoft YaHei, Arial Regular;
  --td-font-family-medium: PingFang SC, Microsoft YaHei, Arial Medium;
  --td-font-size-link-small: 12px;
  --td-font-size-link-medium: 12px;
  --td-font-size-link-large: 14px;
  --td-font-size-mark-small: 12px;
  --td-font-size-mark-medium: 12px;
  --td-font-size-body-small: 12px;
  --td-font-size-body-medium: 12px;
  --td-font-size-body-large: 14px;
  --td-font-size-title-small: 12px;
  --td-font-size-title-medium: 14px;
  --td-font-size-title-large: 16px;
  --td-font-size-headline-small: 24px;
  --td-font-size-headline-medium: 28px;
  --td-font-size-headline-large: 36px;
  --td-font-size-display-medium: 48px;
  --td-font-size-display-large: 64px;
  --td-line-height-link-small: 20px;
  --td-line-height-link-medium: 20px;
  --td-line-height-link-large: 22px;
  --td-line-height-mark-small: 20px;
  --td-line-height-mark-medium: 22px;
  --td-line-height-body-small: 20px;
  --td-line-height-body-medium: 20px;
  --td-line-height-body-large: 22px;
  --td-line-height-title-small: 20px;
  --td-line-height-title-medium: 22px;
  --td-line-height-title-large: 24px;
  --td-line-height-headline-small: 32px;
  --td-line-height-headline-medium: 36px;
  --td-line-height-headline-large: 44px;
  --td-line-height-display-medium: 56px;
  --td-line-height-display-large: 72px;
  --td-font-link-small: 400 var(--td-font-size-link-small) / var(--td-line-height-link-small) var(--td-font-family);
  --td-font-link-medium: 400 var(--td-font-size-link-medium) / var(--td-line-height-link-medium) var(--td-font-family);
  --td-font-link-large: 400 var(--td-font-size-link-large) / var(--td-line-height-link-large) var(--td-font-family);
  --td-font-mark-small: 600 var(--td-font-size-mark-small) / var(--td-line-height-mark-small) var(--td-font-family);
  --td-font-mark-medium: 600 var(--td-font-size-mark-medium) / var(--td-line-height-mark-medium) var(--td-font-family);
  --td-font-body-small: 400 var(--td-font-size-body-small) / var(--td-line-height-body-small) var(--td-font-family);
  --td-font-body-medium: 400 var(--td-font-size-body-small) / var(--td-line-height-body-small) var(--td-font-family);
  --td-font-body-large: 400 var(--td-font-size-body-large) / var(--td-line-height-body-large) var(--td-font-family);
  --td-font-title-small: 600 var(--td-font-size-title-small) / var(--td-line-height-title-small) var(--td-font-family);
  --td-font-title-medium: 600 var(--td-font-size-title-medium) / var(--td-line-height-title-medium) var(--td-font-family);
  --td-font-title-large: 600 var(--td-font-size-title-large) / var(--td-line-height-title-large) var(--td-font-family);
  --td-font-headline-small: 600 var(--td-font-size-headline-small) / var(--td-line-height-headline-small) var(--td-font-family);
  --td-font-headline-medium: 600 var(--td-font-size-headline-medium) / var(--td-line-height-headline-medium) var(--td-font-family);
  --td-font-headline-large: 600 var(--td-font-size-headline-large) / var(--td-line-height-headline-large) var(--td-font-family);
  --td-font-display-medium: 600 var(--td-font-size-display-medium) / var(--td-line-height-display-medium) var(--td-font-family);
  --td-font-display-large: 600 var(--td-font-size-display-large) / var(--td-line-height-display-large) var(--td-font-family);
  --td-size-1: 2px;
  --td-size-2: 3px;
  --td-size-3: 5px;
  --td-size-4: 10px;
  --td-size-5: 12px;
  --td-size-6: 16px;
  --td-size-7: 20px;
  --td-size-8: 25px;
  --td-size-9: 28px;
  --td-size-10: 30px;
  --td-size-11: 35px;
  --td-size-12: 40px;
  --td-size-13: 48px;
  --td-size-14: 56px;
  --td-size-15: 64px;
  --td-size-16: 72px;
  --td-comp-size-xxxs: var(--td-size-6);
  --td-comp-size-xxs: var(--td-size-7);
  --td-comp-size-xs: var(--td-size-8);
  --td-comp-size-s: var(--td-size-9);
  --td-comp-size-m: var(--td-size-10);
  --td-comp-size-l: var(--td-size-11);
  --td-comp-size-xl: var(--td-size-12);
  --td-comp-size-xxl: var(--td-size-13);
  --td-comp-size-xxxl: var(--td-size-14);
  --td-comp-size-xxxxl: var(--td-size-15);
  --td-comp-size-xxxxxl: var(--td-size-16);
  --td-pop-padding-s: 0;
  --td-pop-padding-m: 0;
  --td-pop-padding-l: 0;
  --td-pop-padding-xl: var(--td-size-5);
  --td-pop-padding-xxl: var(--td-size-6);
  --td-comp-paddingLR-xxs: var(--td-size-1);
  --td-comp-paddingLR-xs: var(--td-size-3);
  --td-comp-paddingLR-s: var(--td-size-4);
  --td-comp-paddingLR-m: var(--td-size-5);
  --td-comp-paddingLR-l: var(--td-size-7);
  --td-comp-paddingLR-xl: var(--td-size-8);
  --td-comp-paddingLR-xxl: var(--td-size-8);
  --td-comp-paddingTB-xxs: var(--td-size-1);
  --td-comp-paddingTB-xs: var(--td-size-3);
  --td-comp-paddingTB-s: var(--td-size-4);
  --td-comp-paddingTB-m: var(--td-size-5);
  --td-comp-paddingTB-l: var(--td-size-7);
  --td-comp-paddingTB-xl: var(--td-size-8);
  --td-comp-paddingTB-xxl: var(--td-size-8);
  --td-comp-margin-xxs: var(--td-size-1);
  --td-comp-margin-xs: var(--td-size-2);
  --td-comp-margin-s: var(--td-size-4);
  --td-comp-margin-m: var(--td-size-5);
  --td-comp-margin-l: var(--td-size-6);
  --td-comp-margin-xl: var(--td-size-7);
  --td-comp-margin-xxl: var(--td-size-8);
  --td-comp-margin-xxxl: var(--td-size-10);
  --td-comp-margin-xxxxl: var(--td-size-12);
}

:root[theme-mode='dark'] {
  --td-brand-color-1: #001b92;
  --td-brand-color-2: #0935ad;
  --td-brand-color-3: #134fc8;
  --td-brand-color-4: #1c6ae4;
  --td-brand-color-5: #2684ff;
  --td-brand-color-6: #4a99ff;
  --td-brand-color-7: #6eadff;
  --td-brand-color-8: #92c2ff;
  --td-brand-color-9: #b7d6ff;
  --td-brand-color-10: #dbebff;
  --td-warning-color-1: #723b16;
  --td-warning-color-2: #8c581e;
  --td-warning-color-3: #a67426;
  --td-warning-color-4: #bf912e;
  --td-warning-color-5: #d9ae36;
  --td-warning-color-6: #dfbc57;
  --td-warning-color-7: #e6c979;
  --td-warning-color-8: #ecd79a;
  --td-warning-color-9: #f2e4bc;
  --td-warning-color-10: #f9f2de;
  --td-error-color-1: #5d1d34;
  --td-error-color-2: #742536;
  --td-error-color-3: #8c2c39;
  --td-error-color-4: #a3333b;
  --td-error-color-5: #bb3b3d;
  --td-error-color-6: #c65c5d;
  --td-error-color-7: #d27c7e;
  --td-error-color-8: #dd9d9e;
  --td-error-color-9: #e8bebe;
  --td-error-color-10: #f4dedf;
  --td-success-color-1: #064e3a;
  --td-success-color-2: #07623f;
  --td-success-color-3: #097545;
  --td-success-color-4: #0a894a;
  --td-success-color-5: #0c9d4f;
  --td-success-color-6: #34ad6c;
  --td-success-color-7: #5dbe8a;
  --td-success-color-8: #85cea7;
  --td-success-color-9: #aedec4;
  --td-success-color-10: #d7efe2;
  --td-gray-color-1: #f3f4f7;
  --td-gray-color-2: #e7eaef;
  --td-gray-color-3: #cfd5de;
  --td-gray-color-4: #b7c1ce;
  --td-gray-color-5: #9eacbe;
  --td-gray-color-6: #8697ad;
  --td-gray-color-7: #6e829d;
  --td-gray-color-8: #60708a;
  --td-gray-color-9: #425472;
  --td-gray-color-10: #344158;
  --td-gray-color-11: #262f3e;
  --td-gray-color-12: #1e222d;
  --td-gray-color-13: #151822;
  --td-gray-color-14: #090a0f;
  --td-font-white-1: rgba(255, 255, 255, .9);
  --td-font-white-2: rgba(255, 255, 255, .55);
  --td-font-white-3: rgba(255, 255, 255, .35);
  --td-font-white-4: rgba(255, 255, 255, .22);
  --td-font-gray-1: rgba(0, 0, 0, .9);
  --td-font-gray-2: rgba(0, 0, 0, .6);
  --td-font-gray-3: rgba(0, 0, 0, .4);
  --td-font-gray-4: rgba(0, 0, 0, .26);
  --td-brand-color: var(--td-brand-color-5);
  --td-warning-color: var(--td-warning-color-5);
  --td-error-color: var(--td-error-color-6);
  --td-success-color: var(--td-success-color-6);
  --td-brand-color-hover: var(--td-brand-color-4);
  --td-brand-color-focus: var(--td-brand-color-2);
  --td-brand-color-active: var(--td-brand-color-6);
  --td-brand-color-disabled: var(--td-brand-color-3);
  --td-brand-color-light: var(--td-brand-color-1);
  --td-brand-color-light-hover: var(--td-brand-color-2);
  --td-warning-color-hover: var(--td-warning-color-4);
  --td-warning-color-focus: var(--td-warning-color-2);
  --td-warning-color-active: var(--td-warning-color-6);
  --td-warning-color-disabled: var(--td-warning-color-3);
  --td-warning-color-light: var(--td-warning-color-1);
  --td-warning-color-light-hover: var(--td-warning-color-2);
  --td-error-color-hover: var(--td-error-color-5);
  --td-error-color-focus: var(--td-error-color-2);
  --td-error-color-active: var(--td-error-color-7);
  --td-error-color-disabled: var(--td-error-color-3);
  --td-error-color-light: var(--td-error-color-1);
  --td-error-color-light-hover: var(--td-error-color-2);
  --td-success-color-hover: var(--td-success-color-5);
  --td-success-color-focus: var(--td-success-color-2);
  --td-success-color-active: var(--td-success-color-7);
  --td-success-color-disabled: var(--td-success-color-3);
  --td-success-color-light: var(--td-success-color-1);
  --td-success-color-light-hover: var(--td-success-color-2);
  --td-mask-active: rgba(0, 0, 0, .4);
  --td-mask-disabled: rgba(0, 0, 0, .6);
  --td-bg-color-page: var(--td-gray-color-14);
  --td-bg-color-container: var(--td-gray-color-13);
  --td-bg-color-container-hover: var(--td-gray-color-12);
  --td-bg-color-container-active: var(--td-gray-color-10);
  --td-bg-color-container-select: var(--td-gray-color-9);
  --td-bg-color-secondarycontainer: var(--td-gray-color-12);
  --td-bg-color-secondarycontainer-hover: var(--td-gray-color-11);
  --td-bg-color-secondarycontainer-active: var(--td-gray-color-9);
  --td-bg-color-component: var(--td-gray-color-11);
  --td-bg-color-component-hover: var(--td-gray-color-10);
  --td-bg-color-component-active: var(--td-gray-color-9);
  --td-bg-color-secondarycomponent: var(--td-gray-color-10);
  --td-bg-color-secondarycomponent-hover: var(--td-gray-color-9);
  --td-bg-color-secondarycomponent-active: var(--td-gray-color-8);
  --td-bg-color-component-disabled: var(--td-gray-color-12);
  --td-bg-color-specialcomponent: transparent;
  --td-text-color-primary: var(--td-font-white-1);
  --td-text-color-secondary: var(--td-font-white-2);
  --td-text-color-placeholder: var(--td-font-white-3);
  --td-text-color-disabled: var(--td-font-white-4);
  --td-text-color-anti: #fff;
  --td-text-color-brand: var(--td-brand-color-5);
  --td-text-color-link: var(--td-brand-color-5);
  --td-border-level-1-color: var(--td-gray-color-11);
  --td-component-stroke: var(--td-gray-color-11);
  --td-border-level-2-color: var(--td-gray-color-10);
  --td-component-border: var(--td-gray-color-10);
  --td-shadow-1: 0 4px 6px rgba(0, 0, 0, .06), 0 1px 10px rgba(0, 0, 0, 8%),    0 2px 4px rgba(0, 0, 0, 12%);
  --td-shadow-2: 0 8px 10px rgba(0, 0, 0, .12), 0 3px 14px rgba(0, 0, 0, 10%),    0 5px 5px rgba(0, 0, 0, 16%);
  --td-shadow-3: 0 16px 24px rgba(0, 0, 0, .14), 0 6px 30px rgba(0, 0, 0, 12%),    0 8px 10px rgba(0, 0, 0, 20%);
  --td-shadow-inset-top: inset 0 .5px 0 #344158;
  --td-shadow-inset-right: inset .5px 0 0 #344158;
  --td-shadow-inset-bottom: inset 0 -.5px 0 #344158;
  --td-shadow-inset-left: inset -.5px 0 0 #344158;
  --td-table-shadow-color: rgba(0, 0, 0, .55);
  --td-scrollbar-color: rgba(255, 255, 255, .1);
  --td-scrollbar-hover-color: rgba(255, 255, 255, .3);
  --td-scroll-track-color: #333;
}

.t-button .t-icon {
  font-size: var(--td-comp-size-xxxs);
}

.t-button .t-loading {
  font-size: var(--td-comp-size-xxxs);
}

.t-button .t-icon+.t-button__text:not(:empty) {
  margin-left: var(--td-comp-margin-xs);
}

.t-button .t-loading+.t-button__text:not(:empty) {
  margin-left: var(--td-comp-margin-xs);
}

.t-button .t-button__suffix:not(:empty) {
  display: inline-flex;
  margin-left: var(--td-comp-margin-xs);
}

.t-button--variant-dashed .t-icon,
.t-button--variant-dashed .t-loading {
  font-size: var(--td-comp-size-xxxs);
}

.t-button--variant-outline .t-icon,
.t-button--variant-outline .t-loading {
  font-size: var(--td-comp-size-xxxs);
}

.t-button--variant-base .t-icon,
.t-button--variant-base .t-loading {
  font-size: var(--td-comp-size-xxxs);
}

.t-button--shape-circle .t-icon {
  font-size: var(--td-comp-size-xxxs);
}

.t-button--shape-circle .t-loading {
  font-size: var(--td-comp-size-xxxs);
}

.t-link .t-link__prefix-icon {
  margin-right: var(--td-comp-margin-xs);
}

.t-link .t-link__suffix-icon {
  margin-left: var(--td-comp-margin-xs);
}

.t-link.t-size-l .t-link__prefix-icon {
  margin-right: var(--td-comp-margin-xs);
}

.t-link.t-size-l .t-link__suffix-icon {
  margin-left: var(--td-comp-margin-xs);
}

.t-popup[data-popper-placement^='top'] .t-popup__arrow {
  bottom: calc(-8px / 2);
}

.t-tooltip .t-popup__arrow {
  width: auto;
}

.t-popup[data-popper-placement^='top'].t-tooltip .t-popup__arrow {
  bottom: var(--td-comp-margin-xs);
}

.t-popup[data-popper-placement^='left'].t-tooltip .t-popup__arrow {
  right: var(--td-comp-margin-xs);
}

.t-popup[data-popper-placement^='right'].t-tooltip .t-popup__arrow {
  left: calc(0px - var(--td-comp-margin-xs));
}

.t-dropdown__menu {
  gap: 0px;
}

.t-dropdown__menu .t-divider {
  color: var(--td-bg-color-container-hover);
}

.t-dropdown__submenu ul {
  gap: 0px;
}

.t-dropdown__item {
  padding: calc(var(--td-comp-paddingTB-xs) + 1px) var(--td-comp-paddingLR-s);
}

.t-dropdown__item--theme-default.t-dropdown__item--active {
  background-color: var(--td-bg-color-container-hover);
  color: var(--td-text-color-primary);
}

.t-dropdown__item--theme-default.t-dropdown__item--suffix:hover {
  background-color: var(--td-bg-color-container-hover);
  color: var(--td-text-color-primary);
}

.t-tabs__header .t-icon {
  font-size: var(--td-comp-size-xxxs);
}

.t-tabs__btn.t-size-m {
  height: var(--td-comp-size-m);
  line-height: var(--td-comp-size-m);
}

.t-tabs__btn.t-size-l {
  height: var(--td-comp-size-xl);
  line-height: var(--td-comp-size-xl);
}

.t-tabs__scroll-btn.t-size-m {
  height: var(--td-comp-size-m);
  line-height: var(--td-comp-size-m);
}

.t-tabs__scroll-btn.t-size-l {
  height: var(--td-comp-size-xl);
  line-height: var(--td-comp-size-xl);
}

.t-tabs__nav-item {
  font: var(--td-font-body-large);
}

.t-tabs__nav-item-wrapper {
  margin-right: 0;
  margin-left: 0;
}

.t-tabs__nav-item.t-size-m {
  height: var(--td-comp-size-m);
  line-height: var(--td-comp-size-m);
}

.t-tabs__nav-item.t-size-l {
  height: var(--td-comp-size-xl);
  line-height: var(--td-comp-size-xl);
}

.t-tabs__nav--card.t-tabs__nav-item {
  padding-right: var(--td-comp-paddingLR-s);
  padding-left: var(--td-comp-paddingLR-s);
}

.t-tabs__nav--card.t-tabs__nav-item.t-size-l {
  padding-right: var(--td-comp-paddingLR-l);
  padding-left: var(--td-comp-paddingLR-l);
}

.t-layout__header {
  height: var(--td-comp-size-xxl);
}

.t-head-menu {
  font: var(--td-font-body-small);
}

.t-head-menu .t-tabs {
  padding-top: var(--td-comp-margin-xs);
}

.t-head-menu .t-tabs .t-tabs__nav-item {
  font-size: var(--td-font-size-body-medium);
}

.t-head-menu__inner {
  height: var(--td-comp-size-xxl);
}

.t-head-menu__inner li+li {
  margin-left: 0;
}

.t-head-menu .t-menu__logo:not(:empty) {
  margin-right: var(--td-comp-margin-xxl);
}

.t-head-menu .t-menu__operations:not(:empty) {
  height: var(--td-comp-size-xxl);
  margin-right: var(--td-comp-margin-xl);
  line-height: 48px;
}

.t-head-menu .t-menu__operations-icon {
  width: var(--td-comp-size-xxl);
  height: var(--td-comp-size-xxl);
  line-height: var(--td-comp-size-xxl);
}

.t-head-menu .t-menu__item {
  height: var(--td-comp-size-xxl);
  line-height: var(--td-comp-size-xxl);
}

.t-head-menu .t-menu__popup {
  top: calc(var(--td-comp-size-xxl) + 12px);
}

.t-head-menu .t-menu__popup .t-menu__item.t-is-active {
  color: var(--td-text-color-anti);
}

.t-head-menu .t-menu__popup .t-menu__item.t-is-active .t-icon {
  color: var(--td-text-color-anti);
}

.t-head-menu .t-menu__popup li+li {
  margin-top: 0;
}

.t-default-menu {
  font: var(--td-font-body-large);
}

.t-default-menu.t-is-collapsed {
  width: 56px;
}

.t-default-menu.t-is-collapsed .t-menu__item.t-is-active.t-is-opened {
  background-color: var(--td-brand-color);
  color: var(--td-text-color-anti);
}

.t-default-menu.t-is-collapsed .t-menu__item.t-is-active.t-is-opened .t-icon {
  color: var(--td-text-color-anti);
}

.t-default-menu__inner .t-menu__logo:not(:empty) {
  height: var(--td-comp-size-xxl);
}

.t-default-menu__inner .t-menu {
  padding: 0;
  box-shadow: 1px 0px 0px var(--td-component-stroke);
  font: var(--td-font-body-large);
}

.t-default-menu .t-menu__operations:not(:empty) {
  box-shadow: 1px 0px 0px var(--td-component-stroke);
}

.t-default-menu .t-submenu {
  position: relative;
  margin-bottom: 0;
}

.t-default-menu .t-submenu.t-is-opened {
  background-color: var(--td-bg-color-secondarycontainer);
}

.t-default-menu .t-menu__operations:not(:empty) {
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-m);
}

.t-default-menu .t-menu__sub.t-is-opened {
  max-height: calc(100vh - var(--td-comp-size-xxl) * 2);
}

.t-default-menu .t-menu__item {
  height: var(--td-comp-size-xl);
  padding: 0 10px 0 var(--td-comp-paddingLR-l);
  margin: 0;
}

.t-default-menu .t-menu__item .t-icon {
  width: var(--td-comp-size-xxxs);
  height: var(--td-comp-size-xxxs);
}

.t-default-menu .t-menu__item.t-is-active:not(.t-is-opened) {
  background-color: var(--td-brand-color);
  color: var(--td-text-color-anti);
}

.t-default-menu .t-menu__item.t-is-active:not(.t-is-opened) .t-icon {
  color: var(--td-text-color-anti);
}

.t-default-menu.t-menu--dark .t-menu__logo:not(:empty) {
  border-bottom-color: var(--td-gray-color-11);
}

.t-default-menu.t-menu--dark .t-menu__operations:not(:empty) {
  border-top-color: var(--td-gray-color-11);
}

.t-default-menu.t-menu--dark .t-menu__item:hover:not(.t-is-active):not(.t-is-disabled) {
  background-color: var(--td-gray-color-11);
}

.t-default-menu.t-menu--dark .t-submenu.t-is-opened {
  background-color: var(--td-gray-color-14);
}

.t-default-menu.t-menu--dark.t-is-collapsed .t-menu-group__title:after {
  background-color: var(--td-gray-color-11);
}

.t-menu {
  font: none;
}

.t-menu__item.t-is-active {
  background-color: var(--td-brand-color);
  color: var(--td-text-color-anti);
}

.t-menu__item.t-is-opened {
  color: none;
}

.t-menu--dark.t-head-menu .t-menu__popup {
  border: solid .5px var(--td-gray-color-11);
}

.t-menu--dark .t-menu__item {
  --ripple-color: var(--td-gray-color-12);
}

.t-menu--dark .t-menu__item.t-is-active {
  background-color: var(--td-brand-color);
  color: var(--td-text-color-anti);
}

.t-menu--dark .t-menu__item:hover:not(.t-is-active):not(.t-is-opened):not(.t-is-disabled) {
  background-color: var(--td-gray-color-11);
}

.t-menu--dark .t-menu__popup .t-menu__item:hover:not(.t-is-active) {
  background-color: var(--td-gray-color-11);
}

.t-menu__popup-wrapper {
  padding: 0;
}

.t-menu__popup.t-is-horizontal li+li {
  margin-top: 0;
}

.t-menu__popup.t-is-opened {
  max-height: calc(100vh - var(--td-comp-size-xxl) * 2);
}

.t-menu__popup .t-menu__item {
  height: var(--td-comp-size-l);
  color: none;
  line-height: var(--td-comp-size-xl);
}

.t-menu__operations-icon:hover {
  background-color: var(--td-brand-color);
}

.t-menu__popup .t-menu__item,
.t-menu__popup .t-submenu {
  margin: 0;
}

.t-input.t-size-l {
  padding: 0 var(--td-comp-paddingLR-s);
}

.t-input.t-size-s {
  padding: 0 var(--td-comp-paddingLR-xs);
}

.t-input .t-input__prefix>.t-icon,
.t-input .t-input__suffix>.t-icon {
  font-size: 16px;
}

.t-input-number:not(.t-input-number--column)>.t-input-number__decrease+.t-input__wrap {
  margin-left: -1px;
}

.t-input-number:not(.t-input-number--column)>.t-input-number__decrease+.t-input__wrap :hover {
  z-index: 2;
}

.t-input-number:not(.t-input-number--column)>.t-input-number__decrease+.t-input__wrap .t-is-focused {
  z-index: 2;
}

.t-input-number:not(.t-input-number--column)>.t-input-number__decrease+.t-input__wrap .t-is-error {
  z-index: 2;
}

.t-input-number:not(.t-input-number--column)>.t-input-number__decrease+.t-input__wrap .t-is-warning {
  z-index: 2;
}

.t-input-number:not(.t-input-number--column)>.t-input-number__decrease+.t-input__wrap .t-is-success {
  z-index: 2;
}

.t-input-number.t-input-number--auto-width:not(.t-input-number--column) .t-input-number__increase {
  right: 0;
}

.t-input-number .t-input-number__decrease .t-icon,
.t-input-number .t-input-number__increase .t-icon {
  font-size: var(--td-comp-size-xxxs);
}

.t-input-number--row .t-input__wrap {
  margin-right: -1px;
}

.t-tag {
  height: var(--td-comp-size-xxs);
}

.t-tag-input .t-input {
  padding: var(--td-comp-paddingTB-xxs) var(--td-comp-paddingLR-s) var(--td-comp-paddingTB-xxs) var(--td-comp-paddingLR-xs);
}

.t-tag .t-icon {
  width: calc(var(--td-font-size-body-medium) + 4px);
  height: calc(var(--td-font-size-body-medium) + 4px);
}

ul.t-select-option-group__header,
.t-select-option-group__header {
  height: var(--td-comp-size-m);
  line-height: var(--td-comp-size-m);
}

.t-select-option-group__header.t-size-l {
  padding: 0 var(--td-comp-paddingLR-s);
}

.t-select-option-group__header.t-size-s {
  height: var(--td-comp-size-xs);
  padding: 0 var(--td-comp-paddingLR-xs);
  line-height: var(--td-comp-size-xs);
}

.t-select-option {
  height: var(--td-comp-size-m);
}

.t-select-option.t-size-l {
  padding: 0 var(--td-comp-paddingLR-s);
}

.t-select-option.t-size-s {
  height: var(--td-comp-size-xs);
  padding: 0 var(--td-comp-paddingLR-xs);
}

.t-select-option.t-is-selected {
  background-color: var(--td-bg-color-component);
  color: var(--td-text-color-primary);
}

.t-select-option.t-is-selected .t-checkbox__label {
  color: var(--td-text-color-primary);
}

.t-select-option.t-is-selected:hover {
  background-color: var(--td-bg-color-component);
}

.t-select-option+.t-select-option {
  margin-top: 0;
}

.t-pagination__pager li:not(:last-child) {
  margin-right: -1px;
}

.t-pagination__number {
  z-index: 2;
}

.t-pagination__number:hover {
  z-index: 3;
}

.t-pagination__number.t-is-current {
  z-index: 3;
}

.t-pagination__number--more {
  z-index: 1;
}

.t-pagination__jump {
  height: calc(var(--td-comp-size-m) - 2px);
  border: 1px solid var(--td-component-border);
}

.t-pagination .t-input-adornment__append {
  height: none;
}

.t-pagination .t-input-number .t-input {
  height: none;
}

.t-pagination.t-size-s .t-pagination__jump {
  height: calc(var(--td-comp-size-xs) - 2px);
}

.t-pagination.t-size-s .t-input-adornment__append {
  height: var(--td-comp-size-xs);
}

.t-pagination.t-size-s .t-input-number .t-input {
  height: var(--td-comp-size-xs);
}

.default-step-icon {
  font-size: var(--td-font-size-title-large);
}

.t-steps .t-steps-item--finish .t-steps-item__icon .t-icon {
  color: var(--td-text-color-anti);
}

.t-steps .t-steps-item--finish .t-steps-item__icon--number {
  background: var(--td-brand-color);
}

.t-steps .t-steps-item__icon {
  font-size: var(--td-comp-size-xxxs);
}

.t-steps .t-steps-item__icon--number {
  font-size: var(--td-font-size-title-large);
}

.t-steps .t-steps-item__icon--finish {
  font-size: var(--td-font-size-title-large);
}

.t-steps .t-steps-item__icon--error {
  font-size: var(--td-font-size-title-large);
}

.t-steps .t-steps-item__icon>.t-icon {
  font-size: calc(var(--td-comp-size-xxxs) + 10px);
}

.t-steps--horizontal.t-steps--default-anchor .t-steps-item__icon>.t-icon {
  font-size: calc(var(--td-comp-size-xxxs) + 10px);
}

.t-cascader__panel--empty {
  height: var(--td-comp-size-m);
  line-height: var(--td-comp-size-m);
}

.t-cascader__item {
  height: var(--td-comp-size-m);
}

.t-cascader__item.t-size-l {
  height: var(--td-comp-size-l);
}

.t-cascader__item-icon.t-loading {
  color: var(--td-text-color-primary);
}

.t-cascader__item.t-is-expanded {
  background: var(--td-bg-color-component);
  color: var(--td-text-color-primary);
}

.t-cascader__item.t-is-expanded .t-icon-chevron-right {
  color: var(--td-text-color-primary);
}

.t-cascader__item .t-checkbox .t-checkbox__label .t-cascader__item__label--filter {
  color: var(--td-text-color-primary);
}

.t-cascader__item-label--filter {
  color: var(--td-text-color-primary);
}

.t-cascader__item.t-is-selected {
  color: var(--td-text-color-primary);
}

.t-cascader__item.t-is-selected .t-checkbox__label {
  color: var(--td-text-color-primary);
}

.t-form__item {
  margin-bottom: var(--td-comp-margin-xl);
}

.t-form__item.t-form__item-with-extra {
  margin-bottom: var(--td-comp-margin-xl);
}

.t-radio-group .t-radio-button {
  transition: background-color .2s linear, color .2s linear,    border-color .2s linear;
}

.t-radio-group .t-radio-button:hover {
  background: var(--td-bg-color-container-hover);
}

.t-radio-group .t-radio-button.t-is-checked:hover {
  background: transparent;
}

.t-transfer__list-source,
.t-transfer__list-target {
  border: 1px solid var(--td-component-border);
}

.t-transfer__list-header {
  width: calc(200px - 0 * 2);
  padding: 0 var(--td-comp-paddingLR-m);
  margin: 0 0;
}

.t-transfer__list-header+ :not(.t-transfer__list--with-search) {
  border-top: 1px solid var(--td-component-border);
}

.t-transfer__list-content .t-checkbox-group {
  gap: 0;
}

.t-transfer__list-content .t-checkbox-group .t-checkbox {
  margin-right: 0;
}

.t-transfer__list-content .t-checkbox-group .t-checkbox+.t-checkbox {
  margin-left: 0;
}

.t-transfer__list-item {
  padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-m);
  margin: 0;
}

.t-transfer__list-item.t-is-checked {
  background: var(--td-bg-color-component);
}

.t-transfer__list-footer {
  border-top: 1px solid var(--td-component-border);
}

.t-transfer__search-wrapper {
  left: -1px;
  width: calc(100% + 2px);
  padding: 0 0;
}

.t-time-picker__panel-body {
  height: calc(calc(var(--td-comp-size-m) + var(--td-size-3)) * 7 + var(--td-size-3));
}

.t-time-picker__panel-body-active-mask {
  height: var(--td-comp-size-m);
}

.t-time-picker__panel-body-active-mask>div {
  height: var(--td-comp-size-m);
  background-color: var(--td-brand-color);
  transform: translateY(calc(0px - calc(var(--td-comp-size-m) / 2)));
}

.t-time-picker__panel-body-scroll-item {
  height: var(--td-comp-size-m);
  line-height: var(--td-comp-size-m);
}

.t-time-picker__panel-body-scroll-item.t-is-current {
  color: var(--td-text-color-anti);
}

.t-time-picker__panel-section-body {
  padding: 0;
  margin: 0 calc(0px - var(--td-comp-margin-xs));
}

.t-tree__item .t-icon,
.t-tree__item .t-loading {
  font-size: calc(var(--td-font-size-body-medium) + 4px);
}

.t-tree__item::before {
  height: 26px;
}

.t-tree__line {
  bottom: calc(26px / 2);
  height: 26px;
}

.t-tree__line::before {
  width: calc(calc(var(--td-font-size-body-medium) + 4px) / 2);
  height: 26px;
}

.t-tree__icon {
  width: calc(var(--td-font-size-body-medium) + 4px);
}

.t-tree__icon::after {
  width: calc(calc(var(--td-font-size-body-medium) + 4px) + 4px);
  height: calc(calc(var(--td-font-size-body-medium) + 4px) + 4px);
}

.t-tree__label.t-is-checked {
  background-color: none;
}

.t-tree--transition .t-tree__item--visible {
  max-height: calc(26px * var(--hscale) + 0px);
}

@keyframes t-tree-toggle {
  0% {
    max-height: 0;
    opacity: 0;
  }

  50% {
    max-height: calc(26px * var(--hscale) + 0px);
    opacity: 0;
  }

  100% {
    max-height: calc(26px * var(--hscale) + 0px);
    opacity: 1;
  }
}

.t-card {
  box-shadow: 0px 1px 4px rgba(38, 47, 62, .2);
}

.t-card__body {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
}

.t-card__header {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
}

.t-card__footer {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
}

.dashboard-overview-card.overview-panel {
  box-shadow: none;
}

.dashboard-overview-card.export-panel {
  box-shadow: none;
}

.inner-card {
  box-shadow: none;
}

.dashboard-list-card {
  box-shadow: none;
}

.content-container .card-padding-no {
  box-shadow: none;
}

.user-left-greeting {
  box-shadow: 0px 1px 4px rgba(38, 47, 62, .2);
}

.t-dialog__header {
  font: var(--td-font-title-large);
}

.t-dialog__header--fullscreen {
  padding: 0 var(--td-comp-paddingLR-xl);
}

.t-dialog__body {
  padding: var(--td-comp-paddingTB-s) 0;
}

.t-dialog__body--fullscreen {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-xl);
}

.t-dialog__footer--fullscreen {
  padding: 0 var(--td-comp-paddingLR-xl) var(--td-comp-paddingTB-xl);
}

.t-dialog--default {
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
}

.t-image-viewer__utils .t-image-viewer__utils-content {
  height: 40px;
  padding: none;
}

.t-image-viewer__utils .t-image-viewer__utils-content .t-image-viewer__modal-icon {
  width: var(--td-comp-size-xl);
  height: var(--td-comp-size-xl);
}

.t-list-item {
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-s);
}

.t-list.t-size-s .t-list-item {
  padding: var(--td-comp-paddingTB-xs) var(--td-comp-paddingLR-s);
}

.t-list.t-size-l .t-list-item {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-s);
}

.t-popup[data-popper-placement^='top'] .t-popup__arrow {
  bottom: calc(-8px / 2);
}

.t-progress__inner {
  border-radius: 0;
}

.t-progress__icon {
  font-size: calc(var(--td-font-size-body-medium) + 4px);
}

.t-progress__bar {
  border-radius: 0;
}

.t-progress--plump {
  border-radius: 0;
}

.t-table__pagination {
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-s);
}

.t-table th,
.t-table td {
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-s);
}

.t-table td[key='row-select'] {
  padding: 13px 0 11px var(--td-comp-paddingLR-s);
}

.t-table.t-size-s th,
.t-table.t-size-s td {
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-xs);
}

.t-table.t-size-l th,
.t-table.t-size-l td {
  padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-l);
}

.t-table--bordered .t-table__pagination {
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-s);
}

.t-table__filter-pop-content-inner>.t-date-range-picker__panel {
  margin: calc(0px - var(--td-comp-margin-s));
}

.t-table__filter--bottom-buttons {
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-s);
}

.t-table__row-full-element {
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-s);
}

.t-timeline-item__wrapper {
  width: 10px;
}

.t-timeline-item__wrapper .t-timeline-item__dot {
  top: 6px;
  width: 6px;
  height: 6px;
}

.t-timeline-item__wrapper .t-timeline-item__tail {
  height: calc(100% - (10px * 2 + 6px));
  border-left: 1px solid transparent;
}

.t-timeline-alternate .t-timeline-item-left {
  margin-left: calc(50% - (10px / 2));
}

.t-timeline-alternate .t-timeline-item-right {
  margin-right: calc(50% - (10px / 2));
}

.t-alert {
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
}

.t-alert--info {
  background-color: var(--td-brand-color-light);
}

.t-alert--success {
  background-color: var(--td-success-color-light);
}

.t-alert--success .t-alert__swiper-trigger-wrap {
  color: var(--td-success-color-light);
}

.t-alert--warning .t-alert__swiper-trigger-wrap {
  color: var(--td-warning-color-light);
}

.t-alert--error {
  background-color: var(--td-error-color-light);
}

.t-alert--error .t-alert__swiper-trigger-wrap {
  color: var(--td-error-color-light);
}

.t-message.t-is-closable .t-message__close .t-icon-close {
  font-size: calc(var(--td-font-size-body-medium) + 4px);
}

.t-popconfirm__body>.t-icon {
  padding: calc((var(--td-line-height-body-medium) - calc(var(--td-font-size-body-medium) + 8px)) / 2) 0;
  font-size: calc(var(--td-font-size-body-medium) + 8px);
}

.list-common-table {
  box-shadow: 0px 1px 4px rgba(38, 47, 62, .2);
}

.table-tree-container {
  box-shadow: 0px 1px 4px rgba(38, 47, 62, .2);
}

.form-basic-container {
  box-shadow: 0px 1px 4px rgba(38, 47, 62, .2);
}

.form-step-container {
  box-shadow: 0px 1px 4px rgba(38, 47, 62, .2);
}

.secondary-notification {
  box-shadow: 0px 1px 4px rgba(38, 47, 62, .2);
}

.t-avatar {
  background: var(--td-brand-color-light);
}

.dashboard-item-top>span {
  font-family: 'TCloud Number';
}

.inner-card__content-title {
  font-family: 'TCloud Number';
}

.dashboard-list-card__number {
  font-family: 'TCloud Number';
}