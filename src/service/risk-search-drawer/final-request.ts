/* eslint-disable max-len */
/* eslint-disable import/prefer-default-export */

import { getAppid } from '@src/utils/index';
import { Env } from '@src/constant';
import {
  IDescribeArchScanRiskItemsParams,
  IDescribeArchScanRiskItemsResult,
  IDescribeArchScanRiskInstanceListParams,
  IDescribeArchScanRiskInstanceListResult,
  IDescribeArchScanIgnoreInstanceListParams,
  IDescribeArchScanIgnoreInstanceListResult,
} from './index.type';
import {
  IDescribeArchScanRiskItemsParams as IDescribeArchScanRiskItemsParamsAdmin,
  IDescribeArchScanRiskInstanceListParams as IDescribeArchScanRiskInstanceListParamsAdmin,
  IDescribeArchScanIgnoreInstanceListParams as IDescribeArchScanIgnoreInstanceListParamsAdmin,
} from './index-admin.type';

import {
  describeArchScanRiskItems as describeArchScanRiskItemsOrigin,
  describeArchScanRiskInstanceList as describeArchScanRiskInstanceListOrigin,
  describeArchScanIgnoreInstanceList as describeArchScanIgnoreInstanceListOrigin,
} from './index';
import {
  describeArchScanRiskItems as describeArchScanRiskItemsAdmin,
  describeArchScanRiskInstanceList as describeArchScanRiskInstanceListAdmin,
  describeArchScanIgnoreInstanceList as describeArchScanIgnoreInstanceListAdmin,
} from './index-admin';

type InferParamsType<T extends Env, K, J> = T extends 'CONSOLE'
  ? K
  : J;

/**
 * 巡检插件查询风险项列表页
 * @param params
 * @returns
 */
export const describeArchScanRiskItems = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeArchScanRiskItemsParams['data'], IDescribeArchScanRiskItemsParamsAdmin>;
}): Promise<IDescribeArchScanRiskItemsResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchScanRiskItemsOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchScanRiskItemsAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 分页查询风险项所关联的实例列表
 * @param params
 * @returns
 */
export const describeArchScanRiskInstanceList = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeArchScanRiskInstanceListParams['data'], IDescribeArchScanRiskInstanceListParamsAdmin>;
}): Promise<IDescribeArchScanRiskInstanceListResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchScanRiskInstanceListOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchScanRiskInstanceListAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 分页查询风险项所关联的已忽略实例列表
 * @param params
 * @returns
 */
export const describeArchScanIgnoreInstanceList = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeArchScanIgnoreInstanceListParams['data'], IDescribeArchScanIgnoreInstanceListParamsAdmin>;
}): Promise<IDescribeArchScanIgnoreInstanceListResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchScanIgnoreInstanceListOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchScanIgnoreInstanceListAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};
