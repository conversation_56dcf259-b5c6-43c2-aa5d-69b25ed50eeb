/* eslint-disable import/prefer-default-export */
/* eslint-disable max-len */
import { post } from '@src/utils/request-admin';
import { ResponseResult } from '@src/utils/request.type';
import {
  CommonAdminParams,
  IDescribeArchScanRiskItemsParams,
  IDescribeArchScanRiskItemsResult,
  IDescribeArchScanRiskInstanceListParams,
  IDescribeArchScanRiskInstanceListResult,
  IDescribeArchScanIgnoreInstanceListParams,
  IDescribeArchScanIgnoreInstanceListResult,
} from './index-admin.type';

const commonPath = '/1/arch';

/**
 * @description 巡检插件查询风险项列表页
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanRiskItems
 * <AUTHOR>
export const describeArchScanRiskItems = (params: IDescribeArchScanRiskItemsParams & CommonAdminParams): Promise<ResponseResult<IDescribeArchScanRiskItemsResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchScanRiskItems' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchScanRiskItems' },
);

/**
 * @description 分页查询风险项所关联的实例列表
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanRiskInstanceList
 * <AUTHOR>
export const describeArchScanRiskInstanceList = (params: IDescribeArchScanRiskInstanceListParams & CommonAdminParams): Promise<ResponseResult<IDescribeArchScanRiskInstanceListResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchScanRiskInstanceList' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchScanRiskInstanceList' },
);

/**
 * @description 分页查询风险项所关联的已忽略实例列表
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanIgnoreInstanceList
 * <AUTHOR>
export const describeArchScanIgnoreInstanceList = (params: IDescribeArchScanIgnoreInstanceListParams & CommonAdminParams): Promise<ResponseResult<IDescribeArchScanIgnoreInstanceListResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchScanIgnoreInstanceList' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchScanIgnoreInstanceList' },
);
