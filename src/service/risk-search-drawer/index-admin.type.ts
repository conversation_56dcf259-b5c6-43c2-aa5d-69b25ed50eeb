export type CommonAdminParams = {
  AppId: number;
  Language?: string;
  Uin?: string;
  SubAccountUin?: string;
};

// ====> 巡检插件查询风险项列表页
export interface IDescribeArchScanRiskItemsParams {
  AppId: number;
  MapId: string;
  Filters: {Name: string, Values: string[]}[];
}
export interface IDescribeArchScanRiskItemsResult {
  StrategyTaskItems: {StrategyId: number, TaskId: string}[];
}
// <====

// ====> 分页查询风险项所关联的实例列表
interface Filter {
  Name: string; // 筛选条件名称，例如 "InstanceFuzzy"
  Values: string[]; // 筛选值列表
}
export interface IDescribeArchScanRiskInstanceListParams {
  AppId: number;
  MapId: string; // 架构图 ID
  StrategyId: number; // 风险项 ID
  Limit: number; // 每页行数
  Offset: number; // 偏移量
  TaskId: string; // 任务 ID
  Filters?: Filter[]; // 筛选条件列表
}
export interface IDescribeArchScanRiskInstanceListResult {
  Total: number;
  InstanceItems: {
    InstanceId: string; // 实例 ID
    NodeUuid: string; // 节点 ID
    InstanceTags: string; // 实例标签
    InstanceUrl: string; // 控制台地址
  }[];
}
// <====

// ====> 分页查询风险项所关联的已忽略实例列表
export interface IDescribeArchScanIgnoreInstanceListParams {
  AppId: number;
  MapId: string; // 架构图 ID
  StrategyId: number; // 风险项 ID
  Limit: number; // 每页行数
  Offset: number; // 偏移量
  TaskId: string; // 任务 ID
  Filters?: Filter[]; // 筛选条件列表
}
export interface IDescribeArchScanIgnoreInstanceListResult {
  Total: number;
  InstanceItems: {
    InstanceId: string; // 实例 ID
    NodeUuid: string; // 节点 ID
    InstanceTags: string; // 实例标签
    InstanceUrl: string; // 控制台地址
  }[];
}
// <====
