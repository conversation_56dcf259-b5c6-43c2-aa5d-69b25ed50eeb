/* eslint-disable max-len */
/* eslint-disable import/prefer-default-export */
import { createServiceFunc, ResultCommonType } from '@src/utils/request';

import {
  IDescribeArchScanRiskItemsParams,
  IDescribeArchScanRiskItemsResult,
  IDescribeArchScanRiskInstanceListParams,
  IDescribeArchScanRiskInstanceListResult,
  IDescribeArchScanIgnoreInstanceListParams,
  IDescribeArchScanIgnoreInstanceListResult,
} from './index.type';

/**
 * @description 巡检插件查询风险项列表页
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanRiskItems
 * <AUTHOR>
export const describeArchScanRiskItems: (params: IDescribeArchScanRiskItemsParams) =>
Promise<ResultCommonType<IDescribeArchScanRiskItemsResult>> = createServiceFunc('DescribeArchScanRiskItems');

/**
 * @description 分页查询风险项所关联的实例列表
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanRiskInstanceList
 * <AUTHOR>
export const describeArchScanRiskInstanceList: (params: IDescribeArchScanRiskInstanceListParams) =>
Promise<ResultCommonType<IDescribeArchScanRiskInstanceListResult>> = createServiceFunc('DescribeArchScanRiskInstanceList');

/**
 * @description 分页查询风险项所关联的已忽略实例列表
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanIgnoreInstanceList
 * <AUTHOR>
export const describeArchScanIgnoreInstanceList: (params: IDescribeArchScanIgnoreInstanceListParams) =>
Promise<ResultCommonType<IDescribeArchScanIgnoreInstanceListResult>> = createServiceFunc('DescribeArchScanIgnoreInstanceList');
