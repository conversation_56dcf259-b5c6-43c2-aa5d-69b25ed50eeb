/* eslint-disable max-len */
/* eslint-disable import/prefer-default-export */
import { createServiceFunc, ResultCommonType } from '@src/utils/request';

import {
  IDescribeRiskInstancesInNodeParams,
  IDescribeRiskInstancesInNodeResult,
  IDescribeStrategiesParams,
  IDescribeStrategiesResult,
  IDescribeArchScanTaskRiskInfoParams,
  IDescribeArchScanTaskRiskInfoResult,
  IDescribeSubAccountsByMainAccountResult,
  IUpdateInstanceToClaimedStatusParams,
  IDescribeClaimedInstancesInNodeParams,
  IDescribeClaimedInstancesInNodeResult,
  IDescribeIgnoredInstancesInNodeParams,
  IDescribeIgnoredInstancesInNodeResult,
  IDescribeSafeInstancesInNodeParams,
  IDescribeSafeInstancesInNodeResult,
  IDescribeRiskItemsForInstanceParams,
  IDescribeRiskItemsForInstanceResult,
  IUpdateRiskItemStatusInInstanceParams,
  IDescribeRiskStatisticInNodeParams,
  IDescribeSubscriptionEmailListV2Params,
  IDescribeSubscriptionEmailListV2Result,
} from './index.type';

/**
 * @description 查询节点下存在风险的实例
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskInstancesInNode
 */
export const describeRiskInstancesInNode: (params: IDescribeRiskInstancesInNodeParams) =>
Promise<ResultCommonType<IDescribeRiskInstancesInNodeResult>> = createServiceFunc('DescribeRiskInstancesInNode');

/**
 * @description 查询巡检策略项列表
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeStrategies
 */
export const describeStrategies: (params: IDescribeStrategiesParams) =>
Promise<ResultCommonType<IDescribeStrategiesResult>> = createServiceFunc('DescribeStrategies');

/**
 * @description 查询风险项列表
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanTaskRiskInfo
 */
export const describeArchScanTaskRiskInfo: (params: IDescribeArchScanTaskRiskInfoParams) =>
Promise<ResultCommonType<IDescribeArchScanTaskRiskInfoResult>> = createServiceFunc('DescribeArchScanTaskRiskInfo');

/**
 * @description 查询主账号下的子账号列表
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeSubAccountsByMainAccount
 */
export const describeSubAccountsByMainAccount: () =>
Promise<ResultCommonType<IDescribeSubAccountsByMainAccountResult>> = createServiceFunc('DescribeSubAccountsByMainAccount');

/**
 * @description 获取报告订阅邮箱列表
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeSubAccountsByMainAccount
 */
export const describeSubscriptionEmailListV2: (params: IDescribeSubscriptionEmailListV2Params) =>
Promise<ResultCommonType<IDescribeSubscriptionEmailListV2Result>> = createServiceFunc('DescribeSubscriptionEmailListV2');

/**
 * @description 实例变成已认领状态
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=UpdateInstanceToClaimedStatus
 */
export const updateInstanceToClaimedStatus: (params: IUpdateInstanceToClaimedStatusParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('UpdateInstanceToClaimedStatus');

/**
 * @description 查询节点下存在已认领的实例
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeClaimedInstancesInNode
 */
export const describeClaimedInstancesInNode: (params: IDescribeClaimedInstancesInNodeParams) =>
Promise<ResultCommonType<IDescribeClaimedInstancesInNodeResult>> = createServiceFunc('DescribeClaimedInstancesInNode');

/**
 * @description 查询节点下存在已忽略的实例
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeIgnoredInstancesInNode
 */
export const describeIgnoredInstancesInNode: (params: IDescribeIgnoredInstancesInNodeParams) =>
Promise<ResultCommonType<IDescribeIgnoredInstancesInNodeResult>> = createServiceFunc('DescribeIgnoredInstancesInNode');

/**
 * @description 查询节点下无风险的实例
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeSafeInstancesInNode
 */
export const describeSafeInstancesInNode: (params: IDescribeSafeInstancesInNodeParams) =>
Promise<ResultCommonType<IDescribeSafeInstancesInNodeResult>> = createServiceFunc('DescribeSafeInstancesInNode');

/**
 * @description 查询实例存在的风险项
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeSafeInstancesInNode
 */
export const describeRiskItemsForInstance: (params: IDescribeRiskItemsForInstanceParams) =>
Promise<ResultCommonType<IDescribeRiskItemsForInstanceResult>> = createServiceFunc('DescribeRiskItemsForInstance');

/**
 * @description 修改实例下的风险项状态
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=UpdateRiskItemStatusInInstance
 */
export const updateRiskItemStatusInInstance: (params: IUpdateRiskItemStatusInInstanceParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('UpdateRiskItemStatusInInstance');

/**
 * @description 获取节点风险统计
 * <AUTHOR>
 * @url rokyjiepan
 */
export const describeRiskStatisticInNode: (params: IDescribeRiskStatisticInNodeParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('DescribeRiskStatisticInNode');
