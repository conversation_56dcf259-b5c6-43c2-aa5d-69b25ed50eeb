/* eslint-disable import/prefer-default-export */
/* eslint-disable max-len */
import { post } from '@src/utils/request-admin';
import { ResponseResult } from '@src/utils/request.type';
import {
  IDescribeStrategiesParams,
  IDescribeStrategiesResult,
  IDescribeArchScanTaskRiskInfoParams,
  IDescribeArchScanTaskRiskInfoResult,
  IDescribeRiskInstancesInNodeParams,
  IDescribeRiskInstancesInNodeResult,
  IUpdateInstanceToClaimedStatusParams,
  IDescribeSubAccountsByMainAccountParams,
  IDescribeSubAccountsByMainAccountResult,
  IDescribeClaimedInstancesInNodeParams,
  IDescribeClaimedInstancesInNodeResult,
  IDescribeIgnoredInstancesInNodeParams,
  IDescribeIgnoredInstancesInNodeResult,
  IDescribeSafeInstancesInNodeParams,
  IDescribeSafeInstancesInNodeResult,
  IDescribeRiskItemsForInstanceParams,
  IDescribeRiskItemsForInstanceResult,
  IUpdateRiskItemStatusInInstanceParams,
  IDescribeRiskStatisticInNodeParams,
  IDescribeSubscriptionEmailListV2Params,
  IDescribeSubscriptionEmailListV2Result,
} from './index.admin.type';

const commonPath = '/1/arch';

/**
 * @description 查询巡检策略项列表
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeStrategies
 * <AUTHOR>
export const describeStrategiesAdmin = (params: IDescribeStrategiesParams): Promise<ResponseResult<IDescribeStrategiesResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeStrategies' },
  { enable: false },
  { 'X-TC-Action': 'DescribeStrategies' },
);

/**
 * @description 查询巡检策略项列表
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeArchScanTaskRiskInfo
 * <AUTHOR>
export const describeArchScanTaskRiskInfoAdmin = (params: IDescribeArchScanTaskRiskInfoParams): Promise<ResponseResult<IDescribeArchScanTaskRiskInfoResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchScanTaskRiskInfo' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchScanTaskRiskInfo' },
);

/**
 * @description 查询节点下存在风险的实例
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeRiskInstancesInNode
 * <AUTHOR>
export const describeRiskInstancesInNodeAdmin = (params: IDescribeRiskInstancesInNodeParams): Promise<ResponseResult<IDescribeRiskInstancesInNodeResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeRiskInstancesInNode' },
  { enable: false },
  { 'X-TC-Action': 'DescribeRiskInstancesInNode' },
);

/**
 * @description 实例变成已认领状态
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=UpdateInstanceToClaimedStatus
 * <AUTHOR>
export const updateInstanceToClaimedStatusAdmin = (params: IUpdateInstanceToClaimedStatusParams): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...params, Action: 'UpdateInstanceToClaimedStatus' },
  { enable: false },
  { 'X-TC-Action': 'UpdateInstanceToClaimedStatus' },
);

/**
 * @description 查询主账号下的子账号列表
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeSubAccountsByMainAccount
 * <AUTHOR>
export const describeSubAccountsByMainAccountAdmin = (params: IDescribeSubAccountsByMainAccountParams): Promise<ResponseResult<IDescribeSubAccountsByMainAccountResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeSubAccountsByMainAccount' },
  { enable: false },
  { 'X-TC-Action': 'DescribeSubAccountsByMainAccount' },
);

/**
 * @description 获取报告订阅邮箱列表
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeSubAccountsByMainAccount
 * <AUTHOR>
export const describeSubscriptionEmailListV2Admin = (params: IDescribeSubscriptionEmailListV2Params): Promise<ResponseResult<IDescribeSubscriptionEmailListV2Result>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeSubscriptionEmailListV2' },
  { enable: false },
  { 'X-TC-Action': 'DescribeSubAccountsByMainAccount' },
);

/**
 * @description 查询节点下存在已认领的实例
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeClaimedInstancesInNode
 * <AUTHOR>
export const describeClaimedInstancesInNodeAdmin = (params: IDescribeClaimedInstancesInNodeParams): Promise<ResponseResult<IDescribeClaimedInstancesInNodeResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeClaimedInstancesInNode' },
  { enable: false },
  { 'X-TC-Action': 'DescribeClaimedInstancesInNode' },
);

/**
 * @description 查询节点下存在已忽略的实例
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeIgnoredInstancesInNode
 * <AUTHOR>
export const describeIgnoredInstancesInNodeAdmin = (params: IDescribeIgnoredInstancesInNodeParams): Promise<ResponseResult<IDescribeIgnoredInstancesInNodeResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeIgnoredInstancesInNode' },
  { enable: false },
  { 'X-TC-Action': 'DescribeIgnoredInstancesInNode' },
);

/**
 * @description 查询节点下存在健康的实例
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeSafeInstancesInNode
 * <AUTHOR>
export const describeSafeInstancesInNodeAdmin = (params: IDescribeSafeInstancesInNodeParams): Promise<ResponseResult<IDescribeSafeInstancesInNodeResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeSafeInstancesInNode' },
  { enable: false },
  { 'X-TC-Action': 'DescribeSafeInstancesInNode' },
);

/**
 * @description 查询实例存在的风险项
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeRiskItemsForInstance
 * <AUTHOR>
export const describeRiskItemsForInstanceAdmin = (params: IDescribeRiskItemsForInstanceParams): Promise<ResponseResult<IDescribeRiskItemsForInstanceResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeRiskItemsForInstance' },
  { enable: false },
  { 'X-TC-Action': 'DescribeRiskItemsForInstance' },
);

/**
 * @description 查询实例存在的风险项
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=UpdateRiskItemStatusInInstance
 * <AUTHOR>
export const updateRiskItemStatusInInstanceAdmin = (params: IUpdateRiskItemStatusInInstanceParams): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...params, Action: 'UpdateRiskItemStatusInInstance' },
  { enable: false },
  { 'X-TC-Action': 'UpdateRiskItemStatusInInstance' },
);

/**
 * @description 查询实例存在的风险项
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=UpdateRiskItemStatusInInstance
 * <AUTHOR>
export const describeRiskStatisticInNodeAdmin = (params: IDescribeRiskStatisticInNodeParams): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeRiskStatisticInNode' },
  { enable: false },
  { 'X-TC-Action': 'DescribeRiskStatisticInNode' },
);
