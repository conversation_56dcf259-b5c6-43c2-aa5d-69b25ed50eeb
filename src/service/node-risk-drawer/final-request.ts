/* eslint-disable import/prefer-default-export */
import { getAppid } from '@src/utils/index';
import {
  describeRiskInstancesInNode,
  describeStrategies,
  describeArchScanTaskRiskInfo,
  describeSubAccountsByMainAccount,
  updateInstanceToClaimedStatus,
  describeClaimedInstancesInNode,
  describeIgnoredInstancesInNode,
  describeSafeInstancesInNode,
  describeRiskItemsForInstance,
  updateRiskItemStatusInInstance,
  describeRiskStatisticInNode,
  describeSubscriptionEmailListV2,
} from './index';
import {
  IDescribeRiskInstancesInNodeParams,
  IDescribeRiskInstancesInNodeResult,
  IDescribeStrategiesParams,
  IDescribeStrategiesResult,
  IDescribeArchScanTaskRiskInfoParams,
  IDescribeArchScanTaskRiskInfoResult,
  IDescribeSubAccountsByMainAccountResult,
  IUpdateInstanceToClaimedStatusParams,
  IDescribeClaimedInstancesInNodeParams,
  IDescribeClaimedInstancesInNodeResult,
  IDescribeIgnoredInstancesInNodeParams,
  IDescribeIgnoredInstancesInNodeResult,
  IDescribeSafeInstancesInNodeParams,
  IDescribeSafeInstancesInNodeResult,
  IDescribeRiskItemsForInstanceParams,
  IDescribeRiskItemsForInstanceResult,
  IUpdateRiskItemStatusInInstanceParams,
  IDescribeRiskStatisticInNodeParams,
  IDescribeSubscriptionEmailListV2Params, IDescribeSubscriptionEmailListV2Result,
} from './index.type';

import {
  describeStrategiesAdmin,
  describeArchScanTaskRiskInfoAdmin,
  describeRiskInstancesInNodeAdmin,
  updateInstanceToClaimedStatusAdmin,
  describeSubAccountsByMainAccountAdmin,
  describeClaimedInstancesInNodeAdmin,
  describeIgnoredInstancesInNodeAdmin,
  describeSafeInstancesInNodeAdmin,
  describeRiskItemsForInstanceAdmin,
  updateRiskItemStatusInInstanceAdmin,
  describeRiskStatisticInNodeAdmin,
  describeSubscriptionEmailListV2Admin,
} from './index-admin';

export const describeRiskInstancesInNodeHandle = async (params: {
  env: string;
  apiParams: IDescribeRiskInstancesInNodeParams,
  uin?: string,
}): Promise<IDescribeRiskInstancesInNodeResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeRiskInstancesInNode(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeRiskInstancesInNodeAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeStrategiesHandle = async (params: {
  env: string;
  apiParams: IDescribeStrategiesParams
  uin?: string;
}): Promise<IDescribeStrategiesResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeStrategies(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeStrategiesAdmin({
        Uin: uin,
        SubAccountUin: uin,
        AppId: getAppid(),
        ...apiParams.data,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeArchScanTaskRiskInfoHandle = async (params: {
  env: string;
  apiParams: IDescribeArchScanTaskRiskInfoParams,
  uin?: string,
}): Promise<IDescribeArchScanTaskRiskInfoResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchScanTaskRiskInfo(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchScanTaskRiskInfoAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeSubAccountsByMainAccountHandle = async (params: {
  env: string;
  uin?: string;
}): Promise<IDescribeSubAccountsByMainAccountResult | undefined> => {
  try {
    const {
      env,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeSubAccountsByMainAccount();
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeSubAccountsByMainAccountAdmin({
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeSubscriptionEmailListV2Handle = async (params: {
  env: string;
  uin?: string;
  apiParams?: IDescribeSubscriptionEmailListV2Params,
}): Promise<IDescribeSubscriptionEmailListV2Result | undefined> => {
  try {
    const {
      env,
      uin,
      apiParams,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeSubscriptionEmailListV2(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeSubscriptionEmailListV2Admin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const updateInstanceToClaimedStatusHandle = async (params: {
  env: string;
  apiParams: IUpdateInstanceToClaimedStatusParams;
  uin?: string;
}): Promise<IDescribeSubAccountsByMainAccountResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await updateInstanceToClaimedStatus(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await updateInstanceToClaimedStatusAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs ?? await Promise.reject(new Error(''));
  } catch (error) {
    throw new Error(error);
  }
};

export const describeClaimedInstancesInNodeHandle = async (params: {
  env: string;
  apiParams: IDescribeClaimedInstancesInNodeParams;
  uin?: string;
}): Promise<IDescribeClaimedInstancesInNodeResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeClaimedInstancesInNode(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeClaimedInstancesInNodeAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeIgnoredInstancesInNodeHandle = async (params: {
  env: string;
  apiParams: IDescribeIgnoredInstancesInNodeParams;
  uin?: string;
}): Promise<IDescribeIgnoredInstancesInNodeResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeIgnoredInstancesInNode(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeIgnoredInstancesInNodeAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeSafeInstancesInNodeHandle = async (params: {
  env: string;
  apiParams: IDescribeSafeInstancesInNodeParams;
  uin?: string;
}): Promise<IDescribeSafeInstancesInNodeResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeSafeInstancesInNode(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeSafeInstancesInNodeAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeRiskItemsForInstanceHandle = async (params: {
  env: string;
  apiParams: IDescribeRiskItemsForInstanceParams;
  uin?: string;
}): Promise<IDescribeRiskItemsForInstanceResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeRiskItemsForInstance(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeRiskItemsForInstanceAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const updateRiskItemStatusInInstanceHandle = async (params: {
  env: string;
  apiParams: IUpdateRiskItemStatusInInstanceParams;
  uin?: string;
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await updateRiskItemStatusInInstance(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await updateRiskItemStatusInInstanceAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeRiskStatisticInNodeHandle = async (params: {
  env: string;
  apiParams: IDescribeRiskStatisticInNodeParams;
  uin?: string;
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeRiskStatisticInNode(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeRiskStatisticInNodeAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};
