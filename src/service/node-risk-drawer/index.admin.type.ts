export interface IDescribeStrategiesParams {
  StrategyFilters: Array<{
    Name: string;
    Values: string[];
  }>;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IStrategiesItem {
  Conditions: Array<{
    ConditionId: number;
    Desc: string;
    Level: number;
    LevelDesc: string;
  }>;
  Desc: string;
  GroupId: number;
  GroupName: string;
  Ignore: string;
  IsSupportCustom: boolean;
  Name: string;
  Notice: string;
  Product: string;
  ProductDesc: string;
  Repair: string;
  StrategyId: number;
  SubProduct: string;
}

export interface IDescribeStrategiesResult {
  Strategies: IStrategiesItem[];
}

export interface IDescribeArchScanTaskRiskInfoParams {
  MapId: string;
  NodeUuid?: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IDescribeArchScanTaskRiskInfoResult {
  TotalStrategyCount: number;
  LastScanAt: string;
  BindInstanceCount: number;
  NodeChanged: boolean;
  Product?: string;
}

export interface IDescribeRiskInstancesInNodeParams {
  MapId: string;
  NodeUuid: string;
  TaskId: string;
  Limit: number;
  Offset: number;
  Filters?: Array<{
    Name: string;
    Values: string[];
  }>;
  TagList?: Array<{
    Key: string;
    Value: string;
  }>;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface INodeRiskInstanceItem {
  InstanceId: string;
  HighRiskCount: number;
  MediumRiskCount: number;
  Tag: string;
  Url: string;
  Region: string;
  IsChange: boolean;
}

export interface IDescribeRiskInstancesInNodeResult {
  NodeRiskInstanceList: INodeRiskInstanceItem[];
  TotalCount: number;
}

export interface IUpdateInstanceToClaimedStatusParams {
  MapId: string;
  NodeUuid: string;
  Operate: string;
  InstanceIdList: string[];
  ClaimPerson: string;
  ClaimUin: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IDescribeSubAccountsByMainAccountParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IDescribeSubscriptionEmailListV2Params {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  SubId?: number;
  Email?: string;
  Limit: number;
  Offset: number;
  UserName?: string;
}

enum OperateEnum {
  add = 'add',
  edit = 'edit',
  view = 'view'
}

export interface IEmailItem {
  AppId: number;
  Email: string;
  SubUin: string;
  UpdateTime: string;
  UserName: string;
  Id: string;
  Status: OperateEnum; // 前端新增字段用于表格内渲染表单逻辑
  UserNameVerifyMsg?: string; // 前端新增业务字段，校验message
  EmailVerifyMsg?: string; // 前端新增业务字段，校验message
}

export interface IDescribeSubscriptionEmailListV2Result {
  EmailList: string[];
  EmailDetails: IEmailItem[];
  TotalCount: number;
}

export interface IDescribeSubAccountsByMainAccountItem {
  Uin: number;
  Name: string;
  NickName: string;
  Uid: number;
}

export interface IDescribeSubAccountsByMainAccountResult {
  SubAccountInfoList: IDescribeSubAccountsByMainAccountItem[];
}

export interface IDescribeClaimedInstancesInNodeParams {
  MapId: string;
  NodeUuid: string;
  TaskId: string;
  Limit: number;
  Offset: number;
  Filters?: Array<{
    Name: string;
    Values: string[];
  }>;
  TagList?: Array<{
    Key: string;
    Value: string;
  }>;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface INodeClaimedInstanceItem {
  InstanceId: string;
  HighRiskCount: number;
  MediumRiskCount: number;
  Tag: string;
  ClaimPerson: string;
  Url: string;
  Region: string;
}

export interface IDescribeClaimedInstancesInNodeResult {
  NodeClaimedInstanceList: INodeClaimedInstanceItem[];
  TotalCount: number;
}

export interface IDescribeIgnoredInstancesInNodeParams {
  MapId: string;
  NodeUuid: string;
  TaskId: string;
  Limit: number;
  Offset: number;
  Filters?: Array<{
    Name: string;
    Values: string[];
  }>;
  TagList?: Array<{
    Key: string;
    Value: string;
  }>;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IDescribeIgnoredInstancesInNodeItem {
  InstanceId: string;
  IgnoredTime: string;
  IgnoredReasons: string;
  IgnoredPerson: string;
  Url: string;
}

export interface IDescribeIgnoredInstancesInNodeResult {
  NodeIgnoredInstanceList: IDescribeIgnoredInstancesInNodeItem[];
  TotalCount: number;
}

export interface IDescribeSafeInstancesInNodeParams {
  MapId: string;
  NodeUuid: string;
  TaskId: string;
  Limit: number;
  Offset: number;
  Filters?: Array<{
    Name: string;
    Values: string[];
  }>;
  TagList?: Array<{
    Key: string;
    Value: string;
  }>;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface INodeSafeInstanceItem {
  InstanceId: string;
  Tag: string;
  IsIgnored: boolean;
  Url: string;
}

export interface IDescribeSafeInstancesInNodeResult {
  NodeSafeInstanceList: INodeSafeInstanceItem[];
  TotalCount: number;
}

export interface IDescribeRiskItemsForInstanceParams {
  MapId: string;
  NodeUuid: string;
  TaskId: string;
  InstanceId: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IInstanceRiskItem {
  StrategyId: number;
  ConditionId: number;
}

export interface IDescribeRiskItemsForInstanceResult {
  InstanceRiskItemList: IInstanceRiskItem[];
}

export interface IUpdateRiskItemStatusInInstanceParams {
  InstanceRegionList: {
    InstanceId: string;
    Region: string;
  }[];
  StrategyId: number;
  IgnoredReason: string;
  Operate: string;
  IgnoredPerson: string;
  ClaimUin: string;
  MapId: string;
  NodeUuid: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IDescribeRiskStatisticInNodeParams {
  MapId: string;
  NodeUuid: string;
  TaskId: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}
