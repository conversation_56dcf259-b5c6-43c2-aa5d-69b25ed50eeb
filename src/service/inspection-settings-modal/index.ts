/* eslint-disable max-len */
/* eslint-disable import/prefer-default-export */
import { createServiceFunc, ResultCommonType } from '@src/utils/request';

import {
  IDescribeGroupAndProductInfosResult,
  IListIgnoreStrategiesParams,
  IListIgnoreStrategiesResult,
  IDescribeConfigParams,
  IDescribeConfigResult,
  IModifyArchIgnoreStrategiesParams,
  IDescribeIgnoredStrategyParams,
  IDescribeIgnoredStrategyResult,
  IModifyCustomThresholdConditionParams,
  IDescribeCustomThresholdConditionParams,
  IDescribeCustomThresholdConditionResult,
} from './index.type';

export const describeGroupAndProductInfos: () =>
Promise<ResultCommonType<IDescribeGroupAndProductInfosResult>> = createServiceFunc('DescribeGroupAndProductInfos');

export const listIgnoreStrategies: (params: IListIgnoreStrategiesParams) =>
Promise<ResultCommonType<IListIgnoreStrategiesResult>> = createServiceFunc('ListIgnoreStrategies');

export const describeConfig: (params: IDescribeConfigParams) =>
Promise<ResultCommonType<IDescribeConfigResult>> = createServiceFunc('DescribeConfig');

export const modifyArchIgnoreStrategies: (params: IModifyArchIgnoreStrategiesParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('ModifyArchIgnoreStrategies');

export const describeIgnoredStrategy: (params: IDescribeIgnoredStrategyParams) =>
Promise<ResultCommonType<IDescribeIgnoredStrategyResult>> = createServiceFunc('DescribeIgnoredStrategy');

export const modifyCustomThresholdCondition: (params: IModifyCustomThresholdConditionParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('ModifyCustomThresholdCondition');

export const describeCustomThresholdCondition: (params: IDescribeCustomThresholdConditionParams) =>
Promise<ResultCommonType<IDescribeCustomThresholdConditionResult>> = createServiceFunc('DescribeCustomThresholdCondition');
