import { getAppid } from '@src/utils/index';
/* eslint-disable import/prefer-default-export */
import {
  describeGroupAndProductInfos,
  listIgnoreStrategies,
  describeConfig,
  modifyArchIgnoreStrategies,
  describeIgnoredStrategy,
  modifyCustomThresholdCondition,
  describeCustomThresholdCondition,
} from './index';
import {
  IDescribeGroupAndProductInfosResult,
  IFiltersItem,
  IListIgnoreStrategiesResult,
  IDescribeConfigResult,
  IDescribeIgnoredStrategyResult,
  IDescribeCustomThresholdConditionResult,
} from './index.type';
import { describeGroupAndProductInfosAdmin } from './index-admin';

export const describeGroupAndProductInfosHandle = async (params: {
  env: string;
  uin?: string;
}): Promise<IDescribeGroupAndProductInfosResult | undefined> => {
  try {
    const {
      env,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeGroupAndProductInfos();
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeGroupAndProductInfosAdmin({
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const listIgnoreStrategiesHandle = async (params: {
  env: string;
  filters: IFiltersItem[],
}): Promise<IListIgnoreStrategiesResult | undefined> => {
  try {
    const {
      env,
      filters,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await listIgnoreStrategies({
        data: {
          Filters: filters,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeConfigHandle = async (params: {
  env: string;
  filters: IFiltersItem[],
}): Promise<IDescribeConfigResult | undefined> => {
  try {
    const {
      env,
      filters,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeConfig({
        data: {
          Filters: filters,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const modifyArchIgnoreStrategiesHandle = async (params: {
  env: string;
  modifyParams: any,
}): Promise<IDescribeConfigResult | undefined> => {
  try {
    const {
      env,
      modifyParams,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await modifyArchIgnoreStrategies({
        data: modifyParams,
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeIgnoredStrategyHandle = async (params: {
  env: string;
  filters: IFiltersItem[],
}): Promise<IDescribeIgnoredStrategyResult | undefined> => {
  try {
    const {
      env,
      filters,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeIgnoredStrategy({
        data: {
          Filters: filters,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const modifyCustomThresholdConditionHandle = async (params: {
  env: string;
  Filters: any;
  Condition: any;
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      Filters,
      Condition,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await modifyCustomThresholdCondition({
        data: {
          Filters,
          Condition,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeCustomThresholdConditionHandle = async (params: {
  env: string;
  Filters: any;
  StrategyId: number;
}): Promise<IDescribeCustomThresholdConditionResult | undefined> => {
  try {
    const {
      env,
      Filters,
      StrategyId,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeCustomThresholdCondition({
        data: {
          Filters,
          StrategyId,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};
