export interface IDescribeGroupAndProductInfosResult {
  Groups: {
    Id: number;
    GroupName: string;
  }[],
  Products: {
    Name: string;
    Product: string;
  }[]
}

export interface IFiltersItem {
  Name: string;
  Values: string[];
}

export interface IListIgnoreStrategiesParams {
  data: {
    Filters: IFiltersItem[];
  }
}

export interface IListIgnoreStrategiesResult {
  StrategyIds: number[];
}

export interface IDescribeConfigParams {
  data: {
    Filters: IFiltersItem[];
  }
}

export interface IConditionItem {
  ConditionId: number;
  Desc: string;
  Level: number;
}

export interface IStrategiesItem {
  Conditions: IConditionItem[];
  Desc: string;
  Ignore: string;
  IsSupportCustom: boolean;
  Name: string;
  Notice: string;
  Product: string;
  Repair: string;
  StrategyId: number;
}

export interface IGroupItem {
  GroupId: number;
  Name: string;
  Strategies: IStrategiesItem[];
}

export interface IDescribeConfigResult {
  Groups: IGroupItem[];
}

export interface IModifyArchIgnoreStrategiesParams {
  data: {
    Filters: IFiltersItem[];
    Operate: string;
    StrategyId: number;
  }
}

export interface IDescribeIgnoredStrategyParams {
  data: {
    Filters: IFiltersItem[];
  }
}

export interface IIgnoredStrategyItem {
  IgnoreType: number;
  StrategyId: number;
}

export interface IDescribeIgnoredStrategyResult {
  IgnoredStrategyList: IIgnoredStrategyItem[];
}

export interface PolicyItem {
  MetricName: string,
  MetricNameAlias: string,
  Factor: string,
  Days: number,
  MaxPercent: number,
  Value: number,
  Unit: string,
  PercentList: number[]
}

export interface ConditionItem {
  StrategyId: number,
  ConditionId: number,
  RiskLevel: number,
  LogicalOperators: string,
  Policy: Array<PolicyItem>,
}

export interface Strategy {
  strategyId: number,
  type: string,
  productShortName: string,
  product: string,
  strategyName: string,
  strategyDescribe: string,
  isAssessing: boolean,
  IsSupportCustom: boolean,
  env?: string,
  Conditions?: any
}

export interface IModifyCustomThresholdConditionParams {
  data: {
    Filters: IFiltersItem[];
    Condition: any;
  }
}

export interface IDescribeCustomThresholdConditionParams {
  data: {
    Filters: IFiltersItem[];
    StrategyId: number;
  }
}

export interface IPolicyItem {
  Days: number;
  Factor: string;
  MaxPercent: number;
  MetricName: string;
  MetricNameAlias: string;
  Unit: string;
  Value: number;
  PercentList: number[];
}

export interface ICustomThresholdConditionItem {
  ConditionId: number;
  LogicalOperators: string;
  Policy: IPolicyItem[];
  RiskLevel: number;
  StrategyId: number;
}

export interface IDescribeCustomThresholdConditionResult {
  Condition: ICustomThresholdConditionItem[];
}
