/* eslint-disable import/prefer-default-export */
/* eslint-disable max-len */
import { post } from '@src/utils/request-admin';
import { ResponseResult } from '@src/utils/request.type';
import {
  IDescribeGroupAndProductInfosResult,
} from './index.type';
import {
  CommonAdminParams,
} from './index.admin.type';

const commonPath = '/1/arch';

export const describeGroupAndProductInfosAdmin = (params: CommonAdminParams): Promise<ResponseResult<IDescribeGroupAndProductInfosResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeGroupAndProductInfos' },
  { enable: false },
  { 'X-TC-Action': 'DescribeGroupAndProductInfos' },
);
