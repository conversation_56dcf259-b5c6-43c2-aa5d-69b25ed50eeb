export interface IDescribeArchTaskProgressParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  Filters?: Array<{
    Name: string;
    Values: string[];
  }>;
}
export interface IDescribeArchTaskProgressResult {
  IsFinish: boolean;
  Message: string;
  FinishTime: string;
  LatestScanType: number;
  CostTime: string;
  BindInsCount: number;
  NodeTaskStatusList: {NodeUuid: string; IsFinish: boolean}[];
  Progress: {ScannedCount: number; TotalCount: number};
}

export interface IDescribeArchScanRiskInfoParams {
  MapId: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IScanNodeRiskItem {
  LastTaskId: string;
  LastSuccessTaskId: string;
  HighRiskCount: number;
  MediumRiskCount: number;
  IsFinish: boolean;
  NodeUuid: string;
}

export interface IDescribeArchScanRiskInfoResult {
  TotalHighRiskCount: number;
  TotalMediumRiskCount: number;
  NodeRiskItems: IScanNodeRiskItem[];
  IsFinish: boolean;
  IsSynced: boolean;
}

export interface IIDescribeArchNodeConfigInfoParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IDescribeArchNodeConfigInfoResult {
  SupportTaskProductList: string[];
}

export interface ICreateArchScanTaskParams {
  IsCusScan: number; // 0 自动巡检 1 手动巡检
  MapId: string;
  UserName: string;
  NodeIdList?: string[];
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface ICreateArchScanTaskResult {
  IsTaskRunning: boolean;
  TaskId: string;
}

export interface IStopArchScanParams {
  ArchId: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IDescribeArchSyncTaskProgressParams {
  MapId: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IDescribeArchSyncTaskProgressResult {
  IsChange: boolean;
  TaskProgress: number;
}

export interface ICreateArchInfoSyncTaskParams {
  MapId: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface ICreateArchInfoSyncTaskResult {
  TaskId: string;
}

export interface IStopArchSyncTaskParams {
  MapId: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IUpdateInstanceToIgnoredStatusParams {
  MapId: string;
  NodeUuid: string;
  Operate: string;
  InstanceIdList: string[];
  Reason: string;
  Person: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IGetTagKeysParams {
  Region?: string;
  Language?: string;
  MaxResults?: number;
  PaginationToken?: string;
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}

export interface IGetTagKeysResult {
  TagKeys: string[];
  PaginationToken?: string;
}

export interface IGetTagValuesParams {
  Region?: string;
  Language?: string;
  MaxResults?: number;
  PaginationToken?: string;
  TagKeys?: string[];
  AppId: number;
  Uin: string;
  SubAccountUin: string;
}
