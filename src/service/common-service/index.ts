/* eslint-disable max-len */
/* eslint-disable import/prefer-default-export */
import { createServiceFunc, ResultCommonType } from '@src/utils/request';

import {
  IDescribeArchNodeConfigInfoResult,
  IDescribeArchTaskProgressParams,
  IDescribeArchTaskProgressResult,
  ICreateArchScanTaskParams,
  ICreateArchScanTaskResult,
  IDescribeArchScanRiskInfoParams,
  IDescribeArchScanRiskInfoResult,
  IStopArchScanParams,
  ICreateArchInfoSyncTaskParams,
  ICreateArchInfoSyncTaskResult,
  IDescribeArchSyncTaskProgressParams,
  IDescribeArchSyncTaskProgressResult,
  IStopArchSyncTaskParams,
  IGetTagKeysParams,
  IGetTagKeysResult,
  IGetTagValuesParams,
  IUpdateInstanceToIgnoredStatusParams,
} from './index.type';

/**
 * @description 获取支持巡检任务的产品列表
 * @url https://capi.woa.com/apidoc?keyword=DescribeArchNodeConfigInfo&product=advisor&version=2020-07-21&action=DescribeArchNodeConfigInfo
 * <AUTHOR>
export const describeArchNodeConfigInfo: () =>
Promise<ResultCommonType<IDescribeArchNodeConfigInfoResult>> = createServiceFunc('DescribeArchNodeConfigInfo');

/**
 * @description 查询架构图巡检进度
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeArchTaskProgress
 * <AUTHOR>
export const describeArchTaskProgress: (params: IDescribeArchTaskProgressParams) =>
Promise<ResultCommonType<IDescribeArchTaskProgressResult>> = createServiceFunc('DescribeArchTaskProgress');

/**
 * @description 创建巡检任务
 * <AUTHOR>
export const createArchScanTask: (params: ICreateArchScanTaskParams) =>
Promise<ResultCommonType<ICreateArchScanTaskResult>> = createServiceFunc('CreateArchScanTask');

/**
 * @description 停止巡检任务
 * <AUTHOR>
export const stopArchScan: (params: IStopArchScanParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('StopArchScan');

/**
 * @description 查询架构图巡检风险角标和顶栏信息
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanRiskInfo
 */
export const describeArchScanRiskInfo: (params: IDescribeArchScanRiskInfoParams) =>
Promise<ResultCommonType<IDescribeArchScanRiskInfoResult>> = createServiceFunc('DescribeArchScanRiskInfo');

/**
 * @description 发起架构图同步任务接口
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=CreateArchInfoSyncTask
 */
export const createArchInfoSyncTask: (params: ICreateArchInfoSyncTaskParams) =>
Promise<ResultCommonType<ICreateArchInfoSyncTaskResult>> = createServiceFunc('CreateArchInfoSyncTask');

/**
 * @description 查询架构图同步任务进度接口
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchSyncTaskProgress
 */
export const describeArchSyncTaskProgress: (params: IDescribeArchSyncTaskProgressParams) =>
Promise<ResultCommonType<IDescribeArchSyncTaskProgressResult>> = createServiceFunc('DescribeArchSyncTaskProgress');

/**
 * @description 查询架构图同步任务进度接口
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=StopArchSyncTask
 */
export const stopArchSyncTask: (params: IStopArchSyncTaskParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('StopArchSyncTask');

/**
 * 查询标签键列表
 * @description 查询标签键列表
 * @url https://cloud.tencent.com/document/api/651/72277
 */
export const getTagKeys: (
  params: IGetTagKeysParams
) => Promise<ResultCommonType<IGetTagKeysResult>> = createServiceFunc(
  'GetTagKeys',
  'tag',
  '2018-08-13',
);

/**
 * 查询标签值列表
 * @description 查询标签值列表
 * @url https://cloud.tencent.com/document/api/651/72276
 */
export const getTagValues: (
  params: IGetTagValuesParams
) => Promise<ResultCommonType<any>> = createServiceFunc(
  'GetTagValues',
  'tag',
  '2018-08-13',
);

/**
 * @description 实例修改成已忽略状态
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=UpdateInstanceToIgnoredStatus
 */
export const updateInstanceToIgnoredStatus: (params: IUpdateInstanceToIgnoredStatusParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('UpdateInstanceToIgnoredStatus');
