export interface IDescribeArchNodeConfigInfoResult {
  SupportTaskProductList: string[];
}

export interface IDescribeArchTaskProgressParams {
  data: {
    Filters?: Array<{
      Name: string;
      Values: string[];
    }>;
  }
}

export interface IDescribeArchTaskProgressResult {
  IsFinish: boolean;
  Message?: string;
  FinishTime: string;
  LatestScanType: number;
  CostTime: string;
  BindInsCount: number;
  NodeTaskStatusList: {NodeUuid: string; IsFinish: boolean}[];
  Progress: {ScannedCount: number; TotalCount: number};
  CurrentTime: string; // 服务器端当前时间
  TimeGap: number; // 定义超过多少时间后能够发起初始化巡检
}

export interface ICreateArchScanTaskParams {
  data: {
    IsCusScan: number; // 0 自动巡检 1 手动巡检
    MapId: string;
    UserName: string;
    NodeIdList?: string[];
  }
}

export interface ICreateArchScanTaskResult {
  IsTaskRunning: boolean;
  TaskId: string;
}

export interface IDescribeArchScanRiskInfoParams {
  data: {
    MapId: string;
    GroupIds?: number[]
  }
}

export interface IScanNodeRiskItem {
  LastTaskId: string;
  LastSuccessTaskId: string;
  HighRiskCount: number;
  MediumRiskCount: number;
  IsFinish: boolean;
  NodeUuid: string;
}

export interface IDescribeArchScanRiskInfoResult {
  TotalHighRiskCount: number;
  TotalMediumRiskCount: number;
  NodeRiskItems: IScanNodeRiskItem[];
  IsFinish: boolean;
  IsSynced: boolean;
}

export interface IStopArchScanParams {
  data: {
    ArchId: string;
  }
}

export interface ICreateArchInfoSyncTaskParams {
  data: {
    MapId: string
  }
}

export interface ICreateArchInfoSyncTaskResult {
  TaskId: string;
}

export interface IDescribeArchSyncTaskProgressParams {
  data: {
    MapId: string;
  }
}

export interface IDescribeArchSyncTaskProgressResult {
  IsChange: boolean;
  TaskProgress: number;
}

export interface IStopArchSyncTaskParams {
  data: {
    MapId: string;
  }
}

export interface IGetTagKeysParams {
  data: {
    Region?: string;
    Language?: string;
    MaxResults?: number;
    PaginationToken?: string;
  }
}

export interface IGetTagKeysResult {
  TagKeys: string[];
  PaginationToken?: string;
}

export interface IGetTagValuesParams {
  data: {
    Region?: string;
    Language?: string;
    MaxResults?: number;
    PaginationToken?: string;
    TagKeys?: string[];
  }
}

export interface IUpdateInstanceToIgnoredStatusParams {
  data: {
    MapId: string;
    NodeUuid: string;
    Operate: string;
    InstanceIdList: string[];
    Reason: string;
    Person: string;
  }
}
