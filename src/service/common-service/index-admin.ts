/* eslint-disable import/prefer-default-export */
/* eslint-disable max-len */
import { post } from '@src/utils/request-admin';
import { ResponseResult } from '@src/utils/request.type';
import {
  IDescribeArchTaskProgressParams,
  IDescribeArchTaskProgressResult,
  IDescribeArchScanRiskInfoParams,
  IDescribeArchScanRiskInfoResult,
  IIDescribeArchNodeConfigInfoParams,
  IDescribeArchNodeConfigInfoResult,
  ICreateArchScanTaskParams,
  ICreateArchScanTaskResult,
  IStopArchScanParams,
  IDescribeArchSyncTaskProgressParams,
  IDescribeArchSyncTaskProgressResult,
  ICreateArchInfoSyncTaskParams,
  ICreateArchInfoSyncTaskResult,
  IStopArchSyncTaskParams,
  IUpdateInstanceToIgnoredStatusParams,
  IGetTagKeysParams,
  IGetTagKeysResult,
  IGetTagValuesParams,
} from './index-admin.type';

const commonPath = '/1/arch';

/**
 * @description 查询架构图巡检进度
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeArchTaskProgress
 * <AUTHOR>
export const describeArchTaskProgress = (params: IDescribeArchTaskProgressParams): Promise<ResponseResult<IDescribeArchTaskProgressResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchTaskProgress' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchTaskProgress' },
);

/**
 * @description 查询架构图角标与顶栏
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeArchScanRiskInfo
 * <AUTHOR>
export const describeArchScanRiskInfoAdmin = (params: IDescribeArchScanRiskInfoParams): Promise<ResponseResult<IDescribeArchScanRiskInfoResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchScanRiskInfo' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchScanRiskInfo' },
);

/**
 * @description 获取支持巡检任务的产品列表
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeArchNodeConfigInfo
 * <AUTHOR>
export const describeArchNodeConfigInfoAdmin = (param: IIDescribeArchNodeConfigInfoParams): Promise<ResponseResult<IDescribeArchNodeConfigInfoResult>> => post(
  `${commonPath}`,
  { ...param, Action: 'DescribeArchNodeConfigInfo' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchNodeConfigInfo' },
);

/**
 * @description 创建巡检任务
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=CreateArchScanTask
 * <AUTHOR>
export const createArchScanTaskAdmin = (param: ICreateArchScanTaskParams): Promise<ResponseResult<ICreateArchScanTaskResult>> => post(
  `${commonPath}`,
  {
    ...param, Action: 'CreateArchScanTask', OnlyData: true, ShowError: true, Language: 'zh-CN',
  },
  { enable: false },
  { 'X-TC-Action': 'CreateArchScanTask' },
);

/**
 * @description 停止巡检任务
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=StopArchScan
 * <AUTHOR>
export const stopArchScanAdmin = (param: IStopArchScanParams): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...param, Action: 'StopArchScan' },
  { enable: false },
  { 'X-TC-Action': 'StopArchScan' },
);

/**
 * @description 查询架构图同步任务进度接口
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeArchSyncTaskProgress
 * <AUTHOR>
export const describeArchSyncTaskProgressAdmin = (param: IDescribeArchSyncTaskProgressParams): Promise<ResponseResult<IDescribeArchSyncTaskProgressResult>> => post(
  `${commonPath}`,
  { ...param, Action: 'DescribeArchSyncTaskProgress' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchSyncTaskProgress' },
);

/**
 * @description 发起架构图同步任务接口
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=CreateArchInfoSyncTask
 * <AUTHOR>
export const createArchInfoSyncTaskAdmin = (param: ICreateArchInfoSyncTaskParams): Promise<ResponseResult<ICreateArchInfoSyncTaskResult>> => post(
  `${commonPath}`,
  { ...param, Action: 'CreateArchInfoSyncTask' },
  { enable: false },
  { 'X-TC-Action': 'CreateArchInfoSyncTask' },
);

/**
 * @description 查询架构图同步任务进度接口
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=StopArchSyncTask
 * <AUTHOR>
export const stopArchSyncTaskAdmin = (param: IStopArchSyncTaskParams): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...param, Action: 'StopArchSyncTask' },
  { enable: false },
  { 'X-TC-Action': 'StopArchSyncTask' },
);

/**
 * @description 实例修改成已忽略状态
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=UpdateInstanceToIgnoredStatus
 * <AUTHOR>
export const updateInstanceToIgnoredStatusAdmin = (param: IUpdateInstanceToIgnoredStatusParams): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...param, Action: 'UpdateInstanceToIgnoredStatus' },
  { enable: false },
  { 'X-TC-Action': 'UpdateInstanceToIgnoredStatus' },
);

/**
 * @description 查询tag
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=UpdateInstanceToIgnoredStatus
 * <AUTHOR>
export const getTagKeysAdmin = (param: IGetTagKeysParams): Promise<ResponseResult<IGetTagKeysResult>> => post(
  '/1/interface',
  { ...param, Action: 'GetTagKeys' },
  { enable: false },
  { 'X-TC-Action': 'GetTagKeys' },
);

/**
 * @description 查询tagvalue
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=UpdateInstanceToIgnoredStatus
 * <AUTHOR>
export const getTagValuesAdmin = (param: IGetTagValuesParams): Promise<ResponseResult<any>> => post(
  '/1/interface',
  { ...param, Action: 'GetTagValues' },
  { enable: false },
  { 'X-TC-Action': 'GetTagValues' },
);
