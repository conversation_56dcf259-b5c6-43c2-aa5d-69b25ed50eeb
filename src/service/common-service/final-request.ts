import { getAppid } from '@src/utils/index';
import { Env } from '@src/constant';
import {
  describeArchNodeConfigInfo,
  describeArchTaskProgress,
  createArchScanTask,
  stopArchScan,
  createArchInfoSyncTask,
  describeArchSyncTaskProgress,
  stopArchSyncTask,
  describeArchScanRiskInfo,
  getTagKeys,
  getTagValues,
  updateInstanceToIgnoredStatus,
} from './index';
import {
  IDescribeArchTaskProgressParams,
  IDescribeArchTaskProgressResult,
  ICreateArchScanTaskParams,
  ICreateArchScanTaskResult,
  IStopArchScanParams,
  ICreateArchInfoSyncTaskParams,
  ICreateArchInfoSyncTaskResult,
  IDescribeArchSyncTaskProgressParams,
  IDescribeArchSyncTaskProgressResult,
  IStopArchSyncTaskParams,
  IDescribeArchScanRiskInfoParams,
  IDescribeArchScanRiskInfoResult,
  IGetTagKeysParams,
  IGetTagKeysResult,
  IGetTagValuesParams,
  IUpdateInstanceToIgnoredStatusParams,
} from './index.type';

import {
  IDescribeArchTaskProgressParams as IDescribeArchTaskProgressParamsAdmin,
//   IDescribeArchTaskProgressResult as IDescribeArchTaskProgressResultAdmin,
} from './index-admin.type';

import {
  describeArchTaskProgress as describeArchTaskProgressAdmin,
  describeArchScanRiskInfoAdmin,
  describeArchNodeConfigInfoAdmin,
  createArchScanTaskAdmin,
  stopArchScanAdmin,
  describeArchSyncTaskProgressAdmin,
  createArchInfoSyncTaskAdmin,
  stopArchSyncTaskAdmin,
  updateInstanceToIgnoredStatusAdmin,
  getTagKeysAdmin,
  getTagValuesAdmin,
} from './index-admin';

type InferParamsType<T extends Env> = T extends 'CONSOLE'
  ? IDescribeArchTaskProgressParams['data']
  : IDescribeArchTaskProgressParamsAdmin;

/**
 * 获取支持巡检任务的产品列表
 * @param params
 * @returns
 */
export const describeArchNodeConfigInfoHandle = async (params: {
  env: string;
  uin?: string;
}): Promise<string[]> => {
  try {
    const {
      env,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchNodeConfigInfo();
      if (!result?.data.Response.Error) {
        rs = result.data.Response.SupportTaskProductList;
      }
    } else {
      const result = await describeArchNodeConfigInfoAdmin({
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response.SupportTaskProductList;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 获取
 * @param params
 * @returns
 */
export const fetchArchTaskProgress = async <E extends Env>(params: {
  env: E;
  uin?: string;
  data: InferParamsType<E>;
}): Promise<IDescribeArchTaskProgressResult | undefined> => {
  try {
    const {
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchTaskProgress({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        const temp = result?.data?.Response;
        if (temp) {
          rs = {
            ...temp,
            NodeTaskStatusList: temp?.NodeTaskStatusList?.length ? temp?.NodeTaskStatusList : [],
          };
        }
      }
    } else {
      const result = await describeArchTaskProgressAdmin({
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        const temp = result?.Response;
        if (temp) {
          rs = {
            ...temp,
            NodeTaskStatusList: temp?.NodeTaskStatusList?.length ? temp?.NodeTaskStatusList : [],
          };
        }
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 创建巡检任务
 * @param params
 * @returns
 */
export const createArchScanTaskHandle = async (params: {
  env: string;
  apiParams: ICreateArchScanTaskParams;
  uin?: string;
}): Promise<ICreateArchScanTaskResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await createArchScanTask(apiParams);
      if (!result?.data.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await createArchScanTaskAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 停止巡检任务
 * @param params
 * @returns
 */
export const stopArchScanHandle = async (params: {
  env: string;
  apiParams: IStopArchScanParams;
  uin?: string;
}): Promise<ICreateArchScanTaskResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await stopArchScan(apiParams);
      if (!result?.data.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await stopArchScanAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 发起架构图同步任务接口
 * @param params
 * @returns
 */
export const createArchInfoSyncTaskHandle = async (params: {
  env: string;
  apiParams: ICreateArchInfoSyncTaskParams;
  uin?: string;
}): Promise<ICreateArchInfoSyncTaskResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await createArchInfoSyncTask(apiParams);
      if (!result?.data.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await createArchInfoSyncTaskAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 查询架构图同步进度
 * @param params
 * @returns
 */
export const describeArchSyncTaskProgressHandle = async (params: {
  env: string;
  apiParams: IDescribeArchSyncTaskProgressParams;
  uin?: string;
}): Promise<IDescribeArchSyncTaskProgressResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchSyncTaskProgress(apiParams);
      if (!result?.data.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchSyncTaskProgressAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 停止架构图同步
 * @param params
 * @returns
 */
export const stopArchSyncTaskHandle = async (params: {
  env: string;
  apiParams: IStopArchSyncTaskParams;
  uin?: string;
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await stopArchSyncTask(apiParams);
      if (!result?.data.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await stopArchSyncTaskAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 获取架构图角标及顶栏数据
 * @param params
 * @returns
 */
export const describeArchScanRiskInfoHandle = async (params: {
  env: string;
  apiParams: IDescribeArchScanRiskInfoParams;
  uin?: string;
}): Promise<IDescribeArchScanRiskInfoResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchScanRiskInfo(apiParams);
      if (!result?.data.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchScanRiskInfoAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 *  查询标签键列表
 * @param params
 * @returns
 */
export const getTagKeysHandle = async (params: {
  env: string;
  apiParams: IGetTagKeysParams;
  uin?: string;
}): Promise<IGetTagKeysResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await getTagKeys(apiParams);
      if (!result?.data.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await getTagKeysAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 *  查询标签值
 * @param params
 * @returns
 */
export const getTagValuesHandle = async (params: {
  env: string;
  apiParams: IGetTagValuesParams;
  uin?: string;
}): Promise<any | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await getTagValues(apiParams);
      if (!result?.data.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await getTagValuesAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const updateInstanceToIgnoredStatusHandle = async (params: {
  env: string;
  apiParams: IUpdateInstanceToIgnoredStatusParams;
  uin?: string;
}): Promise<any | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await updateInstanceToIgnoredStatus(apiParams);
      if (!result?.data.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await updateInstanceToIgnoredStatusAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result.Response.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};
