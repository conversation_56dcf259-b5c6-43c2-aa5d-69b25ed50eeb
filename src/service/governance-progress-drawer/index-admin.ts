/* eslint-disable import/prefer-default-export */
/* eslint-disable max-len */
import { post } from '@src/utils/request-admin';
import { ResponseResult } from '@src/utils/request.type';
import {
  ICreateSubscriptionEmailV2Params,
  IDescribeRiskInstancesInMapParams,
  IDescribeRiskInstancesInMapResult,
  IUpdateInstanceToClaimedStatusInMapParams,
  IUpdateInstanceToIgnoredStatusInMapParams,
  IUpdateSubscriptionEmailV2Params,
  IAdminDescribeRiskManageSubjectListParams,
  IAdminDescribeRiskManageSubjectListResponse,
  IAdminDescribeRiskManageInstanceListParams,
  IAdminDescribeRiskManageInstanceListResponse,
  IAdminCreateRiskManageInstanceParams,
  IAdminUpdateRiskManageSubjectParams,
  IAdminUpdateRiskManageSubjectResponse,
  IAdminDeleteRiskManageSubjectParams,
  IAdminDescribeInsightMessageParams,
  IAdminDescribeInsightMessageResponse,
  IAdminDescribeArchSvgDataParams,
  IAdminDescribeArchSvgDataResponse,
  IDescribeInsightGovernTopicParams,
  IDescribeInsightGovernTopicResponse,
  IDescribeArchCSCRiskInfosParams,
  IDescribeArchCSCRiskInfosResponse,
} from './index.admin.type';

const commonPath = '/1/arch';

/**
 * @description 查询架构图下存在风险的实例
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeStrategies
 * <AUTHOR>
export const describeRiskInstancesInMapAdmin = (params: IDescribeRiskInstancesInMapParams): Promise<ResponseResult<IDescribeRiskInstancesInMapResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeRiskInstancesInMap' },
  { enable: false },
  { 'X-TC-Action': 'DescribeRiskInstancesInMap' },
);

/**
 * @description 创建报告订阅邮箱
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeStrategies
 * <AUTHOR>
export const createSubscriptionEmailV2Admin = (params: ICreateSubscriptionEmailV2Params): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...params, Action: 'CreateSubscriptionEmailV2' },
  { enable: false },
  { 'X-TC-Action': 'CreateSubscriptionEmailV2' },
);

/**
 * @description 更新报告订阅邮箱
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeStrategies
 * <AUTHOR>
export const updateSubscriptionEmailV2Admin = (params: IUpdateSubscriptionEmailV2Params): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...params, Action: 'UpdateSubscriptionEmailV2' },
  { enable: false },
  { 'X-TC-Action': 'UpdateSubscriptionEmailV2' },
);

/**
 * @description 架构图维度实例变成已认领状态
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeStrategies
 * <AUTHOR>
export const updateInstanceToClaimedStatusInMapAdmin = (params: IUpdateInstanceToClaimedStatusInMapParams): Promise<ResponseResult<IDescribeRiskInstancesInMapResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'UpdateInstanceToClaimedStatusInMap' },
  { enable: false },
  { 'X-TC-Action': 'UpdateInstanceToClaimedStatusInMap' },
);

/** updateInstanceToClaimedStatusInMapAdmin
 * @description 架构图维度实例修改成已忽略状态
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeStrategies
 * <AUTHOR>
export const updateInstanceToIgnoredStatusInMapAdmin = (params: IUpdateInstanceToIgnoredStatusInMapParams): Promise<ResponseResult<IDescribeRiskInstancesInMapResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'UpdateInstanceToIgnoredStatusInMap' },
  { enable: false },
  { 'X-TC-Action': 'UpdateInstanceToIgnoredStatusInMap' },
);

/** updateInstanceToClaimedStatusInMapAdmin
 * @description 架构图维度实例修改成已忽略状态
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=DescribeStrategies
 * <AUTHOR>
export const describeRiskManageSubjectListAdmin = (params: IAdminDescribeRiskManageSubjectListParams): Promise<ResponseResult<IAdminDescribeRiskManageSubjectListResponse>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeRiskManageSubjectList' },
  { enable: false },
  { 'X-TC-Action': 'DescribeRiskManageSubjectList' },
);

/**
 * @description 分页查询治理主题风险实例
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskManageInstanceList
 * <AUTHOR>
export const describeRiskManageInstanceListAdmin = (params: IAdminDescribeRiskManageInstanceListParams): Promise<ResponseResult<IAdminDescribeRiskManageInstanceListResponse>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeRiskManageInstanceList' },
  { enable: false },
  { 'X-TC-Action': 'DescribeRiskManageInstanceList' },
);

/**
 * @description 保存巡检风险治理实例信息
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=CreateRiskManageInstance
 * <AUTHOR>
export const createRiskManageInstanceAdmin = (params: IAdminCreateRiskManageInstanceParams): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...params, Action: 'CreateRiskManageInstance' },
  { enable: false },
  { 'X-TC-Action': 'CreateRiskManageInstance' },
);

/**
 * @description 保存巡检风险治理信息
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=UpdateRiskManageSubject
 * <AUTHOR>
export const updateRiskManageSubjectAdmin = (params: IAdminUpdateRiskManageSubjectParams): Promise<ResponseResult<IAdminUpdateRiskManageSubjectResponse>> => post(
  `${commonPath}`,
  { ...params, Action: 'UpdateRiskManageSubject' },
  { enable: false },
  { 'X-TC-Action': 'UpdateRiskManageSubject' },
);

/**
 * @description 删除巡检风险治理信息
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DeleteRiskManageSubject
 * <AUTHOR>
export const deleteRiskManageSubjectAdmin = (params: IAdminDeleteRiskManageSubjectParams): Promise<ResponseResult<unknown>> => post(
  `${commonPath}`,
  { ...params, Action: 'DeleteRiskManageSubject' },
  { enable: false },
  { 'X-TC-Action': 'DeleteRiskManageSubject' },
);

/**
 * @description 获取洞察对话消息内容
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_pre_release&version=2020-07-21&action=DescribeInsightMessage
 * <AUTHOR>
export const describeInsightMessageAdmin = (params: IAdminDescribeInsightMessageParams): Promise<ResponseResult<IAdminDescribeInsightMessageResponse>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeInsightMessage' },
  { enable: false },
  { 'X-TC-Action': 'DescribeInsightMessage' },
);

/**
 * @description 查询架构图SVG数据
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchSvgData
 * <AUTHOR>
export const describeArchSvgDataAdmin = (params: IAdminDescribeArchSvgDataParams): Promise<ResponseResult<IAdminDescribeArchSvgDataResponse>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchSvgData' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchSvgData' },
);

/**
 * @description 获取洞察风险治理主题内容
 * @url https://capi.woa.com/apidoc?product=advisor&version=2020-07-21&action=DescribeInsightGovernTopic
 * <AUTHOR>
export const describeInsightGovernTopicAdmin = (params: IDescribeInsightGovernTopicParams): Promise<ResponseResult<IDescribeInsightGovernTopicResponse>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeInsightGovernTopic' },
  { enable: false },
  { 'X-TC-Action': 'DescribeInsightGovernTopic' },
);

/**
 * @description 查询架构图SVG数据
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchSvgData
 * <AUTHOR>
export const describeArchCSCRiskInfosAdmin = (params: IDescribeArchCSCRiskInfosParams): Promise<ResponseResult<IDescribeArchCSCRiskInfosResponse>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchCSCRiskInfos' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchCSCRiskInfos' },
);
