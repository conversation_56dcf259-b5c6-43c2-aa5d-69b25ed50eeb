export type CommonAdminParams = {
  AppId: number;
  Language?: string;
  Uin?: string;
  SubAccountUin?: string;
};

export interface ICreateArchScanReportFileParams {
  AppId: number;
  Language: string;
  /** 架构图uuid */
  CloudMapUuid?: string;
  /** id */
  Id?: number;
  /** type */
  Type?: string;
  /** 巡检任务Id */
  TaskId?: string;
  /** 报告巡检类型 */
  Env?: string;
  /** 产品 */
  Products?: string;
  /** 巡检项类型 */
  GroupIds?: number;
  /** 巡检项Id */
  StrategyId?: string;
  /** 巡检项Ids */
  StrategyIds?: number;
  /** 标签 */
  Tags?: {Key: string, Value: string}[]; // 需要根据实际 Tag 类型定义替换
  /** 巡检类型 */
  TaskType?: string;
  /** 架构图Id */
  CloudMapId?: string;
  /** 报告版本 (默认v1) */
  ReportVersion?: 'v1' | string; // 可扩展的版本类型
  /** 报告插件名称 (默认abc) */
  SdkName?: 'abc' | string; // 可扩展的SDK名称
}
export interface ICreateArchScanReportFileResult {
  IsFinish: boolean;
  Message: string;
  FinishTime: string;
  LatestScanType: number;
  CostTime: string;
  BindInsCount: number;
  NodeTaskStatusList: {NodeUuid: string; IsFinish: boolean}[];
  Progress: {ScannedCount: number; TotalCount: number};
}

export interface IDescribeArchScanReportTaskStatusParams {
  AppId: number;
  Language: string;
  ResultId: string;
}

export interface IDescribeArchScanReportTaskStatusResult {
  TaskStatus: string;
  CostTime: string;
}

export interface IDescribeDownloadTaskParams {
  ResultId: string;
  AppId: number;
  Language: string;
}

export interface IDescribeDownloadTaskResult {
  CosUrl: string;
  TaskStatus: string;
  CosUrlPdf: string;
}

export interface IUpdateArchScanReportArchiveInfoParams {
  ArchIds: string[];
  ResultIds: string[];
  ReportVersion: string;
  SdkName: string;
}

export interface IUpdateArchScanReportArchiveInfoResult {
  [key: string]: any
}

export interface IDescribeArchStrategyListParams {
  ArchId: string;
  ResultId: string;
  AppId: number;
  Language: string;
}

export interface IDescribeArchStrategyListResult {
  CosUrl: string;
  ArchStrategyList: {
    GroupName: string;
    StrategyName: string;
    ProductName: string;
    Level: string;
    Condition?: string;
    Notice: string;
    Repair: string;
  }[]
}

export interface IDescribeArchRiskTrendInfoParams {
  ArchId: string;
  ResultId: string;
  AppId: number;
  Language: string;
}

export interface IDescribeArchRiskTrendInfoResult {
  [key: string]: any
}

export interface IDescribeArchScanOverviewInfoParams {
  ArchId: string;
  ResultId: string;
  AppId: number;
  Language: string;
}

interface ArchScanChartInfo {
  Name: string; // 名称
  ChartInfoSet: {
    TitleName: string; // 图表名称
    ChartType: string; // 图表类型，Pie表示饼图，Pistogram表示柱形图，Line表示折线图，Bar表示长条图
    DisplayStatus: number; // 图表展示状态
    ChartDataInfoSet: {
      DataName: string; // 数据名称
      KeyValueSet: {
        Key: string;
        KeyCNName: string;
        Value: number;
      }
    }; // 图表数据
  }; // 图表信息
  ExtraInfo: string; // 图表说明信息
}
export interface IDescribeArchScanOverviewInfoResult {
  CustomerName: string; // xxx
  ArchId: string; // arch-xx
  ArchName: string; // xxx
  AppId: number; // xxx
  ResultId: string; // xxx
  ReportArchiveStatus: boolean; // false (未归档为 false，已归档为 true)
  FinishTime: string; // 2006-01-02 15:04:05
  ArchScanSourceInfo: {
    ScanStrategyCount: number; // 已扫描的巡检项
    ScanResourceCount: number; // 扫描的架构图资源数量
    ScanResourcePercent: string; // 风险扫描的资源数占整体资源数的比例
    UnScanResourceCount: number; // 架构图中未被巡检的资源数
    UnBindResourceCount: number; // 未绑定的资源数
    ScanProductCount: number; // 巡检产品数
  };
  CurrentStrategySummaryInfo: {
    ScanCount: string; // 已扫风险项
    HighRiskCount: string; // 高风险项
    MediumRiskCount: string; // 中风险项
    NoRiskCount: string; // 健康项
  };
  LastWeekStrategySummaryInfo: {
    ScanCount: string; // 已扫风险项
    HighRiskCount: string; // 高风险项
    MediumRiskCount: string; // 中风险项
    NoRiskCount: string; // 健康项
  };
  ArchScanScoreTrendInfo: ArchScanChartInfo;
  CurrentScanScore: number; // 80
  ArchScanGroupScoreInfo: ArchScanChartInfo;
  ReportDate: string;
}

export interface IStopArchScanParams {
  ArchId: string;
  AppId?: number;
  Language?: string;
  Uin?: string;
  SubAccountUin?: string;
}
export interface IStopArchScanResult {
  [key: string]: any
}

export interface IDescribeArchitectureNodeRiskInfoV3Params {
  MapId: string;
}

interface NodeRiskCount {
  NodeUuid: string; // 节点id，跟describeArch接口返回的NodeId对应
  HighRiskCount: number; // 高风险项数量
  MidRiskCount: number; // 中风险项数量
  LastSuccessTaskId: string; // 节点最后一次巡检成功任务id
  TaskId: string; // 当前巡检任务id
  FinishTime: string; // 巡检完成时间
  IsIgnore: boolean; // 节点是否被忽略
}

export interface IDescribeArchitectureNodeRiskInfoV3Result {
  NodeRiskCountList: NodeRiskCount[]; // 节点风险数量信息
  LastSuccessTaskId: string; // 最近一次成功的任务id, 二期废除
  TaskId: string; // 当前任务id, 二期废除
  IsFinish: boolean; // 当前巡检是否完成, 二期废除
  Hash: boolean; // 是否巡检过, 二期废除
  FinishTime: string; // 最近一次完成巡检的任务完成时间, 二期废除
}

// ====> 云巡检插件查询节点报告结果
export interface IDescribeArchScanNodeReportResultParams {
  MapId: string;
  NodeUuid: string;
  TaskId: string;
}
interface ArchNodeReportRiskInstanceItem {
  InstanceId: string; // 实例id
  StrategyName: string; // 风险项名称
  RiskLevel: number; // 风险等级
  RiskLevelName: string; // 风险等级名称
  RiskType: number; // 类别
  RiskTypeName: string; // 类别名称
  AlertConditions: string[]; // 告警条件
}
export interface IDescribeArchScanNodeReportResultResult {
  ReportName: string; // 报告标题
  MapName: string; // 架构图名称
  NodeName: string; // 节点名称
  MapId: string; // 架构图 ID
  AppId: number; // 应用ID
  ScanAt: string; // 评估时间
  RiskInstanceList: ArchNodeReportRiskInstanceItem[]; // 节点巡检明细清单
  TaskId: string; // 任务报告 ID，用于导出
  CustomerName: string; // 客户名称
}
// <====

// ====> 巡检插件导出节点报告结果
export interface IExportArchScanNodeReportResultParams {
  MapId: string;
  NodeUuid: string;
  TaskId: string;
}
export interface IExportArchScanNodeReportResultResult {
  FileUrl: string;
}
// <====
