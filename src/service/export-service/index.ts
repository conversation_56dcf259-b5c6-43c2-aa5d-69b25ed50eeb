/* eslint-disable max-len */
/* eslint-disable import/prefer-default-export */
import { createServiceFunc, ResultCommonType } from '@src/utils/request';

import {
  ICreateArchScanReportFileParams,
  ICreateArchScanReportFileResult,
  IDescribeArchScanReportTaskStatusParams,
  IDescribeArchScanReportTaskStatusResult,
  IDescribeDownloadTaskParams,
  IDescribeDownloadTaskResult,
  IUpdateArchScanReportArchiveInfoParams,
  IUpdateArchScanReportArchiveInfoResult,
  IDescribeArchStrategyListParams,
  IDescribeArchStrategyListResult,
  IDescribeArchRiskTrendInfoParams,
  IDescribeArchRiskTrendInfoResult,
  IDescribeArchScanOverviewInfoParams,
  IDescribeArchScanOverviewInfoResult,
  IStopArchScanParams,
  IStopArchScanResult,
  IDescribeArchitectureNodeRiskInfoV3Params,
  IDescribeArchitectureNodeRiskInfoV3Result,
  IDescribeArchScanNodeReportResultParams,
  IDescribeArchScanNodeReportResultResult,
  IExportArchScanNodeReportResultParams,
  IExportArchScanNodeReportResultResult,
} from './index.type';

/**
 * @description 生成架构巡检报告
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=CreateArchScanReportFile
 * <AUTHOR>
export const createArchScanReportFile: (params: ICreateArchScanReportFileParams) =>
Promise<ResultCommonType<ICreateArchScanReportFileResult>> = createServiceFunc('CreateArchScanReportFile');

/**
 * @description 查询架构图生成巡检报告任务状态
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanReportTaskStatus
 * <AUTHOR>
export const describeArchScanReportTaskStatus: (params: IDescribeArchScanReportTaskStatusParams) =>
Promise<ResultCommonType<IDescribeArchScanReportTaskStatusResult>> = createServiceFunc('DescribeArchScanReportTaskStatus');

/**
 * @description 获取扫描报告下载链接
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeDownloadTask
 * <AUTHOR>
export const describeDownloadTask: (params: IDescribeDownloadTaskParams) =>
Promise<ResultCommonType<IDescribeDownloadTaskResult>> = createServiceFunc('DescribeDownloadTask');

/**
 * @description 更新架构图巡检报告归档信息
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=UpdateArchScanReportArchiveInfo
 * <AUTHOR>
export const updateArchScanReportArchiveInfo: (params: IUpdateArchScanReportArchiveInfoParams) =>
Promise<ResultCommonType<IUpdateArchScanReportArchiveInfoResult>> = createServiceFunc('UpdateArchScanReportArchiveInfo');

/**
 * @description 查询架构图巡检项信息
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchStrategyList
 * <AUTHOR>
export const describeArchStrategyList: (params: IDescribeArchStrategyListParams) =>
Promise<ResultCommonType<IDescribeArchStrategyListResult>> = createServiceFunc('DescribeArchStrategyList');

/**
 * @description
 * @url
 * <AUTHOR>
export const describeArchRiskTrendInfo: (params: IDescribeArchRiskTrendInfoParams) =>
Promise<ResultCommonType<IDescribeArchRiskTrendInfoResult>> = createServiceFunc('DescribeArchRiskTrendInfo');

/**
 * @description 查询架构图云巡检在线报告概览信息
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanOverviewInfo
 * <AUTHOR>
export const describeArchScanOverviewInfo: (params: IDescribeArchScanOverviewInfoParams) =>
Promise<ResultCommonType<IDescribeArchScanOverviewInfoResult>> = createServiceFunc('DescribeArchScanOverviewInfo');

/**
 * @description 停止架构巡检任务
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=StopArchScan
 * <AUTHOR>
export const stopArchScan: (params: IStopArchScanParams) =>
Promise<ResultCommonType<IStopArchScanResult>> = createServiceFunc('StopArchScan');

/**
 * @description 查询节点风险信息V3
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchitectureNodeRiskInfoV3
 * <AUTHOR>
export const describeArchitectureNodeRiskInfoV3: (params: IDescribeArchitectureNodeRiskInfoV3Params) =>
Promise<ResultCommonType<IDescribeArchitectureNodeRiskInfoV3Result>> = createServiceFunc('DescribeArchitectureNodeRiskInfoV3');

/**
 * @description 云巡检插件查询节点报告结果
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanNodeReportResult
 * <AUTHOR>
export const describeArchScanNodeReportResult: (params: IDescribeArchScanNodeReportResultParams) =>
Promise<ResultCommonType<IDescribeArchScanNodeReportResultResult>> = createServiceFunc('DescribeArchScanNodeReportResult');

/**
 * @description 巡检插件导出节点报告结果
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=ExportArchScanNodeReportResult
 * <AUTHOR>
export const exportArchScanNodeReportResult: (params: IExportArchScanNodeReportResultParams) =>
Promise<ResultCommonType<IExportArchScanNodeReportResultResult>> = createServiceFunc('ExportArchScanNodeReportResult');
