/* eslint-disable import/prefer-default-export */
/* eslint-disable max-len */
import { post } from '@src/utils/request-admin';
import { ResponseResult } from '@src/utils/request.type';
import {
  CommonAdminParams,
  ICreateArchScanReportFileParams,
  ICreateArchScanReportFileResult,
  IDescribeArchScanReportTaskStatusParams,
  IDescribeArchScanReportTaskStatusResult,
  IDescribeDownloadTaskParams,
  IDescribeDownloadTaskResult,
  IUpdateArchScanReportArchiveInfoParams,
  IUpdateArchScanReportArchiveInfoResult,
  IDescribeArchStrategyListParams,
  IDescribeArchStrategyListResult,
  IDescribeArchRiskTrendInfoParams,
  IDescribeArchRiskTrendInfoResult,
  IDescribeArchScanOverviewInfoParams,
  IDescribeArchScanOverviewInfoResult,
  IStopArchScanParams,
  IStopArchScanResult,
  IDescribeArchitectureNodeRiskInfoV3Params,
  IDescribeArchitectureNodeRiskInfoV3Result,
  IDescribeArchScanNodeReportResultParams,
  IDescribeArchScanNodeReportResultResult,
  IExportArchScanNodeReportResultParams,
  IExportArchScanNodeReportResultResult,
} from './index-admin.type';

const commonPath = '/1/arch';
const qcloudPath = '/1/qcloud';

/**
 * @description 生成架构巡检报告
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_formal&version=2020-07-21&action=CreateArchScanReportFile
 * <AUTHOR>
export const createArchScanReportFile = (params: ICreateArchScanReportFileParams & CommonAdminParams): Promise<ResponseResult<ICreateArchScanReportFileResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'CreateArchScanReportFile' },
  { enable: false },
  { 'X-TC-Action': 'CreateArchScanReportFile' },
);

/**
 * @description 查询架构图生成巡检报告任务状态
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanReportTaskStatus
 * <AUTHOR>
export const describeArchScanReportTaskStatus = (params: IDescribeArchScanReportTaskStatusParams & CommonAdminParams): Promise<ResponseResult<IDescribeArchScanReportTaskStatusResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchScanReportTaskStatus' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchScanReportTaskStatus' },
);

/**
 * @description 获取扫描报告下载链接
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeDownloadTask
 * <AUTHOR>
export const describeDownloadTask = (params: IDescribeDownloadTaskParams & CommonAdminParams): Promise<ResponseResult<IDescribeDownloadTaskResult>> => post(
  `${qcloudPath}`,
  { ...params, Action: 'DescribeDownloadTask' },
  { enable: false },
  { 'X-TC-Action': 'DescribeDownloadTask' },
);

/**
 * @description 更新架构图巡检报告归档信息
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=UpdateArchScanReportArchiveInfo
 * <AUTHOR>
export const updateArchScanReportArchiveInfo = (params: IUpdateArchScanReportArchiveInfoParams & CommonAdminParams): Promise<ResponseResult<IUpdateArchScanReportArchiveInfoResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'UpdateArchScanReportArchiveInfo' },
  { enable: false },
  { 'X-TC-Action': 'UpdateArchScanReportArchiveInfo' },
);

/**
 * @description 查询架构图巡检项信息
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchStrategyList
 * <AUTHOR>
export const describeArchStrategyList = (params: IDescribeArchStrategyListParams & CommonAdminParams): Promise<ResponseResult<IDescribeArchStrategyListResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchStrategyList' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchStrategyList' },
);

/**
 * @description
 * @url
 * <AUTHOR>
export const describeArchRiskTrendInfo = (params: IDescribeArchRiskTrendInfoParams & CommonAdminParams): Promise<ResponseResult<IDescribeArchRiskTrendInfoResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchRiskTrendInfo' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchRiskTrendInfo' },
);

/**
 * @description 查询架构图云巡检在线报告概览信息
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanOverviewInfo
 * <AUTHOR>
export const describeArchScanOverviewInfo = (params: IDescribeArchScanOverviewInfoParams & CommonAdminParams): Promise<ResponseResult<IDescribeArchScanOverviewInfoResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchScanOverviewInfo' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchScanOverviewInfo' },
);

/**
 * @description 停止架构巡检任务
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=StopArchScan
 * <AUTHOR>
export const stopArchScan = (params: IStopArchScanParams & CommonAdminParams): Promise<ResponseResult<IStopArchScanResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'StopArchScan' },
  { enable: false },
  { 'X-TC-Action': 'StopArchScan' },
);

/**
 * @description 查询节点风险信息V3
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchitectureNodeRiskInfoV3
 * <AUTHOR>
export const describeArchitectureNodeRiskInfoV3 = (params: IDescribeArchitectureNodeRiskInfoV3Params & CommonAdminParams): Promise<ResponseResult<IDescribeArchitectureNodeRiskInfoV3Result>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchitectureNodeRiskInfoV3' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchitectureNodeRiskInfoV3' },
);

/**
 * @description 云巡检插件查询节点报告结果
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchScanNodeReportResult
 * <AUTHOR>
export const describeArchScanNodeReportResult = (params: IDescribeArchScanNodeReportResultParams & CommonAdminParams): Promise<ResponseResult<IDescribeArchScanNodeReportResultResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'DescribeArchScanNodeReportResult' },
  { enable: false },
  { 'X-TC-Action': 'DescribeArchScanNodeReportResult' },
);

/**
 * @description 巡检插件导出节点报告结果
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=ExportArchScanNodeReportResult
 * <AUTHOR>
export const exportArchScanNodeReportResult = (params: IExportArchScanNodeReportResultParams & CommonAdminParams): Promise<ResponseResult<IExportArchScanNodeReportResultResult>> => post(
  `${commonPath}`,
  { ...params, Action: 'ExportArchScanNodeReportResult' },
  { enable: false },
  { 'X-TC-Action': 'ExportArchScanNodeReportResult' },
);
