/* eslint-disable max-len */
/* eslint-disable import/prefer-default-export */

import { getAppid, getLanguageParam } from '@src/utils/index';
import { Env } from '@src/constant';
import {
  ICreateArchScanReportFileResult,
  ICreateArchScanReportFileParams,
  IDescribeArchScanReportTaskStatusParams,
  IDescribeArchScanReportTaskStatusResult,
  IDescribeDownloadTaskParams,
  IDescribeDownloadTaskResult,
  IUpdateArchScanReportArchiveInfoParams,
  IUpdateArchScanReportArchiveInfoResult,
  IDescribeArchStrategyListParams,
  IDescribeArchStrategyListResult,
  IDescribeArchRiskTrendInfoParams,
  IDescribeArchRiskTrendInfoResult,
  IDescribeArchScanOverviewInfoParams,
  IDescribeArchScanOverviewInfoResult,
  IStopArchScanParams,
  IStopArchScanResult,
  IDescribeArchitectureNodeRiskInfoV3Params,
  IDescribeArchitectureNodeRiskInfoV3Result,
  IDescribeArchScanNodeReportResultParams,
  IDescribeArchScanNodeReportResultResult,
  IExportArchScanNodeReportResultParams,
  IExportArchScanNodeReportResultResult,
} from './index.type';
import {
  ICreateArchScanReportFileParams as ICreateArchScanReportFileParamsAdmin,
  IDescribeArchScanReportTaskStatusParams as IDescribeArchScanReportTaskStatusParamsAdmin,
  IDescribeDownloadTaskParams as IDescribeDownloadTaskParamsAdmin,
  IUpdateArchScanReportArchiveInfoParams as IUpdateArchScanReportArchiveInfoParamsAdmin,
  IDescribeArchStrategyListParams as IDescribeArchStrategyListParamsAdmin,
  IDescribeArchRiskTrendInfoParams as IDescribeArchRiskTrendInfoParamsAdmin,
  IDescribeArchScanOverviewInfoParams as IDescribeArchScanOverviewInfoParamsAdmin,
  IStopArchScanParams as IStopArchScanParamsAdmin,
  IDescribeArchitectureNodeRiskInfoV3Params as IDescribeArchitectureNodeRiskInfoV3ParamsAdmin,
  IDescribeArchScanNodeReportResultParams as IDescribeArchScanNodeReportResultParamsAdmin,
  IExportArchScanNodeReportResultParams as IExportArchScanNodeReportResultParamsAdmin,
} from './index-admin.type';

import {
  createArchScanReportFile as createArchScanReportFileOrigin,
  describeArchScanReportTaskStatus as describeArchScanReportTaskStatusOrigin,
  describeDownloadTask as describeDownloadTaskOrigin,
  updateArchScanReportArchiveInfo as updateArchScanReportArchiveInfoOrigin,
  describeArchStrategyList as describeArchStrategyListOrigin,
  describeArchRiskTrendInfo as describeArchRiskTrendInfoOrigin,
  describeArchScanOverviewInfo as describeArchScanOverviewInfoOrigin,
  stopArchScan as stopArchScanOrigin,
  describeArchitectureNodeRiskInfoV3 as describeArchitectureNodeRiskInfoV3Origin,
  describeArchScanNodeReportResult as describeArchScanNodeReportResultOrigin,
  exportArchScanNodeReportResult as exportArchScanNodeReportResultOrigin,
} from './index';
import {
  createArchScanReportFile as createArchScanReportFileAdmin,
  describeArchScanReportTaskStatus as describeArchScanReportTaskStatusAdmin,
  describeDownloadTask as describeDownloadTaskAdmin,
  updateArchScanReportArchiveInfo as updateArchScanReportArchiveInfoAdmin,
  describeArchStrategyList as describeArchStrategyListAdmin,
  describeArchRiskTrendInfo as describeArchRiskTrendInfoAdmin,
  describeArchScanOverviewInfo as describeArchScanOverviewInfoAdmin,
  stopArchScan as stopArchScanAdmin,
  describeArchitectureNodeRiskInfoV3 as describeArchitectureNodeRiskInfoV3Admin,
  describeArchScanNodeReportResult as describeArchScanNodeReportResultAdmin,
  exportArchScanNodeReportResult as exportArchScanNodeReportResultAdmin,
} from './index-admin';

type InferParamsType<T extends Env, K, J> = T extends 'CONSOLE'
  ? K
  : J;

/**
 * 生成架构巡检报告
 * @param params
 * @returns
 */
export const createArchScanReportFile = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, ICreateArchScanReportFileParams['data'], ICreateArchScanReportFileParamsAdmin>;
}): Promise<ICreateArchScanReportFileResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await createArchScanReportFileOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await createArchScanReportFileAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Language: getLanguageParam(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 查询架构图生成巡检报告任务状态
 * @param params
 * @returns
 */
export const fetchArchScanReportTaskStatus = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeArchScanReportTaskStatusParams['data'], IDescribeArchScanReportTaskStatusParamsAdmin>;
}): Promise<IDescribeArchScanReportTaskStatusResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchScanReportTaskStatusOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchScanReportTaskStatusAdmin({
        // ArchId: archId,
        Uin: uin,
        SubAccountUin: uin,
        AppId: getAppid(),
        Language: getLanguageParam(),
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 获取扫描报告下载链接
 * @param params
 * @returns
 */
export const describeDownloadTask = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeDownloadTaskParams['data'], IDescribeDownloadTaskParamsAdmin>;
}): Promise<IDescribeDownloadTaskResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeDownloadTaskOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeDownloadTaskAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Language: getLanguageParam(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 更新架构图巡检报告归档信息
 * @param params
 * @returns
 */
export const updateArchScanReportArchiveInfo = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IUpdateArchScanReportArchiveInfoParams['data'], IUpdateArchScanReportArchiveInfoParamsAdmin>;
}): Promise<IUpdateArchScanReportArchiveInfoResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await updateArchScanReportArchiveInfoOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await updateArchScanReportArchiveInfoAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 架构巡检项信息
 * @param params
 * @returns
 */
export const describeArchStrategyList = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeArchStrategyListParams['data'], IDescribeArchStrategyListParamsAdmin>;
}): Promise<IDescribeArchStrategyListResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchStrategyListOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchStrategyListAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Language: getLanguageParam(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 *
 * @param params
 * @returns
 */
export const describeArchRiskTrendInfo = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeArchRiskTrendInfoParams['data'], IDescribeArchRiskTrendInfoParamsAdmin>;
}): Promise<IDescribeArchRiskTrendInfoResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchRiskTrendInfoOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchRiskTrendInfoAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Language: getLanguageParam(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 架构图巡检结果图表信息
 * @param params
 * @returns
 */
export const describeArchScanOverviewInfo = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeArchScanOverviewInfoParams['data'], IDescribeArchScanOverviewInfoParamsAdmin>;
}): Promise<IDescribeArchScanOverviewInfoResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchScanOverviewInfoOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchScanOverviewInfoAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Language: getLanguageParam(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 停止架构巡检任务
 * @param params
 * @returns
 */
export const stopArchScan = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IStopArchScanParams['data'], IStopArchScanParamsAdmin>;
}): Promise<IStopArchScanResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await stopArchScanOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await stopArchScanAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 查询节点风险信息V3
 * @param params
 * @returns
 */
export const describeArchitectureNodeRiskInfoV3 = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeArchitectureNodeRiskInfoV3Params['data'], IDescribeArchitectureNodeRiskInfoV3ParamsAdmin>;
}): Promise<IDescribeArchitectureNodeRiskInfoV3Result | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchitectureNodeRiskInfoV3Origin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchitectureNodeRiskInfoV3Admin({
        // ArchId: archId,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 云巡检插件查询节点报告结果
 * @param params
 * @returns
 */
export const describeArchScanNodeReportResult = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IDescribeArchScanNodeReportResultParams['data'], IDescribeArchScanNodeReportResultParamsAdmin>;
}): Promise<IDescribeArchScanNodeReportResultResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchScanNodeReportResultOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchScanNodeReportResultAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * 巡检插件导出节点报告结果
 * @param params
 * @returns
 */
export const exportArchScanNodeReportResult = async <E extends Env>(params: {
  env: E;
  // archId: string;
  uin?: string;
  data: InferParamsType<E, IExportArchScanNodeReportResultParams['data'], IExportArchScanNodeReportResultParamsAdmin>;
}): Promise<IExportArchScanNodeReportResultResult | undefined> => {
  try {
    const {
      // archId,
      env,
      uin,
      data,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await exportArchScanNodeReportResultOrigin({
        data: {
          ...data,
        },
      });
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await exportArchScanNodeReportResultAdmin({
        // ArchId: archId,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        ...data,
      });
      if (result?.Response) {
        rs = result?.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};
