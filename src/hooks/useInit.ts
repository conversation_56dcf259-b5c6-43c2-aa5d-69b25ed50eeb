import { useEffect } from 'react';
import { changeGlobalData, useGlobalSelector } from '@src/store/global/index';
import { useDispatch } from 'react-redux';
import {
  describeArchScanRiskInfoHandle,
  describeArchSyncTaskProgressHandle,
  createArchInfoSyncTaskHandle,
} from '@src/service/common-service/final-request';
import { InspectionTaskStatusEnum, IDriftDetectionTaskEnum } from '@src/constant';
import _ from 'lodash';
import { setInitTaskIsStop, initTaskIsStop } from '@src/utils/caching';
import useUpdateShapesBar from './useUpdateShapesBar';
import useInspectionTask from './useInspectionTask';
import useNodeInspectionTask from './useNodeInspectionTask';

interface IuseInitProps {
  pluginAPI: AppPluginAPI.PluginAPI;
  createArchScanReportFile?: () => void;
}

/**
 * 插件初始化hook
 * @returns
 */
export default function useInit(props: IuseInitProps) {
  const {
    pluginAPI,
    createArchScanReportFile,
  } = props;
  const dispatch = useDispatch();
  const {
    inspectionTaskStatus, isInitTask, supportTaskProductList, riskType,
  } = useGlobalSelector();
  const { startArchScanTask } = useInspectionTask({
    pluginAPI,
    createArchScanReportFile,
  });
  const { initNodeScanTask } = useNodeInspectionTask({ pluginAPI });
  const { updateShapesBar } = useUpdateShapesBar();

  const startInit = async () => {
    try {
      setInitTaskIsStop(false);
      dispatch(changeGlobalData({
        inspectionTaskStatus: InspectionTaskStatusEnum.notStarted,
        driftDetectionTaskStatus: IDriftDetectionTaskEnum.notStarted,
        inspectionTaskData: {
          IsFinish: false,
          FinishTime: '',
          NodeTaskStatusList: [],
          Progress: {
            ScannedCount: 0,
            TotalCount: 1,
          },
          LatestScanType: 1,
          CostTime: '',
          BindInsCount: 0,
        },
      }));
      pluginAPI.setAsyncTaskStop();
      // 先查询架构图角标及顶栏数据完成插件loading逻辑
      const riskInfoResult = await describeArchScanRiskInfoHandle({
        env: pluginAPI.env,
        apiParams: {
          data: {
            MapId: pluginAPI.archInfo.archId,
            GroupIds: Number(riskType) ? [Number(riskType)] : [],
          },
        },
        uin: pluginAPI.uin,
      });
      if (riskInfoResult) {
        // 存储公共store, useShapesBar hook会监听依赖进行角标和顶栏更新操作
        dispatch(changeGlobalData({
          archScanRiskInfo: riskInfoResult,
        }));
        // 开启巡检任务检测
        startArchScanTask(supportTaskProductList, riskInfoResult.IsSynced, true);
      }
      pluginAPI.initOver();
    } catch (error) {
      pluginAPI.initOver();
    }
  };

  // 防抖包裹后的核心逻辑
  const getArchTaskProgress = _.debounce(() => {
    describeArchSyncTaskProgressHandle({
      env: pluginAPI.env,
      apiParams: {
        data: {
          MapId: pluginAPI.archInfo.archId,
        },
      },
    }).then((rs) => {
      if (rs && rs.TaskProgress < 100 && !initTaskIsStop) {
        dispatch(changeGlobalData({
          driftDetectionTaskStatus: IDriftDetectionTaskEnum.driftDetectioning,
          driftDetectionTaskData: rs,
        }));
        getArchTaskProgress(); // 递归调用依然受防抖控制
      } else if (rs && rs.TaskProgress === 100 && !initTaskIsStop) {
        dispatch(changeGlobalData({
          driftDetectionTaskStatus: IDriftDetectionTaskEnum.driftDetectioned,
          driftDetectionTaskData: rs,
        }));
        initNodeScanTask(supportTaskProductList);
        if (rs.IsChange) {
          // 再次调用角标顶栏接口更新数据
          updateShapesBar();
        }
      }
    });
  }, 2000);

  const startArchInfoSyncTask = async () => {
    try {
      const taskResult = await describeArchSyncTaskProgressHandle({
        env: pluginAPI.env,
        apiParams: {
          data: {
            MapId: pluginAPI.archInfo.archId,
          },
        },
        uin: pluginAPI.uin,
      });
      if (taskResult && taskResult.TaskProgress === 100) {
        // 如果当前没有正常进行中的漂移检测任务
        dispatch(changeGlobalData({
          driftDetectionTaskStatus: IDriftDetectionTaskEnum.driftDetectioning,
          driftDetectionTaskData: undefined,
        }));
        const createRs = await createArchInfoSyncTaskHandle({
          env: pluginAPI.env,
          apiParams: {
            data: {
              MapId: pluginAPI.archInfo.archId,
            },
          },
          uin: pluginAPI.uin,
        });
        if (createRs) {
          getArchTaskProgress();
        }
      } else if (taskResult && taskResult.TaskProgress <= 100) {
        // 当前有正在进行中的漂移检查任务
        getArchTaskProgress();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updateArchSyncTaskProgress = () => {
    describeArchSyncTaskProgressHandle({
      env: pluginAPI.env,
      apiParams: {
        data: {
          MapId: pluginAPI.archInfo.archId,
        },
      },
    }).then((rs) => {
      if (rs && rs.TaskProgress === 100) {
        dispatch(changeGlobalData({
          driftDetectionTaskStatus: IDriftDetectionTaskEnum.driftDetectioned,
          driftDetectionTaskData: rs,
        }));
      }
    });
  };

  useEffect(() => {
    if (inspectionTaskStatus === InspectionTaskStatusEnum.inspectionCompleted && isInitTask) {
      // 当巡检任务完成且流程是初始化流程时。进行漂移检测
      startArchInfoSyncTask();
    }
  }, [inspectionTaskStatus, isInitTask]);

  useEffect(() => {
    if (supportTaskProductList?.length) {
      startInit();
    }
    return () => {
      pluginAPI.setAsyncTaskStop();
      dispatch(changeGlobalData({
        inspectionTaskStatus: InspectionTaskStatusEnum.notStarted,
        driftDetectionTaskStatus: IDriftDetectionTaskEnum.notStarted,
        driftDetectionTaskData: undefined,
      }));
    };
  }, [supportTaskProductList]);

  return {
    updateArchSyncTaskProgress,
  };
}
