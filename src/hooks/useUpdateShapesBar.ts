import { useEffect, useRef } from 'react';
import { changeGlobalData, useGlobalSelector } from '@src/store/global/index';
import { useDispatch } from 'react-redux';
import {
  describeArchScanRiskInfoHandle,
} from '@src/service/common-service/final-request';

/**
 * 更新角标hook
 */
export default function useUpdateShapesBar() {
  const { pluginPropsData, riskType } = useGlobalSelector();
  const dispatch = useDispatch();
  const riskTypeRef = useRef(null);

  const updateShapesBar = async () => {
    const riskInfoResult = await describeArchScanRiskInfoHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          MapId: pluginPropsData.archInfo.archId,
          GroupIds: Number(riskTypeRef.current) ? [Number(riskTypeRef.current)] : [],
        },
      },
      uin: pluginPropsData.uin,
    });
    if (riskInfoResult) {
      // 存储公共store, useShapesBar hook会监听依赖进行角标和顶栏更新操作
      dispatch(changeGlobalData({
        archScanRiskInfo: riskInfoResult,
      }));
    }
  };

  useEffect(() => {
    riskTypeRef.current = riskType;
  }, [riskType]);

  return {
    updateShapesBar,
  };
}
