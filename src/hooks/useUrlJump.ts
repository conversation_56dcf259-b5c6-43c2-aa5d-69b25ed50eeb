import { useEffect } from 'react';
import {
  deleteUrlParam, getUrlParam, splitFirstHyphen, reportEvent,
} from '@src/utils/index';
import { changeNodeRiskDrawerState } from '@src/store/node-risk-drawer/index';
import { changeGlobalData } from '@src/store/global/index';
import { EnvEnum, UrlParamsEnum, RiskProgressEnum } from '@src/constant/index';
import { useDispatch } from 'react-redux';
import { changeGovernanceProgressDrawerState } from '@src/store/governance-progress-drawer';
import { TabEnum } from '@src/components/inspection-drawer';
import { useAppContext } from '@src/context/AppContext';

interface IuseUrlJumpProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

export default function useUrlJump(props: IuseUrlJumpProps) {
  const {
    pluginAPI,
  } = props;
  const dispatch = useDispatch();
  const { chatBiCallBacks } = useAppContext();
  useEffect(() => {
    const instanceId = getUrlParam('instanceId');
    let nodeUuid = getUrlParam('nodeUuid');
    const subjectTopicTitle = getUrlParam('subjectTopicTitle');

    if (!nodeUuid && pluginAPI?.extraParams) {
      const parts = pluginAPI?.extraParams?.split(':');
      nodeUuid = parts?.length >= 2 ? parts[1] : '';
    }
    const {
      nodeList,
    } = pluginAPI?.archInfo || {};
    const node = nodeList?.find((item) => item?.DiagramId === nodeUuid);
    const progressTabName = 'progressTab';
    const claimPersonName = 'claimPerson';
    const progressTab: any = getUrlParam(progressTabName);
    const claimPerson: any = getUrlParam(claimPersonName);
    if (nodeUuid && node) {
      pluginAPI?.setShapeChecked([nodeUuid], true);
      dispatch(changeGlobalData({
        nodeRiskDrawerVisible: true,
        selectNodeInfo: {
          key: nodeUuid,
          name: node?.NodeName,
        },
      }));
      if (instanceId) {
        // 如果有实例id和nodeUuid说明是从巡检风险列表页面跳转过来，打开节点风险抽屉
        const filter = [
          {
            attr: {
              key: 'InsId',
              name: '实例ID',
            },
            values: [
              {
                key: instanceId,
                name: instanceId,
              },
            ],
            _key: 0,
          },
        ];
        dispatch(changeNodeRiskDrawerState({
          riskTableFilter: filter,
          claimedTabelFilter: filter,
        }));
      }
    }
    if ([TabEnum.left, TabEnum.right].includes(progressTab)) {
      let riskTableFilter = [];
      if (claimPerson) {
        riskTableFilter = [
          {
            attr: {
              type: [
                'multiple',
                {
                  searchable: true,
                },
              ],
              key: 'ClaimPerson',
              name: '跟进人',
            },
            values: [
              {
                key: claimPerson,
                name: claimPerson,
              },
            ],
            _key: 0,
          },
        ];
        deleteUrlParam(claimPersonName);
      }
      dispatch(changeGovernanceProgressDrawerState({
        visible: true,
        progressTab,
        riskTableFilter,
      }));
      deleteUrlParam(progressTabName);
    }

    if (subjectTopicTitle) {
      dispatch(changeGovernanceProgressDrawerState({
        activeTabId: RiskProgressEnum.customTopic,
        secondaryDrawer: {
          visible: true,
          defaultId: undefined,
          defaultTopicTitle: subjectTopicTitle,
        },
      }));
      deleteUrlParam('subjectTopicTitle');
    }
  }, []);
  useEffect(() => {
    if (chatBiCallBacks) {
      const pluginParam = pluginAPI?.extraParams ?? '';
      if (pluginParam) {
        // 处理来自邮件的链接
        // xx/advisor?archId=xxx&plugin=cloud-inspection-sdk&pluginParam=from-inspection-email-1
        const [prefix, suffix] = splitFirstHyphen(pluginParam);
        // 来自巡检邮件自动打开风险分析抽屉
        if (prefix === UrlParamsEnum.FROM && suffix.includes(UrlParamsEnum.INSPECTION_EMAIL)) {
          reportEvent(pluginAPI.env as EnvEnum, {
            archId: pluginAPI.archInfo.archId,
            subUin: pluginAPI.uin,
            subUinName: pluginAPI.userName,
            eventType: 'clickRiskAiAnalyzeFromEmail',
            from: suffix.replace(`${UrlParamsEnum.INSPECTION_EMAIL}-`, ''),
          }, 'visitFromEmailPluginYeHeData');
          chatBiCallBacks?.setSelectNode?.(undefined);
          chatBiCallBacks?.changeChatVisibleFlag(true);
        }
      }
    }
  }, [chatBiCallBacks]);
  return null;
}
