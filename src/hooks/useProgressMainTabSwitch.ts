import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  changeGovernanceProgressDrawerState,
} from '@src/store/governance-progress-drawer/index';
import { changeGlobalData } from '@src/store/global';
import { RiskProgressEnum, GovernanceTabEvents } from '@src/constant/index';
import { chatBiCallBacks } from '@src/utils/caching';
import { TabEnum } from '@src/components/inspection-drawer';

export function useTabSwitch() {
  const dispatch = useDispatch();

  // 订阅切换tab事件
  useEffect(() => {
    const handleSwitchTab = () => {
      chatBiCallBacks?.changeChatVisibleFlag?.(false);
      dispatch(
        changeGovernanceProgressDrawerState({
          visible: true,
          activeTabId: RiskProgressEnum.customTopic,
          progressTab: TabEnum.left,
          customTopicTabHash: `${new Date().getTime()}`,
        }),
      );
      dispatch(changeGlobalData({
        nodeRiskDrawerVisible: false,
      }));
    };

    window.addEventListener(GovernanceTabEvents.SWITCH_GOVERNANCE_TAB, handleSwitchTab);

    return () => {
      window.removeEventListener(GovernanceTabEvents.SWITCH_GOVERNANCE_TAB, handleSwitchTab);
    };
  }, [dispatch]);

  return {};
}

export default useTabSwitch;
