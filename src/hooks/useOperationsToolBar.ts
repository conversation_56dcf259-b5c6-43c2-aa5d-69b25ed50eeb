import React, { useEffect, useRef } from 'react';
import Search from '@src/assets/svg/search.svg';
import Setting from '@src/assets/svg/setting.svg';
import ReportView from '@src/assets/svg/report-view.svg';
import StartSvg from '@src/assets/svg-component/start-app.svg';
import GovernanceProgressSvg from '@src/assets/svg-component/governance-progress.svg';
import { useDispatch } from 'react-redux';
import { useReportSelector } from '@src/store/report/index';
import { useGlobalSelector, changeGlobalData } from '@src/store/global/index';
import { changeInspectionsSettingsModalState } from '@src/store/inspection-settings-modal/index';
import { changeGovernanceProgressDrawerState } from '@src/store/governance-progress-drawer';
import {
  IDriftDetectionTaskEnum, InspectionTaskStatusEnum, EnvEnum,
} from '@src/constant';
import { stopArchScanHandle, stopArchSyncTaskHandle } from '@src/service/common-service/final-request';
import { changeRiskPanelData } from '@src/store/risk-search-panel';
import { setInspectionTaskIsStop, setInitTaskIsStop, initTaskIsStop } from '@src/utils/caching';
import { reportEvent, toUTCtime } from '@src/utils';
import s from '@src/components/global-report/index.module.scss';
import { useAppContext } from '@src/context/AppContext';
import { t } from '@tea/app/i18n';
import { notification, Button } from '@tencent/tea-component';
import useInspectionTask from './useInspectionTask';

interface IuseOperationsToolBarProps {
  pluginAPI: AppPluginAPI.PluginAPI;
  createArchScanReportFile: () => void;
  stopReportGeneration?: () => void;
  updateArchSyncTaskProgress: () => void;
  reportBtnConfigContentRef?: any
  openreportModal?: () => void;
}

/**
 * 插件工具栏hooks
 * @returns
 */
export default function useOperationsToolBar(props: IuseOperationsToolBarProps) {
  const {
    pluginAPI,
    createArchScanReportFile,
    reportBtnConfigContentRef,
    stopReportGeneration,
    openreportModal,
    updateArchSyncTaskProgress,
  } = props;

  const dispatch = useDispatch();
  const {
    inspectionTaskStatus,
    supportTaskProductList,
    inspectionTaskData,
    driftDetectionTaskStatus,
    driftDetectionTaskData,
    reportFinishTime,
    asyncTriggerScanTask,
  } = useGlobalSelector();
  const { reportBtnConfig, todayInspected } = useReportSelector();
  // eslint-disable-next-line max-len
  const { startArchScanTask, updateArchTaskProgress } = useInspectionTask({ pluginAPI, createArchScanReportFile, openreportModal });
  const { chatBiCallBacks } = useAppContext();
  const prevArchIdRef = useRef(pluginAPI.archInfo.archId);

  // 切换插件stop
  useEffect(
    () => () => {
      stopReportGeneration();
    },
    [],
  );

  // 切换架构图stop
  useEffect(() => {
    if (prevArchIdRef.current !== pluginAPI.archInfo.archId) {
      stopReportGeneration();
      prevArchIdRef.current = pluginAPI.archInfo.archId;
    }
  }, [pluginAPI.archInfo.archId]);

  const startArchScanTaskHandle = () => {
    dispatch(changeGlobalData({
      inspectionTaskStatus: InspectionTaskStatusEnum.inspectioning,
      inspectionTaskData: {
        IsFinish: false,
        FinishTime: '',
        NodeTaskStatusList: [],
        Progress: {
          ScannedCount: 0,
          TotalCount: 1,
        },
        LatestScanType: 1,
        CostTime: '',
        BindInsCount: 0,
      },
    }));
    startArchScanTask(supportTaskProductList, true, false);
  };

  useEffect(() => {
    if (asyncTriggerScanTask !== undefined) {
      startArchScanTaskHandle();
    }
  }, [asyncTriggerScanTask]);

  useEffect(() => {
    let createTaskiconNodeClassName = 'cloud-inspection-nostartd';
    if (inspectionTaskStatus === InspectionTaskStatusEnum.inspectioning
       || driftDetectionTaskStatus === IDriftDetectionTaskEnum.driftDetectioning) {
      createTaskiconNodeClassName = 'cloud-inspection-task-ing';
    }
    let createTaskHoverText = '开始巡检';
    if (inspectionTaskStatus === InspectionTaskStatusEnum.inspectioning) {
      createTaskHoverText = '巡检中';
    }
    if (driftDetectionTaskStatus === IDriftDetectionTaskEnum.driftDetectioning) {
      createTaskHoverText = '配置检测中';
    }
    const hasNodeNotFinish = inspectionTaskData?.NodeTaskStatusList?.filter((item) => !item.IsFinish);
    if (!supportTaskProductList) {
      return;
    }
    const operations = [
      {
        key: 'create-task',
        desc: createTaskHoverText,
        iconNode: React.createElement(StartSvg, {
          className: createTaskiconNodeClassName,
        }),
        disabled: inspectionTaskStatus === InspectionTaskStatusEnum.inspectioning
        || driftDetectionTaskStatus !== IDriftDetectionTaskEnum.driftDetectioned
        || hasNodeNotFinish?.length !== 0,
        hoverText: createTaskHoverText,
        onClick: () => {
          startArchScanTaskHandle();
        },
      },
      {
        key: 'report',
        desc: '生成报告',
        icon: ReportView,
        onClick: () => {
          openreportModal();
        },
        disabled: !reportFinishTime || driftDetectionTaskStatus !== IDriftDetectionTaskEnum.driftDetectioned,
        hoverTextExtraProps: {
          key: `${+new Date()}`,
          overlayClassName: s['report-scan-wrap-overlay'],
          content: !inspectionTaskData?.FinishTime ? (
            t('您需要完成巡检之后才能查看风险，请点击“开始巡检')
          ) : `当前报告为
              ${toUTCtime(new Date(inspectionTaskData?.FinishTime).getTime() / 1000)} 
              ${todayInspected === 0 ? t('自动巡检') : t('手动巡检')}  
              生成的巡检报告。`,
          overlayStyle: {
            zIndex: 9,
          },
        },
      },
      {
        key: 'risk-search',
        desc: '风险查找',
        icon: Search,
        onClick: () => {
          reportEvent(pluginAPI.env as EnvEnum, {
            archId: pluginAPI.archInfo.archId,
            // nodeId: '',
            // nodeName: '',
            subUin: pluginAPI.uin,
            subUinName: pluginAPI.userName,
            eventType: 'clickFindRisk', // 点击风险查找
          });
          dispatch(changeRiskPanelData({
            visible: true,
          }));
          chatBiCallBacks?.changeChatVisibleFlag(false);
        },
      },
      {
        key: 'governance-progress',
        desc: '治理进展',
        iconNode: React.createElement(GovernanceProgressSvg),
        onClick: () => {
          dispatch(changeRiskPanelData({
            visible: false,
          }));
          dispatch(changeGovernanceProgressDrawerState({
            visible: true,
          }));
          chatBiCallBacks?.changeChatVisibleFlag(false);
          chatBiCallBacks?.setSelectNode?.(undefined);
        },
      },
    ];
    if (pluginAPI.env === 'CONSOLE') {
      operations.splice(2, 0, {
        key: 'settings',
        desc: '巡检设置',
        icon: Setting,
        onClick: () => {
          dispatch(changeInspectionsSettingsModalState({
            inspectionSettingsModalVisible: true,
            products: [],
          }));
          chatBiCallBacks?.changeChatVisibleFlag(false);
          dispatch(changeRiskPanelData({ visible: false }));
        },
      });
    }
    pluginAPI.setOperations(operations);
    pluginAPI.showTools();
  }, [
    inspectionTaskStatus,
    supportTaskProductList,
    reportBtnConfig,
    reportBtnConfigContentRef.current,
    driftDetectionTaskStatus,
    reportFinishTime,
    inspectionTaskData,
  ]);

  useEffect(() => {
    if (inspectionTaskStatus !== InspectionTaskStatusEnum.notStarted) {
      if (inspectionTaskStatus === InspectionTaskStatusEnum.inspectioning
        && driftDetectionTaskStatus !== IDriftDetectionTaskEnum.driftDetectioning) {
        const progress = (inspectionTaskData.Progress.ScannedCount / inspectionTaskData.Progress.TotalCount) * 100;
        pluginAPI.setProgressParams(progress, '巡检中', {
          showStopButton: true,
          hoverText: inspectionTaskData.BindInsCount > 100 ? '架构图绑定实例数超100个，将耗时稍长，可先访问其他应用' : '',
          onStop: () => {
            setInspectionTaskIsStop(true);
            pluginAPI.setAsyncTaskStop();
            dispatch(changeGlobalData({
              inspectionTaskStatus: InspectionTaskStatusEnum.notStarted,
            }));
            stopArchScanHandle({
              env: pluginAPI.env,
              apiParams: {
                data: {
                  ArchId: pluginAPI.archInfo.archId,
                },
              },
              uin: pluginAPI.uin,
            }).then((rs) => {
              if (rs) {
                updateArchTaskProgress(supportTaskProductList, (rs: any) => {
                  notification.warning({
                    title: '任务中断',
                    description: (
                      React.createElement(
                        'span',
                        { className: 'inspection-task-stop' },
                        `巡检任务中断，当前风险为${rs?.FinishTime}巡检结果，可`,
                        React.createElement(Button, {
                          type: 'link',
                          className: 'inspection-task-start-button',
                          onClick: () => {
                            startArchScanTaskHandle();
                          },
                        }, '重新发起巡检'),
                        React.createElement(
                          'span',
                          {},
                          '以更新风险信息',
                        ),
                      )
                    ),
                    duration: 0,
                  });
                });
              }
            });
          },
        });
      }
      if (inspectionTaskStatus === InspectionTaskStatusEnum.inspectionCompleted
         && driftDetectionTaskStatus !== IDriftDetectionTaskEnum.driftDetectioning) {
        // 巡检完成
        pluginAPI.setAsyncTaskStop();
        return;
      }
    }
    if ((driftDetectionTaskStatus === IDriftDetectionTaskEnum.driftDetectioning
      || driftDetectionTaskStatus === IDriftDetectionTaskEnum.driftDetectioned) && !initTaskIsStop
      && inspectionTaskStatus === InspectionTaskStatusEnum.inspectionCompleted) {
      pluginAPI.setProgressParams(driftDetectionTaskData?.TaskProgress || 0, '配置检测中', {
        showStopButton: true,
        onStop: () => {
          setInitTaskIsStop(true);
          pluginAPI.setAsyncTaskStop();
          dispatch(changeGlobalData({
            driftDetectionTaskStatus: IDriftDetectionTaskEnum.notStarted,
          }));
          stopArchSyncTaskHandle({
            env: pluginAPI.env,
            apiParams: {
              data: {
                MapId: pluginAPI.archInfo.archId,
              },
            },
            uin: pluginAPI.uin,
          }).then((rs) => {
            if (rs) {
              updateArchSyncTaskProgress();
            }
          });
        },
      });
    }
    if (driftDetectionTaskStatus === IDriftDetectionTaskEnum.driftDetectioned
      && inspectionTaskStatus !== InspectionTaskStatusEnum.inspectioning) {
      // 漂移检测完成
      setTimeout(() => {
        pluginAPI.setAsyncTaskStop();
      }, 1000);
    }
  }, [inspectionTaskStatus, inspectionTaskData, driftDetectionTaskStatus, driftDetectionTaskData]);
  return null;
}
