import { useGlobalSelector } from '@src/store/global/index';
import { changeNodeRiskDrawerState } from '@src/store/node-risk-drawer/index';
import { useDispatch } from 'react-redux';
import {
  describeRiskStatisticInNodeHandle,
} from '@src/service/node-risk-drawer/final-request';

/**
 * 更新节点风险数量
 */
export default function useUpdateNodeRiskCount() {
  const { pluginPropsData, selectNodeInfo, archScanRiskInfo } = useGlobalSelector();
  const dispatch = useDispatch();

  const updateNodeRiskCount = async () => {
    const riskItem = archScanRiskInfo?.NodeRiskItems?.find((item) => item.NodeUuid === selectNodeInfo.key);
    const riskNodeResult = await describeRiskStatisticInNodeHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          MapId: pluginPropsData.archInfo.archId,
          NodeUuid: selectNodeInfo.key,
          TaskId: riskItem?.LastSuccessTaskId,
        },
      },
      uin: pluginPropsData.uin,
    });
    if (riskNodeResult) {
      // 存储公共store, useUpdateNodeRiskCount hook会监听依赖进行角标和顶栏更新操作
      dispatch(changeNodeRiskDrawerState({
        riskNodeCount: riskNodeResult,
      }));
    }
  };

  return {
    updateNodeRiskCount,
  };
}
