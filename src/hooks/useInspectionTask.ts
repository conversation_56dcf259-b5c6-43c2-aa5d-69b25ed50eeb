import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { changeGlobalData, useGlobalSelector } from '@src/store/global/index';
import { InspectionTaskStatusEnum, IDriftDetectionTaskEnum } from '@src/constant';
import {
  createArchScanTaskHandle,
  fetchArchTaskProgress,
} from '@src/service/common-service/final-request';
import { notification, Button } from '@tencent/tea-component';
import { inspectionTaskIsStop, setInspectionTaskIsStop } from '@src/utils/caching';
import _ from 'lodash';
import moment from 'moment';
import useUpdateShapesBar from './useUpdateShapesBar';

interface IuseInspectionTaskProps {
  pluginAPI: AppPluginAPI.PluginAPI;
  createArchScanReportFile?: () => void;
  openreportModal?: () => void;
}

let canPolling = false; // 闭包变量。用于阻止切换插件场景中。轮询会继续执行的问题
export default function useInspectionTask(props: IuseInspectionTaskProps) {
  const {
    pluginAPI,
    createArchScanReportFile,
    openreportModal = null,
  } = props;
  const dispatch = useDispatch();
  const { inspectionTaskStatus } = useGlobalSelector();
  const { updateShapesBar } = useUpdateShapesBar();
  // 防抖包裹后的核心逻辑
  const getArchTaskProgress = _.debounce((supportTaskProductList: string[], isInitSub = false) => {
    const { nodeList, archId } = pluginAPI.archInfo;
    const filterNodeList = nodeList.filter((item) => supportTaskProductList.includes(item.ProductType));

    fetchArchTaskProgress({
      env: pluginAPI.env,
      data: {
        Filters: [
          { Name: 'mapId', Values: [archId] },
          { Name: 'nodeUuid', Values: filterNodeList.map((item) => item.DiagramId) },
        ],
      },
      uin: pluginAPI.uin,
    }).then((rs) => {
      if (rs && !rs.IsFinish && !inspectionTaskIsStop) {
        dispatch(changeGlobalData({
          inspectionTaskData: rs,
          inspectionTaskStatus: InspectionTaskStatusEnum.inspectioning,
          reportFinishTime: rs.FinishTime,
        }));
        if (canPolling && inspectionTaskStatus) {
          getArchTaskProgress(supportTaskProductList, isInitSub); // 递归调用依然受防抖控制
        }
      } else if (rs && rs.IsFinish && !inspectionTaskIsStop) {
        // 巡检任务完成 调用巡检结果新接口
        dispatch(changeGlobalData({
          inspectionTaskStatus: InspectionTaskStatusEnum.inspectionCompleted,
          inspectionTaskData: rs,
          isInitTask: isInitSub, // 当前巡检任务是不是初始化任务。
          reportFinishTime: rs.FinishTime,
        }));
        if (!isInitSub) {
          // 再次调用角标顶栏接口更新数据
          updateShapesBar();
          if (!inspectionTaskIsStop) {
            notification.success({
              title: '巡检完成',
              description: (
                React.createElement(
                  'span',
                  { className: 'inspection-task-completed' },
                  '已完成对业务架构的巡检，您可以点击节点图标查看风险详情，或者',
                  React.createElement(Button, {
                    type: 'link',
                    className: 'inspection-task-view-report-button',
                    onClick: () => {
                      if (openreportModal) {
                        openreportModal();
                      } else {
                        createArchScanReportFile();
                      }
                    },
                  }, '查看报告'),
                )
              ),
              duration: 0,
            });
          }
        }
      }
    });
  }, 2000);

  const updateArchTaskProgress = (supportTaskProductList, cb) => {
    const { nodeList, archId } = pluginAPI.archInfo;
    const filterNodeList = nodeList.filter((item) => supportTaskProductList.includes(item.ProductType));
    fetchArchTaskProgress({
      env: pluginAPI.env,
      data: {
        Filters: [
          { Name: 'mapId', Values: [archId] },
          { Name: 'nodeUuid', Values: filterNodeList.map((item) => item.DiagramId) },
        ],
      },
      uin: pluginAPI.uin,
    // eslint-disable-next-line consistent-return
    }).then((rs) => {
      if (rs) {
        if (rs?.IsFinish) {
          // 巡检任务完成 调用巡检结果新接口
          dispatch(changeGlobalData({
            inspectionTaskStatus: InspectionTaskStatusEnum.inspectionCompleted,
            driftDetectionTaskStatus: IDriftDetectionTaskEnum.driftDetectioned,
            inspectionTaskData: rs,
            isInitTask: false,
            reportFinishTime: rs.FinishTime,
          }));
        }
        cb?.(rs);
      }
    });
  };

  const startArchScanTask = async (supportTaskProductList, isSynced = true, isInitSub = false) => {
    try {
      setInspectionTaskIsStop(false);
      if (isSynced) {
        // 架构图已同步
        // 开始巡检前，先检测当前是否有巡检任务在执行
        const { nodeList, archId } = pluginAPI.archInfo;
        const filterNodeList = nodeList.filter((item) => supportTaskProductList.includes(item.ProductType));
        const progressResult = await fetchArchTaskProgress({
          env: pluginAPI.env,
          data: {
            Filters: [
              { Name: 'mapId', Values: [archId] },
              { Name: 'nodeUuid', Values: filterNodeList.map((item) => item.DiagramId) },
            ],
          },
          uin: pluginAPI.uin,
        });
        if (progressResult && progressResult.IsFinish) {
          const createArchScanTask = async () => {
            // 当前无巡检任务则创建新的巡检任务
            const createRs = await createArchScanTaskHandle({
              env: pluginAPI.env,
              apiParams: {
                data: {
                  IsCusScan: 1, // 0 自动巡检 1 手动巡检
                  MapId: pluginAPI.archInfo.archId,
                  UserName: pluginAPI.userName,
                },
              },
              uin: pluginAPI.uin,
            });
            if (createRs) {
              // 开始轮询巡检任务
              canPolling = true;
              getArchTaskProgress(supportTaskProductList, isInitSub);
            }
          };
          if (isInitSub) {
            try {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              const { CurrentTime, TimeGap, FinishTime } = progressResult;
              const currentTime = moment(CurrentTime);
              const finishTime = moment(FinishTime);
              console.log(CurrentTime, 'CurrentTime');
              console.log(FinishTime, 'FinishTime');
              console.log(TimeGap, 'TimeGap');
              if (currentTime.unix() - finishTime.unix() > TimeGap) {
                console.log('符合自动发起巡检条件创建巡检任务');
                // 如果是初始化加载流程，需要判断当前巡检完成的时间有没有超过TimeGap(s)时间段。如果超过了立即发起一起巡检，如果没超过则直接认为巡检完成。不进行巡检动作
                createArchScanTask();
              } else {
                console.log('不符合自动发起巡检条件，发起配置检测流程');
                // 如果是初始化加载流程则不需要重新发起巡检。
                dispatch(changeGlobalData({
                  inspectionTaskStatus: InspectionTaskStatusEnum.inspectionCompleted,
                  isInitTask: true,
                  inspectionTaskData: progressResult,
                  reportFinishTime: progressResult.FinishTime,
                }));
              }
            } catch (error) {
              console.error(error);
            }
          } else {
            // 当前无巡检任务则创建新的巡检任务
            createArchScanTask();
          }
        } else {
          // 当前巡检任务未完成，则直接开始轮询当前巡检任务
          // 开始轮询巡检任务
          canPolling = true;
          getArchTaskProgress(supportTaskProductList, isInitSub);
        }
      } else {
        // 架构图未同步则直接先发起巡检
        const createRs = await createArchScanTaskHandle({
          env: pluginAPI.env,
          apiParams: {
            data: {
              IsCusScan: 1, // 0 自动巡检 1 手动巡检
              MapId: pluginAPI.archInfo.archId,
              UserName: pluginAPI.userName,
            },
          },
          uin: pluginAPI.uin,
        });
        if (createRs) {
          // 开始轮询巡检任务
          canPolling = true;
          getArchTaskProgress(supportTaskProductList, isInitSub);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => () => {
    canPolling = false;
  }, []);

  return {
    startArchScanTask,
    updateArchTaskProgress,
  };
}
