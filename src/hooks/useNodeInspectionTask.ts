import { useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { changeGlobalData, useGlobalSelector } from '@src/store/global/index';
import {
  createArchScanTaskHandle,
  fetchArchTaskProgress,
} from '@src/service/common-service/final-request';
import {
  NodeInspectionTaskStatusEnum,
} from '@src/constant';
import _ from 'lodash';
import useUpdateShapesBar from './useUpdateShapesBar';

interface IuseInspectionTaskProps {
  pluginAPI: any;
}

let canPolling = false; // 闭包变量。用于阻止切换插件场景中。轮询会继续执行的问题
export default function useNodeInspectionTask(props: IuseInspectionTaskProps) {
  const {
    pluginAPI,
  } = props;
  const { inspectionTaskData } = useGlobalSelector();
  const allNodeFinish = useRef(true); // 记录节点是否全部完成巡检的状态标识
  const dispatch = useDispatch();
  const { updateShapesBar } = useUpdateShapesBar();

  // 防抖包裹后的核心逻辑
  const getNodeTaskProgress = _.debounce((supportTaskProductList: string[]) => {
    const { nodeList, archId } = pluginAPI.archInfo;
    const filterNodeList = nodeList?.filter((item) => supportTaskProductList?.includes(item.ProductType));

    fetchArchTaskProgress({
      env: pluginAPI.env,
      data: {
        Filters: [
          { Name: 'mapId', Values: [archId] },
          { Name: 'nodeUuid', Values: filterNodeList.map((item) => item.DiagramId) },
        ],
      },
      uin: pluginAPI.uin,
    }).then((rs) => {
      if (rs) {
        const isUnFinish = rs?.NodeTaskStatusList?.filter((item) => !item.IsFinish); // 检索节点是否全部结束
        if (isUnFinish?.length === 0) {
          // 节点全部结束
          allNodeFinish.current = true;
          canPolling = false;
          dispatch(changeGlobalData({
            inspectionTaskData: rs,
            nodeInspectionTaskStatus: NodeInspectionTaskStatusEnum.inspectionCompleted,
          }));
          // 再次调用角标顶栏接口更新数据
          updateShapesBar();
        } else {
          // 有节点未完成巡检，继续轮询
          dispatch(changeGlobalData({
            inspectionTaskData: rs,
          }));
          // eslint-disable-next-line no-lonely-if
          if (canPolling && !allNodeFinish.current) {
            getNodeTaskProgress(supportTaskProductList); // 递归调用依然受防抖控制
          }
        }
      }
    });
  }, 2000);

  const startNodeScanTask = async (supportTaskProductList, nodeUuid) => {
    try {
      // 开始节点巡检先更新本地的节点node数据
      const inspectionTaskDataClone = _.cloneDeep(inspectionTaskData);
      const nodeItem = inspectionTaskDataClone?.NodeTaskStatusList?.find((item) => item.NodeUuid === nodeUuid);
      if (nodeItem) {
        nodeItem.IsFinish = false;
        dispatch(changeGlobalData({
          inspectionTaskData: inspectionTaskDataClone,
          nodeInspectionTaskStatus: NodeInspectionTaskStatusEnum.notStarted,
        }));
        // 开始巡检前，先检测当前节点是否有巡检任务在执行
        const { nodeList, archId } = pluginAPI.archInfo;
        const filterNodeList = nodeList?.filter((item) => supportTaskProductList?.includes(item.ProductType));
        const progressResult = await fetchArchTaskProgress({
          env: pluginAPI.env,
          data: {
            Filters: [
              { Name: 'mapId', Values: [archId] },
              { Name: 'nodeUuid', Values: filterNodeList.map((item) => item.DiagramId) },
            ],
          },
          uin: pluginAPI.uin,
        });
        if (progressResult && progressResult?.IsFinish) {
          // 巡检任务完成查看节点任务状态
          const nodeItem = progressResult?.NodeTaskStatusList?.find((item) => item.NodeUuid === nodeUuid);
          if (nodeItem?.IsFinish) {
            // 节点任务完成,创建新的节点巡检任务
            const createRs = await createArchScanTaskHandle({
              env: pluginAPI.env,
              apiParams: {
                data: {
                  IsCusScan: 1, // 0 自动巡检 1 手动巡检
                  MapId: pluginAPI.archInfo.archId,
                  UserName: pluginAPI.userName,
                  NodeIdList: [nodeUuid],
                },
              },
              uin: pluginAPI.uin,
            });
            if (createRs) {
              // 开始轮询巡检任务
              if (allNodeFinish.current) {
                // 当前没有一个节点开始任务
                allNodeFinish.current = false;
                canPolling = true;
                dispatch(changeGlobalData({
                  nodeInspectionTaskStatus: NodeInspectionTaskStatusEnum.inspectioning,
                }));
                getNodeTaskProgress(supportTaskProductList);
              }
            }
          } else if (allNodeFinish.current) {
            allNodeFinish.current = false;
            canPolling = true;
            dispatch(changeGlobalData({
              nodeInspectionTaskStatus: NodeInspectionTaskStatusEnum.inspectioning,
            }));
            // 节点巡检任务未完成继续巡检
            getNodeTaskProgress(supportTaskProductList);
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 初始化插件后在漂移检测完成后还要看一下节点巡检是否完成
  const initNodeScanTask = async (supportTaskProductList) => {
    try {
      const isUnFinish = inspectionTaskData?.NodeTaskStatusList?.filter((item) => !item.IsFinish); // 检索节点是否全部结束
      if (isUnFinish?.length) {
        // 若还存在节点任务没有完成需要继续轮询节点任务直到完成
        if (allNodeFinish.current) {
          allNodeFinish.current = false;
          canPolling = true;
          dispatch(changeGlobalData({
            nodeInspectionTaskStatus: NodeInspectionTaskStatusEnum.inspectioning,
          }));
          // 节点巡检任务未完成继续巡检
          getNodeTaskProgress(supportTaskProductList);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => () => {
    canPolling = false;
  }, []);

  return {
    startNodeScanTask,
    initNodeScanTask,
  };
}
