import { useEffect } from 'react';
import { use } from '@tencent/tea-sdk-runner';
import { EnvEnum } from '@src/constant/index';
import { ActionEnum, useAppContext } from '@src/context/AppContext';
import { setChatBiCallBacks } from '@src/utils/caching';

interface IUseChatBiInitProps {
  pluginAPI: AppPluginAPI.PluginAPI
}

export default function useChatBiInit(props: IUseChatBiInitProps) {
  const {
    pluginAPI,
  } = props;

  const { dispatch } = useAppContext();

  const loadChatBiSdk = async () => {
    let sdkCallbacks = null;
    let sdk = null;
    if (pluginAPI.env === EnvEnum.CONSOLE) {
      sdk = await use('ai-bi-for-inspection-sdk');
    } else {
      sdk = await pluginAPI.use('ai-bi-for-inspection-sdk');
    }
    sdkCallbacks = sdk?.init({
      ...pluginAPI,
      defaultMode: 'insight',
      useExportComponents: true,
      showModeSwitch: false,
      chatOnBoardingData: {
        subTitle: '我是云巡检Agent',
        description: '在这里我可以帮你分析左侧架构图的巡检风险情况与治理优先级',
        quickList: [
          {
            title: '常见问题:',
            description: '分析当前架构图的风险情况',
          },
          {
            title: '常见问题:',
            description: '分析当前架构风险的治理优先级',
          },
        ],
      },
    });
    if (sdkCallbacks) {
      setChatBiCallBacks(sdkCallbacks);
      dispatch?.({
        type: ActionEnum.SET_CHAT_BI_CALLBACKS,
        value: sdkCallbacks,
      });
    }
  };

  useEffect(() => {
    loadChatBiSdk();
  }, []);

  return null;
}
