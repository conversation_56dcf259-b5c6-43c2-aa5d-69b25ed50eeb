import { useMemo } from 'react';
import { debounce } from 'lodash';
import { useDispatch } from 'react-redux';
import { app } from '@tea/app';
import {
  createArchScanReportFile as createArchScanReport,
  fetchArchScanReportTaskStatus,
  describeDownloadTask as downloadTask,
} from '@src/service/export-service/final-request';
import { t } from '@tea/app/i18n';
import { reportVersion } from '@src/constant';
import { changeReportData } from '@src/store/report/index';

let downloadTimer = null;
let scanReportTimeTimer = null;
(window as any).initScanReportTime = null;
const isLeave = false;

interface IuseInitProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

/**
 * 插件报告相关hook
 * @returns
 */
export default function useInit(props: IuseInitProps) {
  const {
    pluginAPI,
  } = props;
  const dispatch = useDispatch();
  const createArchScanReportFile = useMemo(() => debounce(async () => {
    if (pluginAPI.env === 'ISA') {
      (document as any).querySelector('.inpected-lay-wrap  .tea-notification__close .tea-btn')?.click();
    } else {
      (document as any).querySelector('.inpected-lay-wrap .sdk-menus-btn')?.click();
    }
    dispatch(changeReportData({
      downloadInfo: {},
      generating: true,
      scanFail: false,
    }));
    try {
      dispatch(changeReportData({
        scanReportBubbleVisible: true,
      }));
      const res = await createArchScanReport({
        env: pluginAPI.env,
        uin: pluginAPI.uin,
        data: {
          CloudMapUuid: pluginAPI.archInfo.archId,
          TaskType: 'mapTaskType',
          ReportVersion: reportVersion,
          SdkName: 'inspect-report-sdk',
        },
      });
      if (!res) {
        return;
      }
      describeDownloadTask(res.ResultId);
      dispatch(changeReportData({
        resultId: res.ResultId,
        scanReportBubbleVisible: true,
      }));
    } catch (err) {
      dispatch(changeReportData({
        generating: false,
      }));
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  }, 300), []);
  const describeDownload = async (resultId) => {
    try {
      const res = await downloadTask({
        env: pluginAPI.env,
        uin: pluginAPI.uin,
        data: {
          ResultId: resultId,
        },
      });
      if (!res) {
        dispatch(changeReportData({
          generating: false,
        }));
        resetScanReportInfo();
        return;
      }
      dispatch(changeReportData({
        downloadInfo: res,
      }));
    } catch (err) {
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  };

  const resetScanReportInfo = () => {
    clearInterval(scanReportTimeTimer);
    scanReportTimeTimer = null;
    // 清除下载任务轮询定时器
    if (downloadTimer) {
      clearTimeout(downloadTimer);
      downloadTimer = null;
    }
    dispatch(changeReportData({
      scanReportTime: null,
    }));
    // initScanReportTime = null;
    (window as any).initScanReportTime = null;
  };

  const stopReportGeneration = () => {
    // 停止报告生成任务
    resetScanReportInfo();
    dispatch(changeReportData({
      generating: false,
      scanReportBubbleVisible: false,
    }));
  };

  const describeDownloadTask = async (resultId) => {
    try {
      const res = await fetchArchScanReportTaskStatus({
        env: pluginAPI.env,
        uin: pluginAPI.uin,
        data: {
          ResultId: resultId,
        },
      });
      if (!res) {
        dispatch(changeReportData({
          generating: false,
        }));
        resetScanReportInfo();
        return;
      }
      dispatch(changeReportData({
        progressNum: res?.Progress ?? 0,
      }));
      if (res.TaskStatus === 'running' || res.TaskStatus === 'init') {
        const arr = res.CostTime.split(':');
        if ((window as any).initScanReportTime === null && !isLeave) {
          const time = (arr.length === 3 ? res.CostTime : `00:${res.CostTime}`);
          // initScanReportTime = time;
          (window as any).initScanReportTime = time;
          dispatch(changeReportData({
            scanReportTime: time,
          }));
        }
        if (arr?.length > 1 && parseInt(arr[arr.length - 2], 10) > 3) {
          dispatch(changeReportData({
            scanFail: true,
            generating: false,
            downloadInfo: res,
            scanReportBubbleVisible: true,
          }));
          resetScanReportInfo();
          return;
        }
        downloadTimer = setTimeout(() => {
          if (downloadTimer === null) {
            clearTimeout(downloadTimer);
            return;
          }
          describeDownloadTask(resultId);
        }, 2000);
      } else if (res.TaskStatus === 'success') {
        if (pluginAPI.env === 'ISA') {
          describeDownload(resultId);
        } else {
          dispatch(changeReportData({
            downloadInfo: res,
          }));
        }
        dispatch(changeReportData({
          generating: false,
          scanReportBubbleVisible: true,
          isFinished: false,
        }));
        resetScanReportInfo();
      }
    } catch (err) {
      resetScanReportInfo();
      dispatch(changeReportData({
        generating: false,
      }));
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  };

  return { createArchScanReportFile, stopReportGeneration } as any;
}
