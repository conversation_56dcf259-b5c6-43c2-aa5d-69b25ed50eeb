import React, { useEffect } from 'react';
import { useGlobalSelector, changeGlobalData } from '@src/store/global/index';
import RiskNumberPanel from '@src/components/risk-number-pannel';
import { useDispatch } from 'react-redux';
import { useAppContext } from '@src/context/AppContext';
import { useRiskPanelSelector } from '@src/store/risk-search-panel';

interface IuseCreateSmartIslandProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

export default function useCreateSmartIsland(props: IuseCreateSmartIslandProps) {
  const {
    pluginAPI,
  } = props;
  const dispatch = useDispatch();
  const { chatBiCallBacks } = useAppContext();
  const {
    archScanRiskInfo, riskFilterType, pluginPropsData, riskType,
  } = useGlobalSelector();
  const { searchBoxValue } = useRiskPanelSelector();

  useEffect(() => {
    if (archScanRiskInfo && riskFilterType && pluginPropsData && pluginAPI && chatBiCallBacks) {
      pluginAPI?.setSlotComponentV2(React.createElement(RiskNumberPanel, {
        archScanRiskInfo,
        riskFilterType,
        pluginPropsData,
        riskType,
        searchBoxValue,
        dispatch,
        changeGlobalData,
        chatBiCallBacks,
      }));
    }
  }, [archScanRiskInfo, riskFilterType, pluginPropsData, pluginAPI, chatBiCallBacks, riskType, searchBoxValue]);

  return null;
}
