import { useRef } from 'react';
import { useDispatch } from 'react-redux';
import { changeGlobalData } from '@src/store/global/index';
import { describeArchNodeConfigInfoHandle } from '@src/service';
import { describeStrategiesHandle } from '@src/service/node-risk-drawer/final-request';

interface IuseInitFetchDataProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

export default function useInitFetchData(props: IuseInitFetchDataProps) {
  const {
    pluginAPI,
  } = props;

  const isFisrtLoad = useRef(true);
  const dispatch = useDispatch();

  if (isFisrtLoad.current) {
    isFisrtLoad.current = false;
    describeArchNodeConfigInfoHandle({
      env: pluginAPI.env,
    }).then((rs) => {
      dispatch(changeGlobalData({
        supportTaskProductList: rs,
      }));
    });
    describeStrategiesHandle({
      env: pluginAPI.env,
      apiParams: {
        data: {
          StrategyFilters: [
            {
              Name: 'mapId',
              Values: [pluginAPI.archInfo.archId],
            },
          ],
        },
      },
      uin: pluginAPI.env,
    }).then((rs) => {
      if (rs) {
        dispatch(changeGlobalData({
          strategies: rs.Strategies,
        }));
      }
    });
  }
}
