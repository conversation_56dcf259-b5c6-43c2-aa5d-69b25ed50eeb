import React from 'react';
import { Provider } from 'react-redux';
import { store } from '@src/store';
import { setPluginAPI } from '@src/utils/caching';
import AppProvider from '@src/context/AppContext';
import CloudInspection from './pages/cloud-inspection';

interface IRootProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

/**
 * 云巡检插件入口组件
 * @returns
 */
export default function Root(props: IRootProps): React.ReactElement {
  const {
    pluginAPI,
  } = props;
  setPluginAPI(pluginAPI);
  return (
    <Provider store={store}>
      <AppProvider>
        <CloudInspection pluginAPI={pluginAPI} />
      </AppProvider>
    </Provider>
  );
}
